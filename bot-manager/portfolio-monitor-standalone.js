#!/usr/bin/env node

/**
 * Standalone Portfolio Monitor Service with Bot Manager Integration
 * 
 * This service integrates with the main bot manager to track bot creation times
 * and implement proper grace periods for newly provisioned bots.
 */

const { PortfolioMonitorService } = require('./portfolio-monitor-service');
const mainBotManager = require('./index');

class IntegratedPortfolioMonitor extends PortfolioMonitorService {
    async start() {
        console.log('[IntegratedPortfolioMonitor] Starting integrated portfolio monitoring service...');

        // Register this service with the main bot manager for creation tracking
        if (mainBotManager && mainBotManager.setPortfolioMonitor) {
            mainBotManager.setPortfolioMonitor(this);
            console.log('[IntegratedPortfolioMonitor] ✅ Registered with main bot manager');
        } else {
            console.warn('[IntegratedPortfolioMonitor] ⚠️  Could not register with main bot manager');
        }

        // Call parent start method
        return super.start();
    }
}

// Main execution
async function main() {
    console.log('🚀 Starting Integrated Portfolio Monitor Service\n');

    const monitor = new IntegratedPortfolioMonitor();

    // Handle graceful shutdown
    process.on('SIGTERM', async () => {
        console.log('\n📧 SIGTERM received, shutting down gracefully...');
        await monitor.stop();
        process.exit(0);
    });

    process.on('SIGINT', async () => {
        console.log('\n📧 SIGINT received, shutting down gracefully...');
        await monitor.stop();
        process.exit(0);
    });

    // Start the service
    await monitor.start();

    // Keep the process running with enhanced status reporting
    setInterval(() => {
        const stats = monitor.getStats();
        const uptime = ((Date.now() - stats.startTime) / 1000 / 60).toFixed(1);

        console.log(`[PortfolioMonitor] Status: ${stats.isRunning ? 'RUNNING' : 'STOPPED'} | Users: ${stats.totalUsers} | Uptime: ${uptime}min | Bot Creation Tracking: ${monitor.botCreationTimes.size} bots`);

        // Log individual user stats if verbose logging is enabled
        if (process.env.VERBOSE_LOGGING === 'true') {
            Object.entries(stats.users).forEach(([userId, userStats]) => {
                console.log(`  └─ User ${userId}: ${userStats.activeBots}/${userStats.totalBots} bots active, ${userStats.totalSnapshots} snapshots`);
            });
        }
    }, 300000); // Status update every 5 minutes
}

// Run if this file is executed directly
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Integrated Portfolio Monitor Service failed:', error);
        process.exit(1);
    });
}

module.exports = { IntegratedPortfolioMonitor };

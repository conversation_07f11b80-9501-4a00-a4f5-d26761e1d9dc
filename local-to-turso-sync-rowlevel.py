#!/usr/bin/env python3
"""
Ultra-Optimized Local-First Database Sync with Turso
Row-level change detection to minimize Turso operations
"""

import os
import sys
import json
import sqlite3
import time
import subprocess
import argparse
import hashlib
from pathlib import Path

def log(message):
    """Log with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")
    sys.stdout.flush()

def run_turso_command(cmd_args, dry_run=False):
    """Run turso CLI command and return output"""
    try:
        turso_cmd = os.environ.get('TURSO_CMD', 'turso')
        full_cmd = [turso_cmd] + cmd_args
        
        if dry_run:
            log(f"DRY RUN: Would execute: {' '.join(full_cmd)}")
            return "dry-run-output"
        
        log(f"Executing: {' '.join(full_cmd)}")
        result = subprocess.run(full_cmd, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        log(f"Turso command failed: {' '.join(cmd_args)}")
        log(f"Error: {e.stderr}")
        raise

def get_row_hash(row_data):
    """Get hash of a single row for change detection"""
    data_str = str(row_data)
    return hashlib.md5(data_str.encode()).hexdigest()

def get_table_structure(cursor, table):
    """Get table structure for creating remote table"""
    cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
    result = cursor.fetchone()
    if result:
        create_sql = result[0]
        if create_sql.upper().startswith('CREATE TABLE'):
            create_sql = create_sql.replace('CREATE TABLE', 'CREATE TABLE IF NOT EXISTS', 1)
        return create_sql
    return None

def ensure_turso_database(instance_id, user_id, turso_org, turso_region="us-east-1", dry_run=False):
    """Ensure Turso database exists for the bot instance"""
    db_name = f"bot-{user_id}-{instance_id}".lower().replace('_', '-')
    
    try:
        dbs_output = run_turso_command(['db', 'list'], dry_run)
        if not dry_run and db_name in dbs_output:
            log(f"Turso database '{db_name}' already exists")
        elif not dry_run:
            log(f"Creating Turso database '{db_name}' in region '{turso_region}'...")
            run_turso_command(['db', 'create', db_name, '--location', turso_region, '--wait'], dry_run)
            log(f"✓ Turso database '{db_name}' created successfully")
        
        db_url = get_turso_url(db_name, dry_run)
        return db_name, db_url
        
    except Exception as e:
        log(f"Failed to ensure Turso database: {e}")
        raise

def get_turso_url(db_name, dry_run=False):
    """Get Turso database URL"""
    try:
        url = run_turso_command(['db', 'show', db_name, '--url'], dry_run)
        if not dry_run:
            log(f"Retrieved Turso URL for '{db_name}'")
        return url
    except Exception as e:
        log(f"Failed to get Turso URL: {e}")
        raise

def sync_local_to_turso_rowlevel(local_db_path, turso_db_name, dry_run=False):
    """Ultra-optimized row-level sync that only changes what's different"""
    try:
        log(f"Starting row-level optimized sync from {local_db_path} to Turso database '{turso_db_name}'")
        
        if not os.path.exists(local_db_path):
            log(f"Local database not found: {local_db_path}")
            return False
        
        local_conn = sqlite3.connect(local_db_path)
        cursor = local_conn.cursor()
        
        # Get tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        if not tables:
            log("No tables found in local database")
            local_conn.close()
            return True
        
        log(f"Found {len(tables)} tables: {', '.join(tables)}")
        
        # Load sync metadata for row-level tracking
        sync_metadata_path = local_db_path + '.rowsync_metadata'
        metadata = {}
        if os.path.exists(sync_metadata_path):
            try:
                with open(sync_metadata_path, 'r') as f:
                    metadata = json.load(f)
                log(f"Loaded row-level metadata for {len(metadata)} tables")
            except Exception as e:
                log(f"Warning: Could not load row metadata: {e}")
                metadata = {}
        
        total_operations = 0
        operations_saved = 0
        
        # Process each table with row-level precision
        for table in tables:
            try:
                log(f"Processing table '{table}' with row-level sync...")
                
                # Get table structure and ensure it exists in Turso
                create_sql = get_table_structure(cursor, table)
                if not create_sql:
                    continue
                
                if not dry_run:
                    run_turso_command(['db', 'shell', turso_db_name, create_sql])
                else:
                    log(f"DRY RUN: Would create table {table}")
                
                # Get column names
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [col[1] for col in cursor.fetchall()]
                
                # Get local data
                cursor.execute(f"SELECT * FROM {table}")
                local_rows = cursor.fetchall()
                
                if not local_rows:
                    # Table is empty - check if we need to clear remote
                    last_count = metadata.get(table, {}).get('row_count', 0)
                    if last_count > 0:
                        log(f"Table '{table}' is now empty, clearing remote data")
                        if not dry_run:
                            run_turso_command(['db', 'shell', turso_db_name, f"DELETE FROM {table}"])
                        total_operations += 1
                        metadata[table] = {'row_count': 0, 'row_hashes': {}}
                    else:
                        log(f"Table '{table}' is empty, skipping")
                        operations_saved += 1
                    continue
                
                # Get previous row hashes
                prev_row_hashes = metadata.get(table, {}).get('row_hashes', {})
                
                # Calculate current row hashes
                current_row_hashes = {}
                for row in local_rows:
                    row_hash = get_row_hash(row)
                    # Use first column (usually ID) as key
                    key = str(row[0])
                    current_row_hashes[key] = row_hash
                
                # Determine what operations are needed
                rows_to_update = []
                rows_to_insert = []
                keys_to_delete = []
                unchanged_count = 0
                
                # Check for updates and inserts
                for row in local_rows:
                    key = str(row[0])
                    current_hash = current_row_hashes[key]
                    prev_hash = prev_row_hashes.get(key)
                    
                    if prev_hash is None:
                        # New row
                        rows_to_insert.append(row)
                    elif prev_hash != current_hash:
                        # Changed row
                        rows_to_update.append(row)
                    else:
                        # Unchanged row
                        unchanged_count += 1
                
                # Check for deletions
                for prev_key in prev_row_hashes:
                    if prev_key not in current_row_hashes:
                        keys_to_delete.append(prev_key)
                
                # Log what we found
                log(f"  Rows to insert: {len(rows_to_insert)}")
                log(f"  Rows to update: {len(rows_to_update)}")
                log(f"  Rows to delete: {len(keys_to_delete)}")
                log(f"  Unchanged rows: {unchanged_count}")
                
                # Perform minimal operations
                ops_for_table = 0
                
                # Delete removed rows
                for key in keys_to_delete:
                    delete_sql = f"DELETE FROM {table} WHERE {columns[0]} = {key}"
                    if not dry_run:
                        run_turso_command(['db', 'shell', turso_db_name, delete_sql])
                    ops_for_table += 1
                    log(f"  {'DRY RUN: Would delete' if dry_run else 'Deleted'} row with {columns[0]}={key}")
                
                # Insert new rows
                for row in rows_to_insert:
                    values = []
                    for val in row:
                        if val is None:
                            values.append('NULL')
                        elif isinstance(val, str):
                            escaped = val.replace("'", "''")
                            values.append(f"'{escaped}'")
                        else:
                            values.append(str(val))
                    
                    insert_sql = f"INSERT INTO {table} ({','.join(columns)}) VALUES ({','.join(values)})"
                    if not dry_run:
                        run_turso_command(['db', 'shell', turso_db_name, insert_sql])
                    ops_for_table += 1
                    log(f"  {'DRY RUN: Would insert' if dry_run else 'Inserted'} row with {columns[0]}={row[0]}")
                
                # Update changed rows
                for row in rows_to_update:
                    # Build update SQL
                    set_clauses = []
                    for i, col in enumerate(columns[1:], 1):  # Skip first column (usually ID)
                        val = row[i]
                        if val is None:
                            set_clauses.append(f"{col} = NULL")
                        elif isinstance(val, str):
                            escaped = val.replace("'", "''")
                            set_clauses.append(f"{col} = '{escaped}'")
                        else:
                            set_clauses.append(f"{col} = {val}")
                    
                    update_sql = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {columns[0]} = {row[0]}"
                    if not dry_run:
                        run_turso_command(['db', 'shell', turso_db_name, update_sql])
                    ops_for_table += 1
                    log(f"  {'DRY RUN: Would update' if dry_run else 'Updated'} row with {columns[0]}={row[0]}")
                
                total_operations += ops_for_table
                
                if ops_for_table == 0:
                    operations_saved += len(local_rows) * 2  # Would have been DELETE + INSERT for each row
                    log(f"✓ Table '{table}' unchanged - saved {len(local_rows) * 2} operations")
                else:
                    saved = (len(local_rows) * 2) - ops_for_table  # Old method vs new method
                    operations_saved += max(0, saved)
                    log(f"✓ Table '{table}' optimized - {ops_for_table} operations vs {len(local_rows) * 2} with old method")
                
                # Update metadata
                metadata[table] = {
                    'row_count': len(local_rows),
                    'row_hashes': current_row_hashes,
                    'last_sync': time.time()
                }
                
            except Exception as e:
                log(f"Failed to sync table '{table}': {e}")
                continue
        
        # Save metadata
        if not dry_run:
            try:
                with open(sync_metadata_path, 'w') as f:
                    json.dump(metadata, f, indent=2)
                log(f"Saved row-level metadata to {sync_metadata_path}")
            except Exception as e:
                log(f"Warning: Could not save metadata: {e}")
        
        local_conn.close()
        
        log(f"✅ Ultra-optimized sync complete!")
        log(f"  Total operations performed: {total_operations}")
        log(f"  Operations saved: {operations_saved}")
        if (total_operations + operations_saved) > 0:
            efficiency = operations_saved / (total_operations + operations_saved) * 100
            log(f"  Efficiency: {efficiency:.1f}% reduction")
        else:
            log(f"  Efficiency: 100% (no operations needed)")
        
        return True
        
    except Exception as e:
        log(f"Row-level sync failed: {e}")
        return False

def main():
    log("Ultra-optimized row-level sync script starting...")
    
    parser = argparse.ArgumentParser(description='Ultra-optimized row-level sync: local SQLite to Turso')
    parser.add_argument('--instance-id', required=True, help='Bot instance ID')
    parser.add_argument('--user-id', required=True, help='User ID')
    parser.add_argument('--local-db', required=True, help='Path to local SQLite database')
    parser.add_argument('--turso-org', help='Turso organization (from env if not provided)')
    parser.add_argument('--turso-region', default='us-east-1', help='Turso region')
    parser.add_argument('--create-if-missing', action='store_true', help='Create Turso DB if missing')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without executing')
    
    args = parser.parse_args()
    
    log(f"Arguments: instance_id={args.instance_id}, user_id={args.user_id}, local_db={args.local_db}")
    
    turso_org = args.turso_org or os.getenv('TURSO_ORG')
    if not turso_org:
        log("ERROR: TURSO_ORG must be provided via --turso-org or environment variable")
        sys.exit(1)
    
    if not os.getenv('TURSO_API_KEY'):
        log("ERROR: TURSO_API_KEY environment variable is required")
        sys.exit(1)
    
    log(f"Using Turso org: {turso_org}")
    
    if args.dry_run:
        log("DRY RUN MODE: No actual changes will be made")
    
    try:
        if args.create_if_missing:
            turso_db_name, turso_db_url = ensure_turso_database(args.instance_id, args.user_id, turso_org, args.turso_region, args.dry_run)
            log(f"Using Turso database: {turso_db_name} (URL: {turso_db_url})")
        else:
            turso_db_name = f"bot-{args.user_id}-{args.instance_id}".lower().replace('_', '-')
            log(f"Using existing Turso database: {turso_db_name}")
        
        success = sync_local_to_turso_rowlevel(args.local_db, turso_db_name, args.dry_run)
        
        if success:
            log("✅ Ultra-optimized row-level sync completed successfully")
            sys.exit(0)
        else:
            log("❌ Sync failed")
            sys.exit(1)
            
    except Exception as e:
        log(f"❌ Sync failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

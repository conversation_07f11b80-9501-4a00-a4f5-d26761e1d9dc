/* TA-LIB Copyright (c) 1999-2007, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/* Important: This file is automatically generated by the utility gen_code.
 *            Any modification will be lost on next execution of gen_code.
 *
 * The goal of this file is to provide the functionality TA_SetRetCodeInfo.
 *
 * This function is a convenient way for the user to translate a TA_RetCode into
 * a human readable string.
 */
#include <ta_common.h>

typedef struct 
{
   TA_RetCode retCode;
   const char * const enumStr;
   const char * const infoStr;
} TA_InternalRetCodeInfo;

static TA_InternalRetCodeInfo retCodeInfoTable[] = {
%%%GENCODE%%%
         {(TA_RetCode)0xFFFF,"TA_UNKNOWN_ERR","Unknown Error"}
};

#define NB_RET_CODE_INFO (sizeof(retCodeInfoTable)/sizeof(TA_InternalRetCodeInfo))

void TA_SetRetCodeInfo( TA_RetCode theRetCode, TA_RetCodeInfo *retCodeInfo )
{
   unsigned int i;
   
   /* Trap internal error code */
   if( (theRetCode >= 5000) && (theRetCode <= 5999) )
   {
      retCodeInfo->enumStr = "TA_INTERNAL_ERROR";
      retCodeInfo->infoStr = "Unexpected Internal Error - Contact TA-Lib.org";
      return;
   }
   
   /* Check among all the error code defined in ta_common.h */
   for( i=0; i < (NB_RET_CODE_INFO-1); i++ )
   {
      if( theRetCode == retCodeInfoTable[i].retCode )
      {
         /* Error code found. */
         retCodeInfo->enumStr = retCodeInfoTable[i].enumStr;
         retCodeInfo->infoStr = retCodeInfoTable[i].infoStr;
         return;
      }
   }

   /* Error code not found. */

   /* "TA_UNKNOWN_ERR" is ALWAYS the last entry in the table. */
   retCodeInfo->enumStr = retCodeInfoTable[i].enumStr;
   retCodeInfo->infoStr = retCodeInfoTable[i].infoStr;
}

/***************/
/* End of File */
/***************/


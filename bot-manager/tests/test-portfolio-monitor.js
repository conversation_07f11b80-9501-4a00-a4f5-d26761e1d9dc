#!/usr/bin/env node

/**
 * Portfolio Monitor Test Script
 * 
 * This script tests the portfolio monitoring functionality by:
 * 1. Checking if the service is running
 * 2. Verifying portfolio snapshot files are being created
 * 3. Monitoring portfolio capture rate
 * 4. Displaying real-time statistics
 */

const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');

class PortfolioMonitorTester {
    constructor() {
        this.BOT_BASE_DIR = process.env.BOT_BASE_DIR || path.join(__dirname, 'freqtrade-instances');
        this.testDuration = 120000; // 2 minutes
        this.checkInterval = 5000; // Check every 5 seconds
        this.results = {
            serviceRunning: false,
            usersFound: 0,
            snapshotFiles: 0,
            totalSnapshots: 0,
            newSnapshots: 0,
            errors: []
        };
    }

    async runTest() {
        console.log('🧪 Portfolio Monitor Test Suite\n');
        console.log(`📂 Testing directory: ${this.BOT_BASE_DIR}`);
        console.log(`⏱️  Test duration: ${this.testDuration / 1000} seconds\n`);

        try {
            // Initial checks
            await this.checkServiceStatus();
            await this.discoverPortfolioFiles();

            // Monitor for changes
            await this.monitorPortfolioActivity();

            // Display results
            this.displayResults();
        } catch (error) {
            console.error('❌ Test failed:', error);
        }
    }

    async checkServiceStatus() {
        try {
            console.log('🔍 Checking portfolio monitor service status...');

            const { stdout } = await this.runCommand('systemctl is-active portfolio-monitor');
            this.results.serviceRunning = stdout.trim() === 'active';

            if (this.results.serviceRunning) {
                console.log('✅ Portfolio monitor service is running');

                // Get service info
                const { stdout: statusOutput } = await this.runCommand('systemctl status portfolio-monitor --no-pager -l');
                const lines = statusOutput.split('\n');
                const activeLine = lines.find(line => line.includes('Active:'));
                if (activeLine) {
                    console.log(`📊 ${activeLine.trim()}`);
                }
            } else {
                console.log('❌ Portfolio monitor service is not running');
                console.log('💡 Start it with: sudo ./manage-portfolio-monitor.sh start');
            }
        } catch (error) {
            console.log('❌ Portfolio monitor service is not installed or not running');
            console.log('💡 Install it with: sudo ./quick-start-portfolio-monitor.sh');
            this.results.serviceRunning = false;
        }
        console.log('');
    }

    async discoverPortfolioFiles() {
        try {
            console.log('📁 Discovering portfolio files...');

            if (!await fs.pathExists(this.BOT_BASE_DIR)) {
                console.log(`⚠️  Bot base directory does not exist: ${this.BOT_BASE_DIR}`);
                return;
            }

            const userDirs = await fs.readdir(this.BOT_BASE_DIR);
            this.results.usersFound = 0;
            this.results.snapshotFiles = 0;
            this.results.totalSnapshots = 0;

            for (const userId of userDirs) {
                const userPath = path.join(this.BOT_BASE_DIR, userId);
                const stats = await fs.stat(userPath);

                if (stats.isDirectory()) {
                    this.results.usersFound++;

                    const snapshotPath = path.join(userPath, 'portfolio_snapshots.json');
                    if (await fs.pathExists(snapshotPath)) {
                        this.results.snapshotFiles++;

                        try {
                            const data = await fs.readJson(snapshotPath);
                            const snapshotCount = data.snapshots ? data.snapshots.length : 0;
                            this.results.totalSnapshots += snapshotCount;

                            console.log(`📊 User ${userId}: ${snapshotCount} snapshots`);
                        } catch (error) {
                            console.log(`⚠️  User ${userId}: Error reading snapshot file`);
                        }
                    } else {
                        console.log(`📊 User ${userId}: No snapshot file yet`);
                    }
                }
            }

            console.log(`✅ Found ${this.results.usersFound} users, ${this.results.snapshotFiles} with snapshots`);
            console.log(`📈 Total snapshots across all users: ${this.results.totalSnapshots}`);
        } catch (error) {
            console.error('❌ Error discovering portfolio files:', error);
            this.results.errors.push(`Discovery error: ${error.message}`);
        }
        console.log('');
    }

    async monitorPortfolioActivity() {
        console.log('🔄 Monitoring portfolio activity for 2 minutes...');
        console.log('(Press Ctrl+C to stop early)\n');

        const startTime = Date.now();
        const startingSnapshots = this.results.totalSnapshots;
        let checkCount = 0;

        const monitor = setInterval(async () => {
            checkCount++;
            const elapsed = Date.now() - startTime;

            if (elapsed >= this.testDuration) {
                clearInterval(monitor);
                return;
            }

            try {
                // Recount snapshots
                let currentTotal = 0;
                const userDirs = await fs.readdir(this.BOT_BASE_DIR);

                for (const userId of userDirs) {
                    const userPath = path.join(this.BOT_BASE_DIR, userId);
                    const snapshotPath = path.join(userPath, 'portfolio_snapshots.json');

                    if (await fs.pathExists(snapshotPath)) {
                        try {
                            const data = await fs.readJson(snapshotPath);
                            currentTotal += data.snapshots ? data.snapshots.length : 0;
                        } catch (error) {
                            // Ignore read errors during active writing
                        }
                    }
                }

                this.results.newSnapshots = currentTotal - startingSnapshots;
                const rate = this.results.newSnapshots / (elapsed / 1000);

                console.log(`📊 Check ${checkCount}: ${currentTotal} total snapshots (+${this.results.newSnapshots} new) | Rate: ${rate.toFixed(2)}/sec`);

                // Check service logs for recent activity
                if (checkCount % 6 === 0) { // Every 30 seconds
                    try {
                        const { stdout } = await this.runCommand('journalctl -u portfolio-monitor --since "30 seconds ago" --no-pager | grep -c "Snapshot added" || echo "0"');
                        const recentSnapshots = parseInt(stdout.trim()) || 0;
                        if (recentSnapshots > 0) {
                            console.log(`🔥 Service activity: ${recentSnapshots} snapshots captured in last 30 seconds`);
                        }
                    } catch (error) {
                        // Ignore log check errors
                    }
                }
            } catch (error) {
                console.error(`❌ Error during monitoring check ${checkCount}:`, error.message);
                this.results.errors.push(`Monitor error: ${error.message}`);
            }
        }, this.checkInterval);

        // Wait for test duration
        await new Promise(resolve => setTimeout(resolve, this.testDuration));
        clearInterval(monitor);
        console.log('');
    }

    displayResults() {
        console.log('📋 Test Results Summary');
        console.log('='.repeat(40));

        console.log(`Service Status: ${this.results.serviceRunning ? '✅ RUNNING' : '❌ STOPPED'}`);
        console.log(`Users Found: ${this.results.usersFound}`);
        console.log(`Portfolio Files: ${this.results.snapshotFiles}`);
        console.log(`Total Snapshots: ${this.results.totalSnapshots}`);
        console.log(`New Snapshots: ${this.results.newSnapshots}`);

        if (this.results.newSnapshots > 0) {
            console.log('✅ Portfolio monitoring is ACTIVE and capturing data');
            const rate = this.results.newSnapshots / (this.testDuration / 1000);
            console.log(`📈 Capture rate: ${rate.toFixed(2)} snapshots/second`);
        } else if (this.results.serviceRunning) {
            console.log('⚠️  Portfolio monitoring is running but no new snapshots detected');
            console.log('💡 This may be normal if no bots are currently active');
        } else {
            console.log('❌ Portfolio monitoring is not active');
        }

        if (this.results.errors.length > 0) {
            console.log('\n⚠️  Errors encountered:');
            this.results.errors.forEach(error => console.log(`   - ${error}`));
        }

        console.log('\n💡 Next steps:');
        if (!this.results.serviceRunning) {
            console.log('   1. Install and start the service: sudo ./quick-start-portfolio-monitor.sh');
        } else {
            console.log('   1. Check service logs: sudo ./manage-portfolio-monitor.sh logs');
            console.log('   2. View service stats: sudo ./manage-portfolio-monitor.sh stats');
            console.log('   3. Follow live logs: sudo ./manage-portfolio-monitor.sh follow');
        }
    }

    async runCommand(command) {
        return new Promise((resolve, reject) => {
            const child = spawn('bash', ['-c', command]);
            let stdout = '';
            let stderr = '';

            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            child.on('close', (code) => {
                if (code === 0) {
                    resolve({ stdout, stderr });
                } else {
                    reject(new Error(`Command failed with code ${code}: ${stderr}`));
                }
            });
        });
    }
}

// Handle graceful exit
process.on('SIGINT', () => {
    console.log('\n\n🛑 Test interrupted by user');
    process.exit(0);
});

// Main execution
async function main() {
    const tester = new PortfolioMonitorTester();
    await tester.runTest();
}

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Test script failed:', error);
        process.exit(1);
    });
}

module.exports = { PortfolioMonitorTester };

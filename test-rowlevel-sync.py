#!/usr/bin/env python3
"""
Test version of row-level sync to debug issues
"""

import os
import sys
import sqlite3

def log(message):
    """Log with timestamp"""
    print(f"DEBUG: {message}")
    sys.stdout.flush()

def main():
    log("Script started")
    
    db_path = "/root/Crypto-Pilot-Freqtrade/freqtrade-instances/mybot1/user_data/tradesv3.sqlite"
    
    log(f"Checking database at: {db_path}")
    
    if not os.path.exists(db_path):
        log(f"ERROR: Database not found at {db_path}")
        return
    
    log("Database file exists")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        log("Connected to database")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        log(f"Found tables: {tables}")
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            log(f"Table {table} has {count} rows")
        
        conn.close()
        log("Test completed successfully")
        
    except Exception as e:
        log(f"ERROR: {e}")

if __name__ == "__main__":
    main()

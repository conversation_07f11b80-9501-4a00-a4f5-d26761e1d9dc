# Default Settings API Documentation

## Overview
The Default Settings API provides the frontend with all necessary information to properly initialize and manage universal risk management settings for trading bots.

## 🎯 **Frontend Requirements Met**

✅ **Default Values**: API provides default state for all settings  
✅ **Schema Information**: Field types, constraints, and validation rules  
✅ **Risk Level Mapping**: User-friendly labels for risk levels  
✅ **New Bot Detection**: Automatic initialization with proper defaults  
✅ **Complete Lifecycle**: Load defaults → Load bot settings → Save changes  

## 📡 **API Endpoints**

### 1. Get Default Settings Schema
```http
GET /api/universal-settings-defaults
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "defaults": {
    "riskLevel": 50,
    "autoRebalance": true,
    "dcaEnabled": true,
    "enabled": true
  },
  "schema": {
    "riskLevel": {
      "type": "number",
      "min": 0,
      "max": 100,
      "default": 50,
      "step": 1,
      "description": "Risk level from conservative (0) to aggressive (100)",
      "examples": {
        "0": "Ultra Conservative - 5% max drawdown, 1% risk per trade",
        "25": "Conservative - 10% max drawdown, 1.5% risk per trade",
        "50": "Balanced - 15% max drawdown, 2% risk per trade",
        "75": "Aggressive - 20% max drawdown, 2.5% risk per trade",
        "100": "Ultra Aggressive - 25% max drawdown, 3% risk per trade"
      }
    },
    "autoRebalance": {
      "type": "boolean",
      "default": true,
      "description": "Automatically rebalance portfolio to maintain target allocations",
      "details": "Monitors portfolio drift and rebalances when allocation deviates beyond threshold"
    },
    "dcaEnabled": {
      "type": "boolean",
      "default": true,
      "description": "Enable Dollar Cost Averaging on losing positions",
      "details": "Places additional orders when price drops to average down position cost"
    }
  },
  "riskLevelMapping": {
    "0": { "label": "Ultra Conservative", "maxDrawdown": "5%", "riskPerTrade": "1%", "dcaOrders": 2 },
    "25": { "label": "Conservative", "maxDrawdown": "10%", "riskPerTrade": "1.5%", "dcaOrders": 2 },
    "50": { "label": "Balanced", "maxDrawdown": "15%", "riskPerTrade": "2%", "dcaOrders": 3 },
    "75": { "label": "Aggressive", "maxDrawdown": "20%", "riskPerTrade": "2.5%", "dcaOrders": 4 },
    "100": { "label": "Ultra Aggressive", "maxDrawdown": "25%", "riskPerTrade": "3%", "dcaOrders": 5 }
  }
}
```

### 2. Get Bot Settings with Defaults
```http
GET /api/universal-settings/{instanceId}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "settings": {
    "riskLevel": 75,
    "autoRebalance": false,
    "dcaEnabled": true,
    "enabled": true
  },
  "defaults": {
    "riskLevel": 50,
    "autoRebalance": true,
    "dcaEnabled": true,
    "enabled": true
  },
  "schema": { /* same schema as above */ },
  "isNewBot": false,
  "message": "Universal settings retrieved successfully"
}
```

### 3. Save Changes (Frontend "Save Changes" Button)
```http
PUT /api/universal-settings/{instanceId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "riskLevel": 60,
  "autoRebalance": true,
  "dcaEnabled": false
}
```

**Response:**
```json
{
  "success": true,
  "settings": {
    "riskLevel": 60,
    "autoRebalance": true,
    "dcaEnabled": false,
    "enabled": true
  },
  "message": "Universal settings updated successfully"
}
```

### 4. Get All Bots with Settings Overview
```http
GET /api/universal-settings
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "bots": [
    {
      "instanceId": "user123-bot-001",
      "settings": { "riskLevel": 50, "autoRebalance": true, "dcaEnabled": true, "enabled": true },
      "defaults": { "riskLevel": 50, "autoRebalance": true, "dcaEnabled": true, "enabled": true },
      "isRunning": true,
      "isNewBot": false,
      "lastUpdated": "2025-09-20T10:30:00.000Z"
    }
  ],
  "defaults": { "riskLevel": 50, "autoRebalance": true, "dcaEnabled": true, "enabled": true },
  "totalBots": 3,
  "runningBots": 2
}
```

## 🎨 **Frontend Implementation Guide**

### React/Vue/Angular Component Example
```javascript
class UniversalSettingsComponent {
  constructor() {
    this.defaults = null;
    this.schema = null;
    this.currentSettings = null;
  }

  async initialize(botId = null) {
    // 1. Load default settings and schema
    const defaultsResponse = await fetch('/api/universal-settings-defaults', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const { defaults, schema, riskLevelMapping } = await defaultsResponse.json();
    
    this.defaults = defaults;
    this.schema = schema;
    this.riskLevelMapping = riskLevelMapping;
    
    // 2. Initialize UI with defaults
    this.setRiskLevel(defaults.riskLevel);
    this.setAutoRebalance(defaults.autoRebalance);
    this.setDcaEnabled(defaults.dcaEnabled);
    
    // 3. Load bot-specific settings if bot exists
    if (botId) {
      const botResponse = await fetch(`/api/universal-settings/${botId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (botResponse.ok) {
        const { settings, isNewBot } = await botResponse.json();
        
        if (!isNewBot) {
          // Override defaults with bot-specific settings
          this.setRiskLevel(settings.riskLevel);
          this.setAutoRebalance(settings.autoRebalance);
          this.setDcaEnabled(settings.dcaEnabled);
        }
        
        this.currentSettings = settings;
      }
    }
  }

  async saveChanges(botId) {
    const newSettings = {
      riskLevel: this.getRiskLevel(),
      autoRebalance: this.getAutoRebalance(),
      dcaEnabled: this.getDcaEnabled()
    };

    const response = await fetch(`/api/universal-settings/${botId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newSettings)
    });

    if (response.ok) {
      const { settings } = await response.json();
      this.currentSettings = settings;
      this.showSuccessMessage('Settings saved successfully!');
    } else {
      this.showErrorMessage('Failed to save settings');
    }
  }

  getRiskLevelLabel(level) {
    // Use the mapping from API
    const mapping = this.riskLevelMapping[level] || this.riskLevelMapping[50];
    return mapping.label;
  }

  getRiskLevelDetails(level) {
    const mapping = this.riskLevelMapping[level] || this.riskLevelMapping[50];
    return `${mapping.label} - ${mapping.maxDrawdown} max drawdown, ${mapping.riskPerTrade} risk per trade`;
  }
}
```

### HTML Template Example
```html
<div class="universal-settings">
  <h3>Advanced Settings</h3>
  
  <!-- Risk Level Slider -->
  <div class="setting-group">
    <label>Risk Level</label>
    <div class="risk-slider-container">
      <input 
        type="range" 
        min="0" 
        max="100" 
        step="1"
        v-model="riskLevel"
        @input="updateRiskLevelLabel"
      />
      <span class="risk-value">{{ riskLevel }}%</span>
    </div>
    <p class="risk-description">{{ getRiskLevelDetails(riskLevel) }}</p>
  </div>

  <!-- Auto-Rebalance Toggle -->
  <div class="setting-group">
    <label>Auto-Rebalance</label>
    <div class="toggle-switch">
      <input type="checkbox" v-model="autoRebalance" />
      <span>{{ autoRebalance ? 'On' : 'Off' }}</span>
    </div>
    <p class="setting-description">{{ schema.autoRebalance.description }}</p>
  </div>

  <!-- DCA Toggle -->
  <div class="setting-group">
    <label>DCA Enabled</label>
    <div class="toggle-switch">
      <input type="checkbox" v-model="dcaEnabled" />
      <span>{{ dcaEnabled ? 'On' : 'Off' }}</span>
    </div>
    <p class="setting-description">{{ schema.dcaEnabled.description }}</p>
  </div>

  <!-- Save Button -->
  <button 
    class="save-button"
    @click="saveChanges"
    :disabled="!hasChanges"
  >
    Save Changes
  </button>
</div>
```

## 🔄 **Bot Lifecycle Integration**

### New Bot Creation
1. **Bot Provisioning**: Universal settings are automatically initialized with defaults
2. **Enhanced Bots**: Get slightly higher default risk levels (50-80 range)
3. **Standard Bots**: Get standard defaults (50% risk level)

### Existing Bot Loading
1. **Frontend**: Loads defaults first for immediate UI initialization
2. **Bot Specific**: Loads and applies bot-specific settings if they exist
3. **Fallback**: Uses defaults if bot settings are missing or invalid

### Settings Updates
1. **Frontend**: User adjusts sliders/toggles
2. **Save Changes**: API call updates both `universal-settings.json` and `risk-config.json`
3. **Background Service**: Automatically applies new settings to running bot

## 🧪 **Testing**

Run the test script to verify the API works correctly:
```bash
cd /root/Crypto-Pilot-Freqtrade
./test-default-settings-api.sh
```

## 🎯 **Benefits for Frontend Development**

1. **Immediate Initialization**: UI can render immediately with defaults
2. **Schema Validation**: Frontend knows field types and constraints
3. **User-Friendly Labels**: Risk level mapping provides clear descriptions
4. **Graceful Degradation**: Works even if bot-specific settings are missing
5. **Complete Context**: All information needed for proper UI state management

The Default Settings API ensures your frontend can properly initialize and manage universal risk management settings for any trading bot! 🚀
#!/usr/bin/env python3
"""
Local-First Database Sync with Turso
Syncs local SQLite database to Turso for backup and remote access
"""

import os
import sys
import json
import sqlite3
import time
import subprocess
import argparse
from pathlib import Path

def log(message):
    """Log with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_turso_command(cmd_args):
    """Run turso CLI command and return output"""
    try:
        # Use TURSO_CMD environment variable if set, otherwise use 'turso'
        turso_cmd = os.environ.get('TURSO_CMD', 'turso')
        result = subprocess.run([turso_cmd] + cmd_args, 
                              capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        log(f"Turso command failed: {' '.join(cmd_args)}")
        log(f"Error: {e.stderr}")
        raise

def ensure_turso_database(instance_id, user_id, turso_org, turso_region="us-east-1"):
    """Ensure Turso database exists for the bot instance"""
    db_name = f"bot-{user_id}-{instance_id}".lower().replace('_', '-')
    
    try:
        # Check if database exists
        dbs_output = run_turso_command(['db', 'list'])
        if db_name in dbs_output:
            log(f"Turso database '{db_name}' already exists")
        else:
            # Create database
            log(f"Creating Turso database '{db_name}' in region '{turso_region}'...")
            run_turso_command(['db', 'create', db_name, '--location', turso_region, '--wait'])
            log(f"✓ Turso database '{db_name}' created successfully")
        
        # Get and log the database URL
        db_url = get_turso_url(db_name)
        log(f"✓ Turso database URL: {db_url}")
        
        return db_name, db_url
        
    except Exception as e:
        log(f"Failed to ensure Turso database: {e}")
        raise

def get_turso_url(db_name):
    """Get Turso database URL"""
    try:
        url = run_turso_command(['db', 'show', db_name, '--url'])
        log(f"Retrieved Turso URL for '{db_name}'")
        return url
    except Exception as e:
        log(f"Failed to get Turso URL: {e}")
        raise

def sync_local_to_turso(local_db_path, turso_db_name):
    """Sync local SQLite database to Turso"""
    try:
        log(f"Starting sync from {local_db_path} to Turso database '{turso_db_name}'")
        
        # Check if local database exists
        if not os.path.exists(local_db_path):
            log(f"Local database not found: {local_db_path}")
            return False
        
        # Get list of tables in local database
        local_conn = sqlite3.connect(local_db_path)
        cursor = local_conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        if not tables:
            log("No tables found in local database")
            local_conn.close()
            return True
        
        log(f"Found tables: {', '.join(tables)}")
        
        # For each table, dump data and sync to Turso
        sync_count = 0
        for table in tables:
            try:
                # Skip system tables
                if table.startswith('sqlite_'):
                    log(f"Skipping system table '{table}'")
                    continue
                    
                # Get table schema
                cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
                schema_result = cursor.fetchone()
                if not schema_result:
                    continue
                    
                create_sql = schema_result[0]
                
                # Convert CREATE TABLE to CREATE TABLE IF NOT EXISTS
                if create_sql.upper().startswith('CREATE TABLE'):
                    create_sql = create_sql.replace('CREATE TABLE', 'CREATE TABLE IF NOT EXISTS', 1)
                
                # Create table in Turso (if not exists)
                log(f"Ensuring table '{table}' exists in Turso...")
                run_turso_command(['db', 'shell', turso_db_name, create_sql])
                
                # Get row count for progress
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                if row_count == 0:
                    log(f"Table '{table}' is empty, skipping data sync")
                    continue
                
                log(f"Syncing {row_count} rows from table '{table}'...")
                
                # Get all data from table
                cursor.execute(f"SELECT * FROM {table}")
                rows = cursor.fetchall()
                
                # Get column names
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [col[1] for col in cursor.fetchall()]
                
                # Clear existing data in Turso table
                run_turso_command(['db', 'shell', turso_db_name, f"DELETE FROM {table}"])
                
                # Insert data in batches
                batch_size = 100
                for i in range(0, len(rows), batch_size):
                    batch = rows[i:i + batch_size]
                    
                    # Create INSERT statements
                    placeholders = ','.join(['?' for _ in columns])
                    insert_sql = f"INSERT INTO {table} ({','.join(columns)}) VALUES ({placeholders})"
                    
                    # Convert batch to proper format for turso shell
                    for row in batch:
                        # Escape and format values
                        values = []
                        for val in row:
                            if val is None:
                                values.append('NULL')
                            elif isinstance(val, str):
                                # Escape single quotes
                                escaped = val.replace("'", "''")
                                values.append(f"'{escaped}'")
                            else:
                                values.append(str(val))
                        
                        final_sql = f"INSERT INTO {table} ({','.join(columns)}) VALUES ({','.join(values)})"
                        run_turso_command(['db', 'shell', turso_db_name, final_sql])
                    
                    log(f"Synced batch {i//batch_size + 1}/{(len(rows) + batch_size - 1)//batch_size} for table '{table}'")
                
                sync_count += row_count
                log(f"✓ Table '{table}' sync complete ({row_count} rows)")
                
            except Exception as e:
                log(f"Failed to sync table '{table}': {e}")
                continue
        
        local_conn.close()
        log(f"✓ Sync complete! Synced {sync_count} total rows across {len(tables)} tables")
        return True
        
    except Exception as e:
        log(f"Sync failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Sync local SQLite database to Turso')
    parser.add_argument('--instance-id', required=True, help='Bot instance ID')
    parser.add_argument('--user-id', required=True, help='User ID')
    parser.add_argument('--local-db', required=True, help='Path to local SQLite database')
    parser.add_argument('--turso-org', help='Turso organization (from env if not provided)')
    parser.add_argument('--turso-region', default='us-east-1', help='Turso region')
    parser.add_argument('--create-if-missing', action='store_true', help='Create Turso DB if missing')
    
    args = parser.parse_args()
    
    # Get Turso org from environment if not provided
    turso_org = args.turso_org or os.getenv('TURSO_ORG')
    if not turso_org:
        log("ERROR: TURSO_ORG must be provided via --turso-org or environment variable")
        sys.exit(1)
    
    # Check if Turso API key is available
    if not os.getenv('TURSO_API_KEY'):
        log("ERROR: TURSO_API_KEY environment variable is required")
        sys.exit(1)
    
    try:
        # Ensure Turso database exists
        if args.create_if_missing:
            turso_db_name, turso_db_url = ensure_turso_database(args.instance_id, args.user_id, turso_org, args.turso_region)
            log(f"Using Turso database: {turso_db_name} (URL: {turso_db_url})")
        else:
            turso_db_name = f"bot-{args.user_id}-{args.instance_id}".lower().replace('_', '-')
            log(f"Using existing Turso database: {turso_db_name}")
        
        # Perform sync
        success = sync_local_to_turso(args.local_db, turso_db_name)
        
        if success:
            log("✅ Sync completed successfully")
            sys.exit(0)
        else:
            log("❌ Sync failed")
            sys.exit(1)
            
    except Exception as e:
        log(f"❌ Sync failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

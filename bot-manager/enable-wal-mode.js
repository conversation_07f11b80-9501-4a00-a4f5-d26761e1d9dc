#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');

async function enableWalMode(dbPath) {
  return new Promise((resolve, reject) => {
    const sqlite3 = spawn('sqlite3', [dbPath, 'PRAGMA journal_mode=WAL;']);

    let output = '';
    let error = '';

    sqlite3.stdout.on('data', (data) => {
      output += data.toString();
    });

    sqlite3.stderr.on('data', (data) => {
      error += data.toString();
    });

    sqlite3.on('close', (code) => {
      if (code === 0) {
        console.log(`✓ Enabled WAL mode for ${dbPath}`);
        resolve(true);
      } else {
        console.error(`✗ Failed to enable WAL mode for ${dbPath}: ${error}`);
        resolve(false);
      }
    });

    sqlite3.on('error', (err) => {
      console.error(`✗ Error running sqlite3 for ${dbPath}:`, err.message);
      resolve(false);
    });
  });
}

async function main() {
  const BOT_BASE_DIR = process.env.BOT_BASE_DIR || path.join(__dirname, '..', 'freqtrade-instances');

  console.log('🔧 Enabling WAL mode for all SQLite databases...');
  console.log(`Bot base directory: ${BOT_BASE_DIR}`);

  let processedCount = 0;
  let successCount = 0;

  try {
    // Scan for all bot instances
    const users = await fs.readdir(BOT_BASE_DIR);

    for (const userId of users) {
      const userDir = path.join(BOT_BASE_DIR, userId);
      const userStat = await fs.stat(userDir);

      if (userStat.isDirectory()) {
        console.log(`\nProcessing user: ${userId}`);

        const instances = await fs.readdir(userDir);

        for (const instanceId of instances) {
          const instanceDir = path.join(userDir, instanceId);
          const instanceStat = await fs.stat(instanceDir);

          if (instanceStat.isDirectory()) {
            const dbPath = path.join(instanceDir, 'user_data', 'tradesv3.sqlite');

            if (await fs.pathExists(dbPath)) {
              console.log(`Processing database: ${instanceId}`);
              processedCount++;

              const success = await enableWalMode(dbPath);
              if (success) {
                successCount++;
              }
            } else {
              console.log(`⚠️  No database found for ${instanceId}`);
            }
          }
        }
      }
    }

    console.log('\n📊 Summary:');
    console.log(`✓ Successfully processed: ${successCount}/${processedCount} databases`);

    if (successCount > 0) {
      console.log('\n🔄 WAL mode has been enabled. This will help with concurrent access.');
      console.log('   Restart bot containers to ensure they pick up the WAL mode.');
    }

  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { enableWalMode };

# HighFrequencyStrategy.py
from freqtrade.strategy import IStrategy
import talib.abstract as ta
import numpy as np
from pandas import DataFrame

class HighFrequencyStrategy(IStrategy):
    timeframe = '1m'
    stoploss = -0.05
    minimal_roi = {"0": 0.002}
    trailing_stop = True
    trailing_stop_positive = 0.005
    trailing_stop_positive_offset = 0.01
    process_only_new_candles = True
    startup_candle_count = 20

    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'limit',
        'stoploss_on_exchange': False,
        'stoploss_on_exchange_interval': 60
    }
    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }
    use_exit_signal = True

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['ema3'] = ta.EMA(dataframe, timeperiod=3)
        dataframe['ema8'] = ta.EMA(dataframe, timeperiod=8)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=7)
        dataframe['stoch_k'], dataframe['stoch_d'] = ta.STOCH(dataframe)
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = [
            dataframe['ema3'] > dataframe['ema8'],
            dataframe['rsi'] < 40,
            dataframe['stoch_k'] < dataframe['stoch_d']
        ]
        if conditions:
            dataframe.loc[np.all(conditions, axis=0), 'enter_long'] = 1
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = [
            dataframe['rsi'] > 70,
            dataframe['stoch_k'] > dataframe['stoch_d']
        ]
        if conditions:
            dataframe.loc[np.any(conditions, axis=0), 'exit_long'] = 1
        return dataframe

// Universal Risk Management Middleware
// This middleware intercepts FreqTrade API calls and applies risk management, DCA, and auto-rebalancing
// to ANY strategy without modifying the strategy code itself.

const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');

class UniversalRiskManager {
  constructor(instanceId, instanceDir) {
    this.instanceId = instanceId;
    this.instanceDir = instanceDir;
    this.riskConfigPath = path.join(instanceDir, 'risk-config.json');
    this.settingsPath = path.join(instanceDir, 'universal-settings.json');
    this.positionsPath = path.join(instanceDir, 'user_data', 'positions.json');
    this.dcaOrdersPath = path.join(instanceDir, 'user_data', 'dca-orders.json');
    
    // Initialize default settings
    this.defaultSettings = {
      riskLevel: 50, // 0-100 scale
      autoRebalance: true,
      dcaEnabled: true,
      enabled: true
    };
    
    this.loadSettings();
  }

  async loadSettings() {
    try {
      if (await fs.pathExists(this.settingsPath)) {
        this.settings = await fs.readJson(this.settingsPath);
      } else {
        this.settings = { ...this.defaultSettings };
        await this.saveSettings();
      }
    } catch (error) {
      console.warn(`[${this.instanceId}] Failed to load settings, using defaults:`, error.message);
      this.settings = { ...this.defaultSettings };
    }
  }

  async saveSettings() {
    try {
      await fs.writeJson(this.settingsPath, this.settings, { spaces: 2 });
      console.log(`[${this.instanceId}] ✓ Universal settings saved`);
    } catch (error) {
      console.error(`[${this.instanceId}] Failed to save settings:`, error.message);
    }
  }

  async updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    await this.saveSettings();
    
    // Update risk config based on new settings
    await this.updateRiskConfig();
    
    console.log(`[${this.instanceId}] ✓ Settings updated:`, this.settings);
  }

  async updateRiskConfig() {
    // Convert risk level (0-100) to actual risk parameters
    const riskLevel = this.settings.riskLevel / 100; // Convert to 0-1 scale
    
    const riskConfig = {
      maxDrawdown: 0.05 + (riskLevel * 0.20), // 5% to 25% based on risk level
      maxTotalRisk: 0.10 + (riskLevel * 0.25), // 10% to 35% based on risk level
      riskPerTrade: 0.01 + (riskLevel * 0.02), // 1% to 3% based on risk level
      positionSizing: {
        baseStakePercent: 0.05 + (riskLevel * 0.10), // 5% to 15% based on risk level
        maxStakePercent: 0.10 + (riskLevel * 0.25), // 10% to 35% based on risk level
        volatilityAdjustment: true
      },
      stopLoss: {
        enabled: true,
        baseStopLoss: -0.04 - (riskLevel * 0.08), // -4% to -12% based on risk level
        trailingStop: true,
        dynamicAdjustment: true
      },
      dca: {
        enabled: this.settings.dcaEnabled,
        maxOrders: Math.floor(2 + (riskLevel * 3)), // 2 to 5 orders based on risk level
        triggerPercent: -0.03 - (riskLevel * 0.09), // -3% to -12% based on risk level
        sizeMultiplier: 1.2 + (riskLevel * 0.8) // 1.2x to 2.0x based on risk level
      },
      rebalancing: {
        enabled: this.settings.autoRebalance,
        threshold: 0.20 - (riskLevel * 0.05), // 20% to 15% threshold (lower = more frequent rebalancing)
        frequency: 24, // Check every 24 hours
        targetAllocations: {
          btc: 0.40,
          eth: 0.25,
          alt: 0.20,
          stable: 0.10,
          other: 0.05
        }
      }
    };

    try {
      await fs.writeJson(this.riskConfigPath, riskConfig, { spaces: 2 });
      console.log(`[${this.instanceId}] ✓ Risk config updated for risk level: ${this.settings.riskLevel}%`);
    } catch (error) {
      console.error(`[${this.instanceId}] Failed to update risk config:`, error.message);
    }
  }

  // Intercept and modify stake amount based on risk settings
  async calculateStakeAmount(originalAmount, pair, currentPrice, accountBalance) {
    if (!this.settings.enabled) return originalAmount;

    try {
      const riskConfig = await fs.readJson(this.riskConfigPath);
      const baseStake = accountBalance * riskConfig.positionSizing.baseStakePercent;
      const maxStake = accountBalance * riskConfig.positionSizing.maxStakePercent;
      
      // Apply volatility adjustment if enabled
      let adjustedStake = baseStake;
      if (riskConfig.positionSizing.volatilityAdjustment) {
        // Simple volatility calculation (you can enhance this)
        const volatility = await this.calculateVolatility(pair);
        adjustedStake = baseStake * (1 / (1 + volatility));
      }
      
      // Ensure we don't exceed maximum stake
      const finalStake = Math.min(adjustedStake, maxStake);
      
      console.log(`[${this.instanceId}] Position sizing: ${pair} - Original: $${originalAmount}, Risk-adjusted: $${finalStake.toFixed(2)}`);
      return finalStake;
      
    } catch (error) {
      console.warn(`[${this.instanceId}] Position sizing failed, using original amount:`, error.message);
      return originalAmount;
    }
  }

  // DCA Order Management
  async checkAndPlaceDCAOrders(pair, currentPrice, openTrades) {
    if (!this.settings.enabled || !this.settings.dcaEnabled) return;

    try {
      const riskConfig = await fs.readJson(this.riskConfigPath);
      if (!riskConfig.dca.enabled) return;

      // Load existing DCA orders
      let dcaOrders = {};
      if (await fs.pathExists(this.dcaOrdersPath)) {
        dcaOrders = await fs.readJson(this.dcaOrdersPath);
      }

      // Check each open trade for DCA opportunities
      for (const trade of openTrades) {
        if (trade.pair === pair && trade.is_open) {
          const entryPrice = trade.open_rate;
          const priceDropPercent = (currentPrice - entryPrice) / entryPrice;
          
          const dcaKey = `${pair}_${trade.trade_id}`;
          const existingDCAOrders = dcaOrders[dcaKey] || [];
          
          // Check if we should place a DCA order
          if (priceDropPercent <= riskConfig.dca.triggerPercent && 
              existingDCAOrders.length < riskConfig.dca.maxOrders) {
            
            const dcaSize = trade.stake_amount * riskConfig.dca.sizeMultiplier;
            
            // Place DCA order (this would call FreqTrade API)
            const dcaOrder = {
              pair: pair,
              originalTradeId: trade.trade_id,
              size: dcaSize,
              price: currentPrice,
              timestamp: Date.now(),
              level: existingDCAOrders.length + 1
            };
            
            existingDCAOrders.push(dcaOrder);
            dcaOrders[dcaKey] = existingDCAOrders;
            
            console.log(`[${this.instanceId}] 🔄 DCA Order placed: ${pair} Level ${dcaOrder.level} - $${dcaSize.toFixed(2)} at $${currentPrice}`);
          }
        }
      }

      // Save updated DCA orders
      await fs.writeJson(this.dcaOrdersPath, dcaOrders, { spaces: 2 });
      
    } catch (error) {
      console.error(`[${this.instanceId}] DCA management failed:`, error.message);
    }
  }

  // Auto-Rebalancing Logic
  async checkAndRebalance(currentPositions, accountBalance) {
    if (!this.settings.enabled || !this.settings.autoRebalance) return;

    try {
      const riskConfig = await fs.readJson(this.riskConfigPath);
      if (!riskConfig.rebalancing.enabled) return;

      // Calculate current allocations
      const currentAllocations = this.calculateCurrentAllocations(currentPositions, accountBalance);
      const targetAllocations = riskConfig.rebalancing.targetAllocations;
      
      // Check if rebalancing is needed
      const needsRebalancing = this.checkRebalancingThreshold(currentAllocations, targetAllocations, riskConfig.rebalancing.threshold);
      
      if (needsRebalancing) {
        console.log(`[${this.instanceId}] 🔄 Portfolio rebalancing triggered`);
        console.log('Current allocations:', currentAllocations);
        console.log('Target allocations:', targetAllocations);
        
        // Calculate rebalancing actions
        const rebalanceActions = this.calculateRebalanceActions(currentAllocations, targetAllocations, accountBalance);
        
        // Execute rebalancing (this would call FreqTrade API to close/open positions)
        for (const action of rebalanceActions) {
          console.log(`[${this.instanceId}] Rebalance action: ${action.action} ${action.pair} - $${action.amount.toFixed(2)}`);
          // Here you would call the actual FreqTrade API to execute the rebalancing
        }
      }
      
    } catch (error) {
      console.error(`[${this.instanceId}] Auto-rebalancing failed:`, error.message);
    }
  }

  calculateCurrentAllocations(positions, totalBalance) {
    const allocations = { btc: 0, eth: 0, alt: 0, stable: 0, other: 0 };
    
    for (const [pair, position] of Object.entries(positions)) {
      const value = position.value || 0;
      const percentage = value / totalBalance;
      
      if (pair.includes('BTC')) allocations.btc += percentage;
      else if (pair.includes('ETH')) allocations.eth += percentage;
      else if (pair.includes('USD') || pair.includes('USDT')) allocations.stable += percentage;
      else if (['ADA', 'SOL', 'DOT', 'AVAX', 'MATIC'].some(alt => pair.includes(alt))) allocations.alt += percentage;
      else allocations.other += percentage;
    }
    
    return allocations;
  }

  checkRebalancingThreshold(current, target, threshold) {
    for (const [category, targetPercent] of Object.entries(target)) {
      const currentPercent = current[category] || 0;
      const drift = Math.abs(currentPercent - targetPercent);
      if (drift > threshold) {
        return true;
      }
    }
    return false;
  }

  calculateRebalanceActions(current, target, totalBalance) {
    const actions = [];
    
    for (const [category, targetPercent] of Object.entries(target)) {
      const currentPercent = current[category] || 0;
      const difference = targetPercent - currentPercent;
      const dollarAmount = difference * totalBalance;
      
      if (Math.abs(dollarAmount) > 50) { // Only rebalance if difference > $50
        actions.push({
          category: category,
          action: dollarAmount > 0 ? 'BUY' : 'SELL',
          amount: Math.abs(dollarAmount),
          pair: this.getCategoryPair(category)
        });
      }
    }
    
    return actions;
  }

  getCategoryPair(category) {
    const categoryPairs = {
      btc: 'BTC/USD',
      eth: 'ETH/USD',
      alt: 'SOL/USD', // Default alt coin
      stable: 'USD',
      other: 'ADA/USD'
    };
    return categoryPairs[category] || 'BTC/USD';
  }

  async calculateVolatility(pair) {
    // Simple volatility calculation - you can enhance this with more sophisticated methods
    try {
      // This would fetch recent price data and calculate volatility
      // For now, return a mock volatility value
      return Math.random() * 0.1; // 0-10% volatility
    } catch (error) {
      return 0.05; // Default 5% volatility
    }
  }
}

module.exports = UniversalRiskManager;
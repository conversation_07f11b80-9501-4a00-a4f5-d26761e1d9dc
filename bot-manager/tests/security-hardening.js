#!/usr/bin/env node

/**
 * Phase 5: Security Hardening & Rate Limiting
 * ============================================
 * 
 * Enhanced security features for production deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 PHASE 5: SECURITY HARDENING IMPLEMENTATION');
console.log('='.repeat(50));

// Rate limiting configuration
const RATE_LIMIT_CONFIG = {
    // WebSocket connection limits
    CONNECTION_LIMITS: {
        MAX_CONNECTIONS_PER_IP: 10,
        MAX_CONNECTIONS_PER_USER: 5,
        CONNECTION_WINDOW_MS: 60000 // 1 minute
    },

    // Message rate limits
    MESSAGE_LIMITS: {
        MAX_MESSAGES_PER_MINUTE: 60,
        MAX_MESSAGES_PER_SECOND: 5,
        BURST_ALLOWANCE: 10
    },

    // Authentication limits
    AUTH_LIMITS: {
        MAX_AUTH_ATTEMPTS_PER_IP: 10,
        AUTH_LOCKOUT_DURATION_MS: 300000, // 5 minutes
        TOKEN_REFRESH_COOLDOWN_MS: 30000 // 30 seconds
    }
};

/**
 * Rate Limiting Middleware for WebSocket
 */
const RATE_LIMITER_CODE = `
/**
 * Enhanced Rate Limiting and Security for WebSocket Connections
 * =============================================================
 */

class SecurityManager {
    constructor() {
        // Connection tracking
        this.connectionsByIP = new Map();
        this.connectionsByUser = new Map();
        
        // Message rate limiting
        this.messageRates = new Map();
        
        // Authentication tracking
        this.authAttempts = new Map();
        this.authLockouts = new Map();
        
        // Configuration
        this.config = {
            maxConnectionsPerIP: ${RATE_LIMIT_CONFIG.CONNECTION_LIMITS.MAX_CONNECTIONS_PER_IP},
            maxConnectionsPerUser: ${RATE_LIMIT_CONFIG.CONNECTION_LIMITS.MAX_CONNECTIONS_PER_USER},
            maxMessagesPerMinute: ${RATE_LIMIT_CONFIG.MESSAGE_LIMITS.MAX_MESSAGES_PER_MINUTE},
            maxMessagesPerSecond: ${RATE_LIMIT_CONFIG.MESSAGE_LIMITS.MAX_MESSAGES_PER_SECOND},
            burstAllowance: ${RATE_LIMIT_CONFIG.MESSAGE_LIMITS.BURST_ALLOWANCE},
            maxAuthAttempts: ${RATE_LIMIT_CONFIG.AUTH_LIMITS.MAX_AUTH_ATTEMPTS_PER_IP},
            authLockoutDuration: ${RATE_LIMIT_CONFIG.AUTH_LIMITS.AUTH_LOCKOUT_DURATION_MS},
            tokenRefreshCooldown: ${RATE_LIMIT_CONFIG.AUTH_LIMITS.TOKEN_REFRESH_COOLDOWN_MS}
        };
        
        // Cleanup intervals
        this.startCleanupRoutines();
    }
    
    /**
     * Check if IP is allowed to connect
     */
    checkConnectionLimit(clientIP, userId = null) {
        const now = Date.now();
        
        // Check IP-based limits
        const ipConnections = this.connectionsByIP.get(clientIP) || [];
        const recentIPConnections = ipConnections.filter(time => now - time < ${RATE_LIMIT_CONFIG.CONNECTION_LIMITS.CONNECTION_WINDOW_MS});
        
        if (recentIPConnections.length >= this.config.maxConnectionsPerIP) {
            return {
                allowed: false,
                reason: 'Too many connections from this IP address',
                retryAfter: ${RATE_LIMIT_CONFIG.CONNECTION_LIMITS.CONNECTION_WINDOW_MS}
            };
        }
        
        // Check user-based limits
        if (userId) {
            const userConnections = this.connectionsByUser.get(userId) || [];
            const recentUserConnections = userConnections.filter(time => now - time < ${RATE_LIMIT_CONFIG.CONNECTION_LIMITS.CONNECTION_WINDOW_MS});
            
            if (recentUserConnections.length >= this.config.maxConnectionsPerUser) {
                return {
                    allowed: false,
                    reason: 'Too many connections for this user',
                    retryAfter: ${RATE_LIMIT_CONFIG.CONNECTION_LIMITS.CONNECTION_WINDOW_MS}
                };
            }
        }
        
        return { allowed: true };
    }
    
    /**
     * Record a new connection
     */
    recordConnection(clientIP, userId = null) {
        const now = Date.now();
        
        // Record IP connection
        const ipConnections = this.connectionsByIP.get(clientIP) || [];
        ipConnections.push(now);
        this.connectionsByIP.set(clientIP, ipConnections);
        
        // Record user connection
        if (userId) {
            const userConnections = this.connectionsByUser.get(userId) || [];
            userConnections.push(now);
            this.connectionsByUser.set(userId, userConnections);
        }
    }
    
    /**
     * Check message rate limits
     */
    checkMessageRate(userId, clientIP) {
        const now = Date.now();
        const key = userId || clientIP;
        
        if (!this.messageRates.has(key)) {
            this.messageRates.set(key, {
                messages: [],
                lastMinute: 0,
                lastSecond: 0,
                burstCount: 0
            });
        }
        
        const rate = this.messageRates.get(key);
        
        // Clean old messages
        rate.messages = rate.messages.filter(time => now - time < 60000);
        
        // Check per-minute limit
        if (rate.messages.length >= this.config.maxMessagesPerMinute) {
            return {
                allowed: false,
                reason: 'Message rate limit exceeded (per minute)',
                retryAfter: 60000
            };
        }
        
        // Check per-second limit with burst allowance
        const recentMessages = rate.messages.filter(time => now - time < 1000);
        if (recentMessages.length >= this.config.maxMessagesPerSecond + this.config.burstAllowance) {
            return {
                allowed: false,
                reason: 'Message rate limit exceeded (per second)',
                retryAfter: 1000
            };
        }
        
        return { allowed: true };
    }
    
    /**
     * Record a message
     */
    recordMessage(userId, clientIP) {
        const now = Date.now();
        const key = userId || clientIP;
        
        if (!this.messageRates.has(key)) {
            this.messageRates.set(key, { messages: [] });
        }
        
        this.messageRates.get(key).messages.push(now);
    }
    
    /**
     * Check authentication limits
     */
    checkAuthLimit(clientIP) {
        const now = Date.now();
        
        // Check if IP is locked out
        const lockout = this.authLockouts.get(clientIP);
        if (lockout && now < lockout.until) {
            return {
                allowed: false,
                reason: 'IP temporarily locked due to too many failed authentication attempts',
                retryAfter: lockout.until - now
            };
        }
        
        // Check auth attempt rate
        const attempts = this.authAttempts.get(clientIP) || [];
        const recentAttempts = attempts.filter(time => now - time < this.config.authLockoutDuration);
        
        if (recentAttempts.length >= this.config.maxAuthAttempts) {
            // Lock out the IP
            this.authLockouts.set(clientIP, {
                until: now + this.config.authLockoutDuration,
                attempts: recentAttempts.length
            });
            
            return {
                allowed: false,
                reason: 'Too many authentication attempts',
                retryAfter: this.config.authLockoutDuration
            };
        }
        
        return { allowed: true };
    }
    
    /**
     * Record authentication attempt
     */
    recordAuthAttempt(clientIP, success = false) {
        const now = Date.now();
        
        if (!success) {
            const attempts = this.authAttempts.get(clientIP) || [];
            attempts.push(now);
            this.authAttempts.set(clientIP, attempts);
        } else {
            // Clear failed attempts on successful auth
            this.authAttempts.delete(clientIP);
            this.authLockouts.delete(clientIP);
        }
    }
    
    /**
     * Validate message content for security
     */
    validateMessage(message) {
        try {
            // Parse JSON
            const parsed = typeof message === 'string' ? JSON.parse(message) : message;
            
            // Check message size
            if (JSON.stringify(parsed).length > 10000) {
                return {
                    valid: false,
                    reason: 'Message too large'
                };
            }
            
            // Check for required fields
            if (!parsed.type) {
                return {
                    valid: false,
                    reason: 'Message missing required type field'
                };
            }
            
            // Whitelist allowed message types
            const allowedTypes = [
                'get_portfolio_summary',
                'get_system_health',
                'get_chart_data',
                'get_bot_status',
                'start_bot',
                'stop_bot',
                'restart_bot',
                'ping'
            ];
            
            if (!allowedTypes.includes(parsed.type)) {
                return {
                    valid: false,
                    reason: \`Unknown message type: \${parsed.type}\`
                };
            }
            
            // Validate parameters based on message type
            if (parsed.type === 'get_chart_data') {
                if (!parsed.chartType || !['portfolio_value', 'profit_loss', 'trades'].includes(parsed.chartType)) {
                    return {
                        valid: false,
                        reason: 'Invalid chart type'
                    };
                }
            }
            
            return { valid: true };
            
        } catch (error) {
            return {
                valid: false,
                reason: 'Invalid JSON format'
            };
        }
    }
    
    /**
     * Cleanup expired data
     */
    startCleanupRoutines() {
        // Clean connection tracking every 5 minutes
        setInterval(() => {
            const now = Date.now();
            const windowMs = ${RATE_LIMIT_CONFIG.CONNECTION_LIMITS.CONNECTION_WINDOW_MS};
            
            // Clean IP connections
            for (const [ip, connections] of this.connectionsByIP) {
                const recent = connections.filter(time => now - time < windowMs);
                if (recent.length === 0) {
                    this.connectionsByIP.delete(ip);
                } else {
                    this.connectionsByIP.set(ip, recent);
                }
            }
            
            // Clean user connections
            for (const [userId, connections] of this.connectionsByUser) {
                const recent = connections.filter(time => now - time < windowMs);
                if (recent.length === 0) {
                    this.connectionsByUser.delete(userId);
                } else {
                    this.connectionsByUser.set(userId, recent);
                }
            }
            
            console.log(\`[SecurityManager] Cleaned connection tracking. Active IPs: \${this.connectionsByIP.size}, Active Users: \${this.connectionsByUser.size}\`);
        }, 300000); // 5 minutes
        
        // Clean message rates every minute
        setInterval(() => {
            const now = Date.now();
            
            for (const [key, rate] of this.messageRates) {
                rate.messages = rate.messages.filter(time => now - time < 60000);
                if (rate.messages.length === 0) {
                    this.messageRates.delete(key);
                }
            }
            
            console.log(\`[SecurityManager] Cleaned message rates. Active keys: \${this.messageRates.size}\`);
        }, 60000); // 1 minute
        
        // Clean auth attempts every hour
        setInterval(() => {
            const now = Date.now();
            
            // Clean auth attempts
            for (const [ip, attempts] of this.authAttempts) {
                const recent = attempts.filter(time => now - time < this.config.authLockoutDuration);
                if (recent.length === 0) {
                    this.authAttempts.delete(ip);
                } else {
                    this.authAttempts.set(ip, recent);
                }
            }
            
            // Clean expired lockouts
            for (const [ip, lockout] of this.authLockouts) {
                if (now >= lockout.until) {
                    this.authLockouts.delete(ip);
                }
            }
            
            console.log(\`[SecurityManager] Cleaned auth data. Active attempts: \${this.authAttempts.size}, Active lockouts: \${this.authLockouts.size}\`);
        }, 3600000); // 1 hour
    }
    
    /**
     * Get security stats
     */
    getSecurityStats() {
        return {
            connections: {
                activeIPs: this.connectionsByIP.size,
                activeUsers: this.connectionsByUser.size
            },
            messaging: {
                activeRateLimits: this.messageRates.size
            },
            authentication: {
                failedAttempts: this.authAttempts.size,
                lockedIPs: this.authLockouts.size
            }
        };
    }
}

module.exports = SecurityManager;
`;

// Write the security manager
fs.writeFileSync(
    path.join(__dirname, '../websocket/SecurityManager.js'),
    RATE_LIMITER_CODE
);

console.log('✅ SecurityManager.js created');

// Integration code for WebSocketManager.js
const INTEGRATION_CODE = `
// Import SecurityManager at the top of WebSocketManager.js
const SecurityManager = require('./SecurityManager');

// Add to WebSocketManager constructor:
this.securityManager = new SecurityManager();

// Add this method to WebSocketManager class:
/**
 * Enhanced connection validation with security checks
 */
validateConnection(ws, req) {
    const clientIP = req.connection?.remoteAddress || req.socket?.remoteAddress || 'unknown';
    
    // Check connection limits
    const connectionCheck = this.securityManager.checkConnectionLimit(clientIP);
    if (!connectionCheck.allowed) {
        console.log(\`[Security] Connection blocked from \${clientIP}: \${connectionCheck.reason}\`);
        ws.close(1008, connectionCheck.reason);
        return false;
    }
    
    // Check authentication limits
    const authCheck = this.securityManager.checkAuthLimit(clientIP);
    if (!authCheck.allowed) {
        console.log(\`[Security] Auth blocked from \${clientIP}: \${authCheck.reason}\`);
        ws.close(1008, authCheck.reason);
        return false;
    }
    
    return true;
}

// Add this method to WebSocketManager class:
/**
 * Enhanced message validation with security checks
 */
validateMessage(ws, message, userId) {
    const clientIP = ws._socket?.remoteAddress || 'unknown';
    
    // Check message rate limits
    const rateCheck = this.securityManager.checkMessageRate(userId, clientIP);
    if (!rateCheck.allowed) {
        console.log(\`[Security] Message rate limit exceeded for \${userId || clientIP}: \${rateCheck.reason}\`);
        ws.send(JSON.stringify({
            type: 'error',
            message: 'Rate limit exceeded',
            retryAfter: rateCheck.retryAfter
        }));
        return false;
    }
    
    // Validate message content
    const contentCheck = this.securityManager.validateMessage(message);
    if (!contentCheck.valid) {
        console.log(\`[Security] Invalid message from \${userId || clientIP}: \${contentCheck.reason}\`);
        ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format',
            details: contentCheck.reason
        }));
        return false;
    }
    
    // Record the message
    this.securityManager.recordMessage(userId, clientIP);
    
    return true;
}

// Add this method to WebSocketManager class:
/**
 * Get security statistics
 */
getSecurityStats() {
    return this.securityManager.getSecurityStats();
}
`;

console.log('📝 Integration code for WebSocketManager.js:');
console.log('   Add SecurityManager import and integration methods');
console.log('   Update handleConnection() to use validateConnection()');
console.log('   Update handleMessage() to use validateMessage()');

console.log('\n✅ Security hardening implementation completed');
console.log('🔒 Features implemented:');
console.log('   • Connection rate limiting per IP/user');
console.log('   • Message rate limiting with burst protection');
console.log('   • Authentication attempt tracking');
console.log('   • IP lockout on repeated failures');
console.log('   • Message content validation');
console.log('   • Automatic cleanup routines');
console.log('   • Security monitoring and stats');

module.exports = { SecurityManager: RATE_LIMITER_CODE, Integration: INTEGRATION_CODE };

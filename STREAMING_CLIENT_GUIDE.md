# Enhanced Streaming Client - Testing Guide

## Overview
The enhanced streaming client (`test-streaming-client.html`) now supports multiple data streams for comprehensive bot and portfolio monitoring.

**Base URL**: `https://freqtrade.crypto-pilot.dev`

## Supported Event Types

### 1. Portfolio Events (`event: portfolio`)
**Description**: Real-time portfolio metrics and bot status updates
**Frequency**: Every 5 seconds
**Data Format**:
```json
{
  "timestamp": 1757428788822,
  "portfolioValue": 19976.25,
  "totalBalance": 19976.25,
  "totalPnL": -23.75,
  "activeBots": 2,
  "botCount": 2,
  "bots": [
    {
      "instanceId": "anshjarvis2003-bot-1",
      "status": [
        {
          "trade_id": 27,
          "pair": "DOT/USD",
          "is_open": true,
          "amount": 23.97391637,
          "open_rate": 4.1712,
          "current_rate": 4.0952,
          "profit_abs": -2.32,
          "profit_ratio": -0.0231
        }
      ],
      "metrics": {
        "totalBalance": 9989.37,
        "totalPnL": -10.63,
        "startingCapital": 10000,
        "openTrades": 1
      }
    }
  ]
}
```

### 2. Chart Events (`event: chart`)
**Description**: Historical portfolio data points for charting
**Frequency**: Every 5 seconds (with historical data)
**Data Format**:
```json
{
  "points": [
    {
      "timestamp": 1757428788822,
      "value": 19976.25,
      "pnl": -23.75
    }
  ],
  "latestValue": 19976.25,
  "count": 50
}
```

### 3. Positions Events (`event: positions`)
**Description**: Live trading positions from all running bots
**Frequency**: Every 5 seconds
**Data Format**:
```json
{
  "positions": [
    {
      "botId": "anshjarvis2003-bot-1",
      "pair": "DOT/USD",
      "side": "long",
      "amount": 23.97391637,
      "entryPrice": 4.1712,
      "currentPrice": 4.0952,
      "pnl": -2.32,
      "pnlPercent": -2.31,
      "status": "open"
    }
  ],
  "count": 2
}
```

### 4. Security Events (`event: security`)
**Description**: Live security price data for market monitoring (sample data for testing)
**Frequency**: Every 5 seconds (rotating pairs: BTC/USD, ETH/USD, DOT/USD)
**Data Format**:
```json
{
  "pair": "BTC/USD",
  "price": 46413.41,
  "timestamp": 1757428423360,
  "exchange": "kraken"
}
```
**Note**: Security events show market price data, while positions show actual bot trades.

## Features

### Visual Components
- **Live Metrics Dashboard**: Portfolio value, P&L, active bots, update count
- **Interactive Chart**: Canvas-based portfolio value chart with historical data
- **Historical Data Chart**: Load and visualize past portfolio performance
- **Trading Positions Table**: Live trading positions from your bots with profit/loss highlighting
- **Security Prices Panel**: Live market price updates for various trading pairs
- **Activity Log**: Real-time event logging with color coding

### Controls
- **Load Test Token**: One-click loading of valid authentication token
- **Connect/Disconnect**: SSE connection management
- **Clear Log**: Activity log cleanup
- **Clear Chart**: Reset live chart data
- **Load Historical Data**: Fetch and plot historical portfolio snapshots
- **Clear Historical Chart**: Reset historical chart display

## Usage

1. **Authentication**: 
   - Click "Load Test Token" for quick setup (loads bot-manager JWT)
   - ⚠️ **IMPORTANT**: Do NOT use Firebase tokens - only bot-manager JWT tokens work
   
2. **Connection**:
   - Click "Connect" to start streaming
   - Events update every 5 seconds
   
3. **Historical Data**:
   - Select data points (50-500)
   - Click "Load Historical Data" (uses same token as streaming)
   
4. **Monitoring**:
   - Watch live metrics in the dashboard
   - See portfolio value changes in the chart
   - Monitor trading positions in the table
   - Check activity log for detailed events

## API Endpoints

### Streaming Endpoint
```
GET https://freqtrade.crypto-pilot.dev/api/stream?token=<jwt_token>
```
**Authentication**: JWT token via query parameter (required for EventSource)
**Content-Type**: `text/event-stream`
**Events**: portfolio, chart, positions, security
**Connection**: Keep-alive, auto-reconnect on disconnect

### Bot Management
```
GET https://freqtrade.crypto-pilot.dev/api/bots
```
**Description**: List all bots for authenticated user
**Authentication**: Bearer token header
**Response**:
```json
{
  "success": true,
  "bots": [
    {
      "instanceId": "anshjarvis2003-bot-1",
      "userId": "Js1Gaz4s...",
      "strategy": "EmaRsiStrategy",
      "port": 8100,
      "containerName": "freqtrade-anshjarvis2003-bot-1",
      "containerStatus": "running",
      "exchange": "kraken",
      "dry_run": true,
      "stake_currency": "USD",
      "stake_amount": 100,
      "max_open_trades": 25
    }
  ]
}
```

```
GET https://freqtrade.crypto-pilot.dev/api/bots/:instanceId
```
**Description**: Get individual bot details
**Authentication**: Bearer token header + ownership verification
**Response**: Single bot object with detailed configuration

```
POST https://freqtrade.crypto-pilot.dev/api/bots/:instanceId/stop
```
**Description**: Stop a running bot container
**Authentication**: Bearer token header + ownership verification
**Response**: `{"success": true, "message": "Bot stopped successfully"}`

```
POST https://freqtrade.crypto-pilot.dev/api/bots/:instanceId/start
```
**Description**: Start a stopped bot container
**Authentication**: Bearer token header + ownership verification
**Response**: `{"success": true, "message": "Bot started successfully"}`

```
DELETE https://freqtrade.crypto-pilot.dev/api/bots/:instanceId
```
**Description**: Delete bot completely (stops container, removes files, cleans Turso DB)
**Authentication**: Bearer token header + ownership verification
**Response**: `{"success": true, "message": "Bot deleted successfully"}`

### Bot Provisioning
```
POST https://freqtrade.crypto-pilot.dev/api/provision
```
**Description**: Create a new bot instance
**Authentication**: Bearer token header
**Body**:
```json
{
  "instanceId": "my-bot-1",  // optional, auto-generated if not provided
  "port": 8102,              // optional, auto-assigned if not provided
  "apiUsername": "admin",    // optional, defaults to env var
  "apiPassword": "password"  // optional, defaults to env var
}
```
**Response**: Bot creation details with instanceId, port, containerName, strategy

### Historical Data
```
GET https://freqtrade.crypto-pilot.dev/api/portfolio/history?token=<jwt_token>
```
**Description**: Get historical portfolio snapshots
**Authentication**: JWT token via query parameter or Bearer header
**Query Parameters**:
- `limit`: Number of snapshots to return (default: 100, max: 1000)
- `from`: Start timestamp (Unix milliseconds)
- `to`: End timestamp (Unix milliseconds)
**Response**:
```json
{
  "success": true,
  "snapshots": [
    {
      "timestamp": 1757428788822,
      "portfolioValue": 19976.25,
      "totalPnL": -23.75,
      "activeBots": 2,
      "botCount": 2
    }
  ],
  "count": 50,
  "period": {
    "from": 1757428788822,
    "to": 1757428888822
  }
}
```

### Health Check
```
GET https://freqtrade.crypto-pilot.dev/api/health
```
**Description**: Service health status
**Authentication**: None required
**Response**:
```json
{
  "ok": true,
  "status": "ok",
  "service": "bot-manager",
  "uptime": 3600.5,
  "timestamp": "2025-09-09T14:35:48.000Z"
}
```

## Testing Scenarios

1. **Portfolio Monitoring**: Watch real-time portfolio value changes
2. **Position Tracking**: Monitor open trades and P&L
3. **Chart Visualization**: See historical portfolio performance
4. **Bot Management**: Start/stop bots and see status updates
5. **Connection Resilience**: Test reconnection after network issues

## Troubleshooting

### Common Issues

**❌ 401 Unauthorized Error on Historical Data**
- **Cause**: Using Firebase token instead of bot-manager JWT
- **Solution**: Click "Load Test Token" button, don't manually paste Firebase tokens
- **Verify**: Bot-manager JWTs start with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6...`

**❌ Stream Connection Failed**  
- **Cause**: Invalid or expired token
- **Solution**: Click "Load Test Token" to get fresh credentials

**❌ No Historical Data**
- **Cause**: No portfolio snapshots saved yet
- **Solution**: Let bots run for a few minutes to generate snapshot data

**❌ Only DOT Positions Shown**
- **Expected**: This is correct - your bots only trade DOT/USD
- **Security Prices**: BTC/ETH prices shown separately for market monitoring



#!/usr/bin/env node

/**
 * Portfolio Snapshot System Test Suite
 * 
 * Comprehensive testing for the portfolio snapshot functionality:
 * 1. Unit tests for snapshot creation and management
 * 2. Integration tests with WebSocket system
 * 3. File system testing for persistence
 * 4. Performance testing for large datasets
 * 5. Mock data generation for testing scenarios
 */

const fs = require('fs-extra');
const path = require('path');
const WebSocket = require('ws');

class PortfolioSnapshotTester {
    constructor() {
        this.testUserId = 'test-user-123';
        this.testDataDir = '/tmp/portfolio-test-data';
        this.mockSnapshots = [];
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        };
    }

    async runAllTests() {
        console.log('🧪 Portfolio Snapshot System Test Suite\n');

        try {
            await this.setupTestEnvironment();

            // Run test suites
            await this.testSnapshotCreation();
            await this.testSnapshotPersistence();
            await this.testSnapshotCompression();
            await this.testGrowthAnalysis();
            await this.testDataExport();
            await this.testFileSystemOperations();
            await this.testPerformance();

            this.printResults();
        } catch (error) {
            console.error('❌ Test suite failed:', error);
        } finally {
            await this.cleanup();
        }
    }

    async setupTestEnvironment() {
        console.log('🔧 Setting up test environment...');

        // Create test directory
        await fs.ensureDir(this.testDataDir);

        // Generate mock portfolio data
        this.generateMockData();

        console.log('✅ Test environment ready\n');
    }

    generateMockData() {
        const baseTime = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days ago
        const baseValue = 10000;

        for (let i = 0; i < 1000; i++) {
            const timestamp = baseTime + (i * 60 * 60 * 1000); // Hourly snapshots
            const variation = (Math.random() - 0.5) * 500; // +/- $250 variation
            const portfolioValue = baseValue + (i * 10) + variation; // Growing trend with noise

            this.mockSnapshots.push({
                timestamp,
                portfolioValue,
                totalBalance: portfolioValue * 0.8,
                totalPnL: portfolioValue * 0.2,
                botCount: Math.floor(Math.random() * 5) + 1,
                activeBots: Math.floor(Math.random() * 4) + 1,
                dailyPnL: variation,
                weeklyPnL: variation * 7,
                monthlyPnL: variation * 30,
                bots: [
                    {
                        instanceId: `bot-${Math.floor(Math.random() * 3) + 1}`,
                        balance: portfolioValue / 3,
                        pnl: variation / 3,
                        openTrades: Math.floor(Math.random() * 5)
                    }
                ],
                riskMetrics: {
                    portfolioRisk: Math.abs(variation),
                    maxDrawdown: Math.abs(variation) * 2,
                    sharpeRatio: Math.random() * 3
                },
                sessionInfo: {
                    wsConnected: true,
                    userAgent: 'Test-Client',
                    ipAddress: '127.0.0.1'
                }
            });
        }
    }

    async testSnapshotCreation() {
        console.log('📊 Testing snapshot creation...');

        try {
            // Test 1: Create UserBotService instance
            const UserBotService = require('./websocket/UserBotService');
            const mockMainService = {
                BOT_BASE_DIR: this.testDataDir
            };
            const mockWsManager = {
                broadcastToUser: () => { }
            };

            const service = new UserBotService(this.testUserId, mockWsManager, mockMainService);

            // Wait for initialization
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test 2: Add mock snapshots
            for (let i = 0; i < 10; i++) {
                await service.addPortfolioSnapshot(this.mockSnapshots[i]);
                await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
            }

            this.recordTest('Snapshot Creation', true, 'Successfully created 10 snapshots');

            // Test 3: Check file exists
            const snapshotFile = path.join(this.testDataDir, this.testUserId, 'portfolio_snapshots.json');
            const fileExists = await fs.pathExists(snapshotFile);
            this.recordTest('Snapshot File Creation', fileExists, fileExists ? 'File created' : 'File not found');

            // Test 4: Verify snapshot data
            if (fileExists) {
                const data = await fs.readJson(snapshotFile);
                const hasSnapshots = data.snapshots && data.snapshots.length > 0;
                const hasMetadata = data.metadata && data.metadata.totalSnapshots > 0;

                this.recordTest('Snapshot Data Structure', hasSnapshots && hasMetadata,
                    `Snapshots: ${data.snapshots?.length || 0}, Metadata valid: ${!!hasMetadata}`);
            }

        } catch (error) {
            this.recordTest('Snapshot Creation', false, error.message);
        }
    }

    async testSnapshotPersistence() {
        console.log('💾 Testing snapshot persistence...');

        try {
            const snapshotFile = path.join(this.testDataDir, this.testUserId, 'portfolio_snapshots.json');

            if (await fs.pathExists(snapshotFile)) {
                const data = await fs.readJson(snapshotFile);

                // Test data integrity
                const hasCorrectStructure = data.metadata && data.snapshots && data.version;
                this.recordTest('Data Structure Integrity', hasCorrectStructure,
                    `Structure valid: ${hasCorrectStructure}`);

                // Test timestamp ordering
                const timestamps = data.snapshots.map(s => s.timestamp);
                const isOrdered = timestamps.every((t, i) => i === 0 || t >= timestamps[i - 1]);
                this.recordTest('Timestamp Ordering', isOrdered,
                    `Timestamps properly ordered: ${isOrdered}`);

                // Test metadata consistency
                const metadataMatch = data.metadata.totalSnapshots === data.snapshots.length;
                this.recordTest('Metadata Consistency', metadataMatch,
                    `Metadata count: ${data.metadata.totalSnapshots}, Actual: ${data.snapshots.length}`);

            } else {
                this.recordTest('Snapshot Persistence', false, 'Snapshot file not found');
            }

        } catch (error) {
            this.recordTest('Snapshot Persistence', false, error.message);
        }
    }

    async testSnapshotCompression() {
        console.log('🗜️ Testing snapshot compression...');

        try {
            // Create a large number of snapshots to trigger compression
            const UserBotService = require('./websocket/UserBotService');
            const mockMainService = {
                BOT_BASE_DIR: this.testDataDir
            };
            const mockWsManager = {
                broadcastToUser: () => { }
            };

            const service = new UserBotService(`${this.testUserId}-compression`, mockWsManager, mockMainService);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Add enough snapshots to trigger compression (>1000)
            console.log('   Adding 1100 snapshots to trigger compression...');
            for (let i = 0; i < 1100; i++) {
                await service.addPortfolioSnapshot(this.mockSnapshots[i % this.mockSnapshots.length]);
                if (i % 100 === 0) {
                    console.log(`   Progress: ${i}/1100`);
                }
            }

            // Check if compression occurred
            const userDir = path.join(this.testDataDir, `${this.testUserId}-compression`);
            const files = await fs.readdir(userDir);
            const archiveFiles = files.filter(f => f.includes('portfolio_archive_'));

            this.recordTest('Compression Triggered', archiveFiles.length > 0,
                `Archive files created: ${archiveFiles.length}`);

        } catch (error) {
            this.recordTest('Snapshot Compression', false, error.message);
        }
    }

    async testGrowthAnalysis() {
        console.log('📈 Testing growth analysis...');

        try {
            const UserBotService = require('./websocket/UserBotService');
            const mockMainService = {
                BOT_BASE_DIR: this.testDataDir
            };
            const mockWsManager = {
                broadcastToUser: () => { }
            };

            const service = new UserBotService(`${this.testUserId}-growth`, mockWsManager, mockMainService);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Add snapshots with known growth pattern
            const growthSnapshots = [
                { ...this.mockSnapshots[0], portfolioValue: 10000, timestamp: Date.now() - 10000 },
                { ...this.mockSnapshots[0], portfolioValue: 11000, timestamp: Date.now() - 5000 },
                { ...this.mockSnapshots[0], portfolioValue: 12000, timestamp: Date.now() }
            ];

            for (const snapshot of growthSnapshots) {
                await service.addPortfolioSnapshot(snapshot);
            }

            // Test growth analysis
            const analysis = service.getPortfolioGrowthAnalysis();

            const hasValidGrowth = analysis.totalGrowth === 2000;
            const hasValidPercentage = Math.abs(analysis.growthPercentage - 20) < 0.1;

            this.recordTest('Growth Calculation', hasValidGrowth,
                `Total growth: ${analysis.totalGrowth} (expected: 2000)`);
            this.recordTest('Growth Percentage', hasValidPercentage,
                `Growth percentage: ${analysis.growthPercentage}% (expected: ~20%)`);

        } catch (error) {
            this.recordTest('Growth Analysis', false, error.message);
        }
    }

    async testDataExport() {
        console.log('📤 Testing data export...');

        try {
            const UserBotService = require('./websocket/UserBotService');
            const mockMainService = {
                BOT_BASE_DIR: this.testDataDir
            };
            const mockWsManager = {
                broadcastToUser: () => { }
            };

            const service = new UserBotService(`${this.testUserId}-export`, mockWsManager, mockMainService);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Add some test data
            for (let i = 0; i < 5; i++) {
                await service.addPortfolioSnapshot(this.mockSnapshots[i]);
            }

            // Test JSON export
            const jsonExportPath = await service.exportPortfolioHistory('json');
            const jsonExists = await fs.pathExists(jsonExportPath);
            this.recordTest('JSON Export', jsonExists, `JSON exported to: ${jsonExportPath}`);

            // Test CSV export
            const csvExportPath = await service.exportPortfolioHistory('csv');
            const csvExists = await fs.pathExists(csvExportPath);
            this.recordTest('CSV Export', csvExists, `CSV exported to: ${csvExportPath}`);

            // Verify JSON export content
            if (jsonExists) {
                const exportData = await fs.readJson(jsonExportPath);
                const hasRequiredFields = exportData.snapshots && exportData.metadata && exportData.growthAnalysis;
                this.recordTest('Export Data Integrity', hasRequiredFields,
                    `Export contains all required fields: ${hasRequiredFields}`);
            }

        } catch (error) {
            this.recordTest('Data Export', false, error.message);
        }
    }

    async testFileSystemOperations() {
        console.log('📁 Testing file system operations...');

        try {
            // Test backup creation
            const backupTestDir = path.join(this.testDataDir, `${this.testUserId}-backup`);
            await fs.ensureDir(backupTestDir);

            const testData = {
                metadata: { totalSnapshots: 1 },
                snapshots: [this.mockSnapshots[0]],
                version: '1.0'
            };

            const snapshotFile = path.join(backupTestDir, 'portfolio_snapshots.json');
            await fs.writeJson(snapshotFile, testData);

            // Create backup
            const backupFile = path.join(backupTestDir, `portfolio_backup_${new Date().toISOString().split('T')[0]}.json`);
            await fs.writeJson(backupFile, testData);

            const backupExists = await fs.pathExists(backupFile);
            this.recordTest('Backup Creation', backupExists, `Backup created: ${backupExists}`);

            // Test file size management
            const stats = await fs.stat(snapshotFile);
            this.recordTest('File Size Reasonable', stats.size < 1024 * 1024,
                `File size: ${stats.size} bytes`);

        } catch (error) {
            this.recordTest('File System Operations', false, error.message);
        }
    }

    async testPerformance() {
        console.log('⚡ Testing performance...');

        try {
            const UserBotService = require('./websocket/UserBotService');
            const mockMainService = {
                BOT_BASE_DIR: this.testDataDir
            };
            const mockWsManager = {
                broadcastToUser: () => { }
            };

            const service = new UserBotService(`${this.testUserId}-perf`, mockWsManager, mockMainService);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Test snapshot addition performance
            const startTime = Date.now();

            for (let i = 0; i < 100; i++) {
                await service.addPortfolioSnapshot(this.mockSnapshots[i % this.mockSnapshots.length]);
            }

            const duration = Date.now() - startTime;
            const avgTimePerSnapshot = duration / 100;

            this.recordTest('Snapshot Addition Performance', avgTimePerSnapshot < 50,
                `Average time per snapshot: ${avgTimePerSnapshot.toFixed(2)}ms`);

            // Test query performance
            const queryStart = Date.now();
            const history = service.getPortfolioHistory({ limit: 50 });
            const queryDuration = Date.now() - queryStart;

            this.recordTest('Query Performance', queryDuration < 100,
                `Query time: ${queryDuration}ms for ${history.length} records`);

        } catch (error) {
            this.recordTest('Performance Testing', false, error.message);
        }
    }

    recordTest(testName, passed, details) {
        const result = {
            name: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        };

        this.results.tests.push(result);

        if (passed) {
            this.results.passed++;
            console.log(`   ✅ ${testName}: ${details}`);
        } else {
            this.results.failed++;
            console.log(`   ❌ ${testName}: ${details}`);
        }
    }

    printResults() {
        console.log('\n📋 Test Results Summary:');
        console.log('═'.repeat(50));
        console.log(`Total Tests: ${this.results.tests.length}`);
        console.log(`Passed: ${this.results.passed} ✅`);
        console.log(`Failed: ${this.results.failed} ❌`);
        console.log(`Success Rate: ${((this.results.passed / this.results.tests.length) * 100).toFixed(1)}%`);

        if (this.results.failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.tests
                .filter(t => !t.passed)
                .forEach(test => {
                    console.log(`   • ${test.name}: ${test.details}`);
                });
        }

        console.log('\n🏁 Test suite completed!');
    }

    async cleanup() {
        try {
            // Clean up test files
            if (await fs.pathExists(this.testDataDir)) {
                await fs.remove(this.testDataDir);
                console.log('🧹 Test environment cleaned up');
            }
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }
}

// WebSocket Integration Test
class WebSocketIntegrationTest {
    constructor() {
        this.ws = null;
        this.testUserId = 'Js1Gaz4sMPPiDNgFbmAgDFLe4je2';
        this.messageId = 1;
        this.responses = new Map();
    }

    async runWebSocketTests() {
        console.log('\n🌐 WebSocket Integration Tests');
        console.log('═'.repeat(50));

        try {
            await this.connectToWebSocket();
            await this.testPortfolioHistoryMessage();
            await this.testGrowthAnalysisMessage();
            await this.testExportMessage();
            this.disconnect();
        } catch (error) {
            console.error('❌ WebSocket tests failed:', error.message);
            console.log('\nℹ️  To run WebSocket tests, ensure:');
            console.log('   1. Bot manager server is running (npm start)');
            console.log('   2. Valid authentication token is available');
            console.log('   3. User has active bot instances');
        }
    }

    async connectToWebSocket() {
        // This would require a real JWT token and running server
        console.log('🔌 WebSocket connection test requires running server');
        throw new Error('WebSocket server not available for testing');
    }

    async testPortfolioHistoryMessage() {
        const message = {
            type: 'get_portfolio_history',
            data: { limit: 10, includeMetadata: true }
        };
        console.log('📨 Would test portfolio history message:', JSON.stringify(message, null, 2));
    }

    async testGrowthAnalysisMessage() {
        const message = { type: 'get_portfolio_growth' };
        console.log('📨 Would test growth analysis message:', JSON.stringify(message, null, 2));
    }

    async testExportMessage() {
        const message = {
            type: 'export_portfolio_history',
            data: { format: 'json' }
        };
        console.log('📨 Would test export message:', JSON.stringify(message, null, 2));
    }

    disconnect() {
        console.log('👋 WebSocket disconnected');
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);

    if (args.includes('--unit') || args.length === 0) {
        const tester = new PortfolioSnapshotTester();
        await tester.runAllTests();
    }

    if (args.includes('--websocket') || args.length === 0) {
        const wsTest = new WebSocketIntegrationTest();
        await wsTest.runWebSocketTests();
    }

    if (args.includes('--help')) {
        console.log('\n📖 Portfolio Snapshot Test Suite Usage:');
        console.log('   node test-portfolio-snapshots-comprehensive.js [options]');
        console.log('\nOptions:');
        console.log('   --unit       Run unit tests only');
        console.log('   --websocket  Run WebSocket integration tests only');
        console.log('   --help       Show this help message');
        console.log('\nRun without options to execute all tests.');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { PortfolioSnapshotTester, WebSocketIntegrationTest };

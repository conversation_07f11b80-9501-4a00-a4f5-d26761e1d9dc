# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs and history files
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.bash_history
.python_history
.lesshst
.wget-hsts

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Shell scripts (unless specifically needed)
*.sh

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# Database files
*.sqlite
*.sqlite3
*.db

# Turso tokens and credentials
.turso-tokens.json
serviceAccountKey.json

# Bot instance data
freqtrade-instances/
freqtrade-shared/

# Service files (these should be managed separately)
*.service

# Backup files
*.bak
*.backup
*.old

# Environment variables
.env
.env.local
.env.production

# Firebase service account
serviceAccountKey.json

# FreqTrade logs and databases
freqtrade-instances/**/user_data/logs/*.log
freqtrade-instances/**/user_data/*.sqlite
freqtrade-instances/**/user_data/*.db

# SQLite databases
*.sqlite
*.db

# Logs
logs/
*.log

# Docker volumes and generated data
freqtrade-instances/**/user_data/data/
freqtrade-instances/**/user_data/backtest_results/
freqtrade-instances/**/user_data/plot/

# Temporary files
tmp/
temp/
.cache/

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Coverage reports
coverage/

# ta-lib build artifacts
ta-lib/bin
ta-lib/lib
ta-lib/include
ta-lib/libtool
ta-lib/config.log
ta-lib/config.status

# Sync logs
sync-strategies.log
strategy-sync-m.log
sync_main_shared.log

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Bash scripts (unless specifically needed)
*.sh

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# Database files
*.sqlite
*.sqlite3
*.db

# Turso tokens and credentials
.turso-tokens.json
serviceAccountKey.json

# Bot instance data
freqtrade-instances/
freqtrade-shared/

# Service files (these should be managed separately)
*.service

# Backup files
*.bak
*.backup
*.old
freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/portfolio_snapshots.json
freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/portfolio_snapshots.json
freqtrade-instances/Js1Gaz4sMPPiDNgFbmAgDFLe4je2/portfolio_snapshots.json

# Bot Manager API Documentation
## Complete Backend Integration Guide

### Table of Contents
1. [System Overview](#system-overview)
2. [Authentication](#authentication)
3. [Live Streaming System](#live-streaming-system)
4. [REST API Endpoints](#rest-api-endpoints)
5. [Data Models](#data-models)
6. [Error Handling](#error-handling)
7. [Production Features](#production-features)
8. [Frontend Integration Examples](#frontend-integration-examples)
9. [Performance Considerations](#performance-considerations)
10. [Security & Rate Limiting](#security--rate-limiting)

---

## System Overview

### Architecture
- **Service**: Single Node.js Express server on port 3001
- **Production Domain**: `https://freqtrade.crypto-pilot.dev`
- **Development**: `http://localhost:3001`
- **Protocols**: REST API + SSE (Server-Sent Events)
- **Authentication**: Firebase JWT or local JWT
- **Database**: SQLite per bot instance
- **Container Management**: Docker integration
- **Real-time Features**: SSE-based live updates via `/api/stream`
- **Security**: TLS/SSL for production

### Core Features Implemented
✅ **Phase 1**: SSE infrastructure with authentication  
✅ **Phase 2**: Multi-layer caching and database monitoring  
✅ **Phase 3**: Portfolio aggregation with time-series data  
✅ **Phase 4**: Advanced bot management and chart data  
✅ **Phase 5**: Production monitoring and security hardening  
✅ **Live Streaming**: Real-time subscription-based updates

### Live Streaming Capabilities
🔴 **Portfolio Updates**: Total value, P&L, metrics (every 60 seconds)
🔴 **Bot Metrics**: Individual bot performance (every 30 seconds)
🔴 **Time-Series Data**: Chart data points (every 5 minutes)
🔴 **Trade Alerts**: New trades and completions (real-time)
🔴 **Bot Status**: Status changes and events (real-time)
🔴 **System Health**: Performance monitoring (every 60 seconds)

### Real-time Bot Discovery
🔄 **Automatic Discovery**: Detects new bots without service restart
🔄 **Periodic Refresh**: Bot discovery runs every 2 minutes
🔄 **Connection-triggered**: New SSE connections trigger bot discovery
🔄 **Real-time Accuracy**: Always shows current bot count (e.g., "3/3 bots")

### Subscription Features
- **Channel Management**: Subscribe/unsubscribe to specific data streams
- **Immediate Updates**: New subscribers get current data instantly
- **Automatic Streaming**: Configured intervals ensure consistent updates
- **Real-time Alerts**: Critical events delivered immediately
- **Performance Monitoring**: System health and metrics tracking
- **Dynamic Bot Detection**: Automatically discovers newly provisioned bots  

---

## Authentication

### Token Requirements
All requests require authentication.

#### REST/SSE Authentication
```http
Authorization: Bearer <token>
```
Or pass token as a query param for SSE:
```
GET /api/stream?token=<token>
```

---

## Live Streaming System

SSE endpoint providing periodic aggregated portfolio updates per user:
- Endpoint: `GET /api/stream?token=<token>`
- Event type: `portfolio`
- Interval: ~5s (configurable)

Client example:
```html
<script>
  const es = new EventSource('/api/stream?token=YOUR_TOKEN');
  es.addEventListener('portfolio', (e) => console.log(JSON.parse(e.data)));
</script>
```

---

## REST API Endpoints

### Base URLs
- **Production**: `https://freqtrade.crypto-pilot.dev`
- **Development**: `http://localhost:3001`

### 1. Bot Management

#### List User Bots
```http
GET /api/bots
```
**Response:**
```json
{
  "success": true,
  "bots": [
    {
      "instanceId": "user123-bot1",
      "status": "running",
      "config": { ... },
      "stats": { ... }
    }
  ]
}
```

#### Get Bot Details
```http
GET /api/bot/:instanceId
```
**Response:**
```json
{
  "success": true,
  "bot": {
    "instanceId": "user123-bot1",
    "status": "running",
    "balance": 1000.50,
    "totalPnL": 125.75,
    "openTrades": 3,
    "config": { ... },
    "performance": { ... }
  }
}
```

#### Start Bot
```http
POST /api/bot/:instanceId/start
```

#### Stop Bot
```http
POST /api/bot/:instanceId/stop
```

#### Update Bot Configuration
```http
PUT /api/bot/:instanceId/config
Content-Type: application/json

{
  "max_open_trades": 5,
  "stake_amount": 100,
  "dry_run": false
}
```

### 2. Portfolio Management

#### Get Portfolio Summary
```http
GET /api/portfolio/summary
```
**Response:**
```json
{
  "success": true,
  "portfolio": {
    "totalBalance": 39114.73,
    "totalPnL": -7.26,
    "portfolioValue": 39107.47,
    "botCount": 4,
    "activeBots": 4,
    "dailyPnL": 29328.61,
    "weeklyPnL": 29328.61,
    "monthlyPnL": 29328.61,
    "totalStartingBalance": 40000.00,
    "overallPnL": -885.27,
    "overallPnLPercentage": -2.21,
    "totalOpenTrades": 10,
    "aggregatedOpenPositions": [
      {
        "id": 1,
        "exchange": "kraken",
        "pair": "BTC/USD",
        "is_open": 1,
        "amount": 0.00084817,
        "stake_amount": 99.999243,
        "open_rate": 117900,
        "close_rate": null,
        "profit": 0,
        "profit_ratio": null,
        "open_date": "2025-07-18 22:45:08.491497",
        "close_date": null,
        "exit_reason": null,
        "strategy": "EmaRsiStrategy",
        "botId": "anshjarvis2003-bot-1",
        "botInstance": "anshjarvis2003-bot-1"
      }
    ],
    "totalOpenPositions": 10,
    "performanceMetrics": { ... }
  }
}
```

#### Get Portfolio History
```http
GET /api/portfolio/history?startDate=2024-01-01&endDate=2024-12-31&limit=100
```

#### Export Portfolio Data
```http
GET /api/portfolio/export?format=json
GET /api/portfolio/export?format=csv
```

### 3. Portfolio Chart Data

#### Get Portfolio Chart Data
```http
GET /api/portfolio/chart/:timeframe
```

**Description**: Retrieves historical portfolio performance data for charting and visualization.

**Supported Timeframes**: 
- `1H` - Last 1 hour of data
- `4H` - Last 4 hours of data  
- `24H` - Last 24 hours of data
- `7D` - Last 7 days of data
- `30D` - Last 30 days of data

**Authentication**: Required (Bearer JWT token)

**Request Example:**
```http
GET /api/portfolio/chart/24H
Authorization: Bearer <jwt_token>
```

**Response Format:**
```json
{
  "success": true,
  "timeframe": "24H",
  "data": [
    {
      "timestamp": 1752878442145,
      "date": "2025-07-18T22:40:42.145Z",
      "portfolioValue": 38944.765358124394,
      "totalPnL": -3.3213602600000005,
      "totalBalance": 38948.086718384395,
      "botCount": 4
    }
  ],
  "metadata": {
    "startValue": 9993.930903712258,
    "endValue": 38944.765358124394,
    "minValue": 0,
    "maxValue": 651325.********,
    "totalReturn": 289.*************,
    "dataPoints": 202
  },
  "movingAverages": null
}
```

**Response Fields:**
- `success`: Boolean indicating request success
- `timeframe`: Requested timeframe identifier
- `data[]`: Array of time-series data points
  - `timestamp`: Unix timestamp in milliseconds
  - `date`: ISO 8601 formatted date string
  - `portfolioValue`: Total portfolio value at this time
  - `totalPnL`: Profit/Loss at this time
  - `totalBalance`: Total account balance at this time
  - `botCount`: Number of active bots at this time
- `metadata`: Statistical information about the dataset
  - `startValue`: Portfolio value at beginning of timeframe
  - `endValue`: Portfolio value at end of timeframe
  - `minValue`: Minimum portfolio value in timeframe
  - `maxValue`: Maximum portfolio value in timeframe
  - `totalReturn`: Percentage return over the timeframe
  - `dataPoints`: Total number of data points returned
- `movingAverages`: Currently null, reserved for future moving average data

**Error Response:**
```json
{
  "success": false,
  "message": "Invalid timeframe. Must be one of: 1H, 4H, 24H, 7D, 30D"
}
```

**Data Source**: Portfolio snapshots are captured every 2-5 minutes and filtered based on the requested timeframe.

### 4. Enhanced Portfolio Analytics

#### Overall P&L Calculation
The system now provides comprehensive P&L calculation based on starting balances vs. current balances:

**Key Metrics:**
- **Starting Balance Tracking**: Each bot's initial balance (typically 10,000 USD) is tracked from creation
- **Overall P&L**: Current total balance minus total starting balance across all bots  
- **Overall P&L Percentage**: Percentage return based on initial investment

**Example Calculation:**
```javascript
// 4 bots with 10,000 USD each = 40,000 USD starting balance
totalStartingBalance = 40000.00

// Current balances: bot1=9,800 + bot2=9,850 + bot3=9,900 + bot4=9,565 = 39,115 USD
totalCurrentBalance = 39115.00

// Overall P&L = 39,115 - 40,000 = -885 USD (-2.21%)
overallPnL = -885.00
overallPnLPercentage = -2.21
```

#### Aggregated Open Positions
All open trades from all bots are aggregated into a single comprehensive view:

**Features:**
- **Complete Position List**: All open trades across all active bots
- **Bot Identification**: Each position includes `botId` and `botInstance` fields
- **Real-time Updates**: Automatically updated as positions are opened/closed
- **Comprehensive Details**: Full trade information including pair, amounts, rates, strategies

**Position Structure:**
```json
{
  "id": 1,
  "exchange": "kraken",
  "pair": "BTC/USD",
  "amount": 0.00084817,
  "stake_amount": 99.999243,
  "open_rate": 117900,
  "strategy": "EmaRsiStrategy",
  "botId": "anshjarvis2003-bot-1",
  "botInstance": "anshjarvis2003-bot-1",
  "open_date": "2025-07-18 22:45:08.491497"
}
```

**Benefits:**
- **Portfolio-Wide View**: See all positions across all bots in one place
- **Risk Management**: Understand total exposure per trading pair
- **Performance Analysis**: Track which bots and strategies are most active
- **Real-time Monitoring**: Live updates via WebSocket streaming

### 5. Time-Series Data

#### Get Portfolio Chart Data
```http
GET /api/portfolio/chart/:timeframe
```

**Description**: Retrieves historical portfolio performance data for charting and visualization.

**Supported Timeframes**: 
- `1H` - Last 1 hour of data
- `4H` - Last 4 hours of data  
- `24H` - Last 24 hours of data
- `7D` - Last 7 days of data
- `30D` - Last 30 days of data

**Authentication**: Required (Bearer JWT token)

**Request Example:**
```http
GET /api/portfolio/chart/24H
Authorization: Bearer <jwt_token>
```

**Response Format:**
```json
{
  "success": true,
  "timeframe": "24H",
  "data": [
    {
      "timestamp": 1752878442145,
      "date": "2025-07-18T22:40:42.145Z",
      "portfolioValue": 38944.765358124394,
      "totalPnL": -3.3213602600000005,
      "totalBalance": 38948.086718384395,
      "botCount": 4
    }
  ],
  "metadata": {
    "startValue": 9993.930903712258,
    "endValue": 38944.765358124394,
    "minValue": 0,
    "maxValue": 651325.********,
    "totalReturn": 289.*************,
    "dataPoints": 202
  },
  "movingAverages": null
}
```

**Response Fields:**
- `success`: Boolean indicating request success
- `timeframe`: Requested timeframe identifier
- `data[]`: Array of time-series data points
  - `timestamp`: Unix timestamp in milliseconds
  - `date`: ISO 8601 formatted date string
  - `portfolioValue`: Total portfolio value at this time
  - `totalPnL`: Profit/Loss at this time
  - `totalBalance`: Total account balance at this time
  - `botCount`: Number of active bots at this time
- `metadata`: Statistical information about the dataset
  - `startValue`: Portfolio value at beginning of timeframe
  - `endValue`: Portfolio value at end of timeframe
  - `minValue`: Minimum portfolio value in timeframe
  - `maxValue`: Maximum portfolio value in timeframe
  - `totalReturn`: Percentage return over the timeframe
  - `dataPoints`: Total number of data points returned
- `movingAverages`: Currently null, reserved for future moving average data

**Error Response:**
```json
{
  "success": false,
  "message": "Invalid timeframe. Must be one of: 1H, 4H, 24H, 7D, 30D"
}
```

**Data Source**: Portfolio snapshots are captured every 2-5 minutes and filtered based on the requested timeframe.

### 5. System Health

#### Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "healthy",
  "timestamp": *************,
  "uptime": "2h 30m",
  "activeBots": 4,
  "activeUsers": 2,
  "dbConnections": 4,
  "cacheStatus": "healthy"
}
```

#### Metrics
```http
GET /metrics
```

#### Ready Check
```http
GET /ready
```

---

## WebSocket API

### Connection Management

#### Establishing Connection
```javascript
// Production (Secure WebSocket over HTTPS)
const ws = new WebSocket('wss://freqtrade.crypto-pilot.dev/ws?token=<jwt_token>');

// Development
const ws = new WebSocket('ws://localhost:3001/ws?token=<jwt_token>');

ws.onopen = function() {
  console.log('Connected to bot manager');
};

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  handleMessage(message);
};
```

#### Message Protocol
All messages use JSON format with the following structure:
```json
{
  "type": "message_type",
  "data": { ... },
  "timestamp": *************,
  "correlationId": "unique_id"
}
```

### 2. Outbound Message Types (Client → Server)

#### Portfolio Summary Request
```json
{
  "type": "get_portfolio_summary",
  "data": {
    "includeTimeSeries": true,
    "includeSnapshots": false
  }
}
```

#### Time-Series Data Request
```json
{
  "type": "get_timeseries_data",
  "data": {
    "timeframe": "24H"
  }
}
```

#### Portfolio History Request
```json
{
  "type": "get_portfolio_history",
  "data": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31",
    "limit": 100,
    "includeMetadata": true
  }
}
```

#### Bot Action Request
```json
{
  "type": "bot_action",
  "data": {
    "action": "start|stop|restart",
    "botId": "user123-bot1"
  }
}
```

#### Bot Configuration Update
```json
{
  "type": "update_bot_config",
  "data": {
    "botId": "user123-bot1",
    "config": {
      "max_open_trades": 5,
      "stake_amount": 100
    }
  }
}
```

#### Chart Data Request
```json
{
  "type": "get_chart_data",
  "data": {
    "chartType": "portfolio_value",
    "parameters": {
      "timeframe": "24H"
    }
  }
}
```

**Available Chart Types:**
- `portfolio_value`: Portfolio value over time
- `pnl_breakdown`: P&L breakdown analysis
- `bot_performance`: Individual bot performance comparison
- `risk_analysis`: Risk metrics over time
- `trading_activity`: Trading activity patterns
- `correlation_matrix`: Bot correlation analysis

**Supported Timeframes:** `1H`, `4H`, `24H`, `7D`, `30D`

**Response:**
```json
{
  "type": "chart_data_response",
  "data": {
    "chartType": "portfolio_value",
    "timeframe": "24H",
    "data": [
      {
        "timestamp": 1752878442145,
        "date": "2025-07-18T22:40:42.145Z",
        "portfolioValue": 38944.765358124394,
        "totalPnL": -3.3213602600000005,
        "totalBalance": 38948.086718384395,
        "botCount": 4
      }
    ],
    "metadata": {
      "startValue": 9993.930903712258,
      "endValue": 38944.765358124394,
      "minValue": 0,
      "maxValue": 651325.********,
      "totalReturn": 289.*************,
      "dataPoints": 202
    }
  }
}
```

#### Subscribe to Updates
```json
{
  "type": "subscribe_updates",
  "data": {
    "channels": ["portfolio", "bot_status", "trades"]
  }
}
```

**Available Subscription Channels:**
- `portfolio`: Portfolio value, P&L, metrics (updates every 60 seconds)
- `bot_metrics`: Individual bot metrics (updates every 30 seconds)  
- `timeseries`: New time-series data points (updates every 5 minutes)
- `trade_alerts`: New trades, trade completions (real-time)
- `bot_status`: Bot status changes (real-time)
- `system_health`: System health updates (updates every 60 seconds)

**Response:**
```json
{
  "type": "subscription_confirmed",
  "data": {
    "subscribedChannels": ["portfolio", "bot_metrics", "timeseries"],
    "availableChannels": ["portfolio", "bot_metrics", "timeseries", "trade_alerts", "bot_status", "system_health"],
    "streamingIntervals": {
      "portfolio": "60 seconds",
      "bot_metrics": "30 seconds", 
      "timeseries": "5 minutes",
      "trade_alerts": "real-time",
      "bot_status": "real-time",
      "system_health": "60 seconds"
    },
    "features": ["portfolio_aggregation", "real_time_updates", "bot_management", "dynamic_discovery"]
  }
}
```

#### Unsubscribe from Updates
```json
{
  "type": "unsubscribe_updates", 
  "data": {
    "channels": ["portfolio", "bot_status"]
  }
}
```

### 3. Streaming Message Types (Server → Client)

#### Portfolio Updates (every 60 seconds)
```json
{
  "type": "portfolio_stream_update",
  "timestamp": "2025-07-18T23:14:15Z",
  "immediate": false,
  "data": {
    "portfolioValue": 39107.47,
    "totalPnL": -7.26,
    "totalPnLPercentage": -0.03,
    "totalBalance": 39114.73,
    "dailyPnL": 29328.61,
    "dailyPnLPercentage": 66.65,
    "weeklyPnL": 29328.61,
    "monthlyPnL": 29328.61,
    
    // New: Overall P&L calculation (current balance - starting balance)
    "totalStartingBalance": 40000.00,
    "overallPnL": -885.27,
    "overallPnLPercentage": -2.21,
    
    // Bot information
    "activeBots": 4,
    "totalBots": 4,
    "botCount": 4,
    "totalOpenTrades": 10,
    
    // New: Aggregated open positions from all bots
    "aggregatedOpenPositions": [
      {
        "id": 1,
        "exchange": "kraken",
        "pair": "BTC/USD",
        "is_open": 1,
        "amount": 0.00084817,
        "stake_amount": 99.999243,
        "open_rate": 117900,
        "close_rate": null,
        "profit": 0,
        "profit_ratio": null,
        "open_date": "2025-07-18 22:45:08.491497",
        "close_date": null,
        "exit_reason": null,
        "strategy": "EmaRsiStrategy",
        "botId": "anshjarvis2003-bot-1",
        "botInstance": "anshjarvis2003-bot-1"
      }
      // ... additional open positions from all bots
    ],
    "totalOpenPositions": 10,
    
    "performanceMetrics": {
      "winRate": 0.0,
      "sharpeRatio": 0.0,
      "maxDrawdown": 0.0,
      "avgTradeReturn": 0.0
    },
    "riskMetrics": {
      "portfolioRisk": 0.0,
      "maxDrawdown": 0.0,
      "sharpeRatio": 0.0
    }
  }
}
```

#### Bot Metrics Updates (every 30 seconds)
```json
{
  "type": "bot_metrics_update",
  "timestamp": "2025-07-15T21:52:32Z",
  "immediate": false,
  "data": {
    "bots": [
      {
        "botId": "anshjarvis2003-bot-1",
        "instanceId": "anshjarvis2003-bot-1",
        "status": "running",
        "balance": 9999.36,
        "profit_total": -3.88,
        "profit_total_percentage": -0.04,
        "open_trade_count": 5,
        "closed_trade_count": 0,
        "total_trades": 5,
        "strategy": "DefaultStrategy",
        "lastUpdate": "2025-07-15T21:52:30Z"
      },
      {
        "botId": "anshjarvis2003-bot-2", 
        "instanceId": "anshjarvis2003-bot-2",
        "status": "running",
        "balance": 9999.37,
        "profit_total": -2.85,
        "profit_total_percentage": -0.03,
        "open_trade_count": 4,
        "closed_trade_count": 0,
        "total_trades": 4,
        "strategy": "DefaultStrategy",
        "lastUpdate": "2025-07-15T21:52:30Z"
      },
      {
        "botId": "anshjarvis2003-bot-3",
        "instanceId": "anshjarvis2003-bot-3", 
        "status": "running",
        "balance": 9999.63,
        "profit_total": -2.97,
        "profit_total_percentage": -0.03,
        "open_trade_count": 5,
        "closed_trade_count": 0,
        "total_trades": 5,
        "strategy": "DefaultStrategy",
        "lastUpdate": "2025-07-15T21:52:30Z"
      }
    ],
    "totalBots": 3,
    "activeBots": 3
  }
}
```

#### Time-Series Data Points (every 5 minutes)
```json
{
  "type": "timeseries_stream_update",
  "timestamp": "2025-07-15T21:55:00Z",
  "data": {
    "newDataPoint": {
      "timestamp": "2025-07-15T21:55:00Z",
      "portfolioValue": 29990.45,
      "totalPnL": -8.91,
      "totalBalance": 29999.36,
      "activeBots": 3,
      "botCount": 3
    },
    "timeframe": "5m",
    "totalDataPoints": 145
  }
}
```

#### Trade Alerts (real-time)
```json
{
  "type": "trade_alert",
  "timestamp": "2024-01-15T10:32:15Z",
  "data": {
    "botId": "bot123",
    "tradeId": "trade_456",
    "action": "buy",
    "pair": "BTC/USDT",
    "amount": 0.1,
    "price": 45250.00,
    "status": "completed",
    "pnl": null,
    "timestamp": "2024-01-15T10:32:15Z"
  }
}
```

#### Bot Status Changes (real-time)
```json
{
  "type": "bot_status_update", 
  "timestamp": "2024-01-15T10:33:00Z",
  "data": {
    "botId": "bot123",
    "previousStatus": "running",
    "newStatus": "stopped",
    "reason": "user_stopped",
    "timestamp": "2024-01-15T10:33:00Z"
  }
}
```

#### System Health Updates (every 60 seconds)
```json
{
  "type": "system_health_update",
  "timestamp": "2025-07-15T21:52:00Z", 
  "data": {
    "status": "healthy",
    "uptime": 7253,
    "activeUsers": 1,
    "activeBots": 3,
    "totalConnections": 1,
    "memoryUsage": {
      "heapUsed": 89.5,
      "heapTotal": 132.2,
      "external": 45.1,
      "rss": 187.8
    },
    "systemLoad": {
      "cpu": 12.5,
      "memory": 68.2
    },
    "botDiscovery": {
      "lastRefresh": "2025-07-15T21:51:33Z",
      "discoveredBots": 3,
      "refreshInterval": "2 minutes"
    }
  }
}
```

#### Error Messages
```json
{
  "type": "error",
  "data": {
    "code": "AUTHENTICATION_FAILED",
    "message": "Invalid or expired token",
    "retryAfter": 5000
  }
}
```

---

## Data Models

### Bot Object
```typescript
interface Bot {
  instanceId: string;
  status: 'running' | 'stopped' | 'error' | 'starting' | 'stopping';
  config: BotConfig;
  balance: number;
  totalPnL: number;
  openTrades: number;
  closedTrades: number;
  performance: PerformanceMetrics;
  lastUpdate: number;
}
```

### Portfolio Object
```typescript
interface Portfolio {
  totalBalance: number;
  totalPnL: number;
  portfolioValue: number;
  botCount: number;
  activeBots: number;
  dailyPnL: number;
  weeklyPnL: number;
  monthlyPnL: number;
  riskMetrics: RiskMetrics;
  lastUpdate: number;
  performanceMetrics: PerformanceMetrics;
}
```

### Time-Series Data Point
```typescript
interface TimeSeriesPoint {
  timestamp: number;
  portfolioValue: number;
  totalPnL: number;
  totalBalance: number;
  botCount: number;
  activeBots: number;
}
```

### Trade Object
```typescript
interface Trade {
  tradeId: number;
  pair: string;
  side: 'buy' | 'sell';
  amount: number;
  price: number;
  fee: number;
  profit: number;
  isOpen: boolean;
  openDate: number;
  closeDate?: number;
  strategy: string;
}
```

---

## Error Handling

### REST API Errors
```json
{
  "success": false,
  "error": {
    "code": "BOT_NOT_FOUND",
    "message": "Bot instance not found",
    "details": { ... }
  }
}
```

### WebSocket Errors
```json
{
  "type": "error",
  "data": {
    "code": "AUTHENTICATION_FAILED",
    "message": "Invalid or expired token",
    "retryAfter": 5000
  }
}
```

### Common Error Codes
- `AUTHENTICATION_FAILED`: Invalid or expired JWT token
- `BOT_NOT_FOUND`: Requested bot instance doesn't exist
- `ACCESS_DENIED`: User doesn't own the requested resource
- `RATE_LIMITED`: Too many requests from client
- `BOT_ACTION_FAILED`: Bot start/stop operation failed
- `INVALID_CONFIG`: Bot configuration validation failed
- `SERVICE_UNAVAILABLE`: Backend service temporarily unavailable

---

## Production Features

### Monitoring & Health Checks
- **Health Endpoint**: `/health` - System status and metrics
- **Ready Endpoint**: `/ready` - Service readiness check
- **Metrics Endpoint**: `/metrics` - Detailed performance metrics

### Security Features
- **Rate Limiting**: Per-user message rate limits
- **Connection Limits**: Maximum connections per IP/user
- **Input Validation**: All inputs validated and sanitized
- **CORS Protection**: Configurable CORS policies

### Performance Features
- **Multi-Layer Caching**: L1 (30s), L2 (5m), L3 (1h)
- **Connection Pooling**: Efficient database connections
- **Background Processing**: Non-blocking operations
- **Memory Management**: Automatic cache cleanup

---

## Frontend Integration Examples

### React Integration

#### WebSocket Hook
```javascript
import { useState, useEffect, useRef } from 'react';

export const useWebSocket = (token) => {
  const [isConnected, setIsConnected] = useState(false);
  const [portfolio, setPortfolio] = useState(null);
  const [bots, setBots] = useState([]);
  const ws = useRef(null);

  useEffect(() => {
    if (token) {
      connectWebSocket();
    }
    return () => {
      if (ws.current) {
        ws.current.close();
      }
    };
  }, [token]);

  const connectWebSocket = () => {
    // Use production URL for live deployment, development URL for local testing
    const wsUrl = process.env.NODE_ENV === 'production' 
      ? `wss://freqtrade.crypto-pilot.dev/ws?token=${token}`
      : `ws://localhost:3001/ws?token=${token}`;
      
    ws.current = new WebSocket(wsUrl);
    
    ws.current.onopen = () => {
      setIsConnected(true);
      // Subscribe to live streaming updates
      sendMessage({
        type: 'subscribe_updates',
        data: { 
          channels: ['portfolio', 'bot_metrics', 'timeseries', 'trade_alerts', 'bot_status']
        }
      });
    };

    ws.current.onmessage = (event) => {
      const message = JSON.parse(event.data);
      handleMessage(message);
    };

    ws.current.onclose = () => {
      setIsConnected(false);
      // Implement reconnection logic
      setTimeout(connectWebSocket, 5000);
    };
  };

  const handleMessage = (message) => {
    switch (message.type) {
      case 'subscription_confirmed':
        console.log('Subscribed to channels:', message.data.subscribedChannels);
        console.log('Streaming intervals:', message.data.streamingIntervals);
        console.log('Available features:', message.data.features);
        break;
      case 'portfolio_stream_update':
        setPortfolio(message.data);
        break;
      case 'bot_metrics_update':
        // Update all bots with current metrics
        setBots(message.data.bots || []);
        break;
      case 'timeseries_stream_update':
        // Update chart data with new data point
        updateChartData(message.data);
        break;
      case 'trade_alert':
        // Show trade notification
        showTradeNotification(message.data);
        break;
      case 'bot_status_update':
        setBots(prev => prev.map(bot => 
          bot.instanceId === message.data.botId 
            ? { ...bot, status: message.data.newStatus }
            : bot
        ));
        break;
      case 'system_health_update':
        setSystemHealth(message.data);
        break;
    }
  };

  const updateChartData = (timeseriesData) => {
    // Implementation for updating chart with new data points
    const newPoint = timeseriesData.newDataPoint;
    setChartData(prev => ({
      ...prev,
      labels: [...prev.labels, new Date(newPoint.timestamp).toLocaleTimeString()],
      values: [...prev.values, newPoint.portfolioValue]
    }));
  };

  const showTradeNotification = (tradeData) => {
    // Implementation for showing trade notifications
    console.log(`New ${tradeData.action} trade: ${tradeData.amount} ${tradeData.pair} at ${tradeData.price}`);
  };

  const sendMessage = (message) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
    }
  };

  const subscribeToChannels = (channels) => {
    sendMessage({
      type: 'subscribe_updates',
      data: { channels }
    });
  };

  const unsubscribeFromChannels = (channels) => {
    sendMessage({
      type: 'unsubscribe_updates',
      data: { channels }
    });
  };

  return {
    isConnected,
    portfolio,
    bots,
    sendMessage,
    subscribeToChannels,
    unsubscribeFromChannels
  };
};
```

#### Portfolio Dashboard Component
```javascript
import React from 'react';
import { useWebSocket } from './useWebSocket';

export const PortfolioDashboard = ({ token }) => {
  const { isConnected, portfolio, bots, sendMessage } = useWebSocket(token);

  const handleBotAction = (botId, action) => {
    sendMessage({
      type: 'bot_action',
      data: { botId, action }
    });
  };

  const requestChartData = (timeframe) => {
    sendMessage({
      type: 'get_chart_data',
      data: {
        chartType: 'portfolio_value',
        parameters: { timeframe }
      }
    });
  };

  return (
    <div className="portfolio-dashboard">
      <div className="connection-status">
        {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
      </div>
      
      {portfolio && (
        <div className="portfolio-summary">
          <h2>Portfolio Overview</h2>
          <p>Total Value: ${portfolio.portfolioValue?.toFixed(2) || '0.00'}</p>
          <p>Total P&L: ${portfolio.totalPnL?.toFixed(2) || '0.00'}</p>
          <p>Daily P&L: ${portfolio.dailyPnL?.toFixed(2) || '0.00'}</p>
          <p>Active Bots: {portfolio.activeBots || 0}/{portfolio.totalBots || 0}</p>
          <p>Open Trades: {portfolio.totalOpenTrades || 0}</p>
        </div>
      )}

      <div className="bot-controls">
        {bots.map(bot => (
          <div key={bot.instanceId} className="bot-card">
            <h3>{bot.instanceId}</h3>
            <p>Status: {bot.status}</p>
            <p>Balance: ${bot.balance?.toFixed(2) || '0.00'}</p>
            <p>P&L: ${bot.profit_total?.toFixed(2) || '0.00'} ({bot.profit_total_percentage?.toFixed(2) || '0.00'}%)</p>
            <p>Trades: {bot.open_trade_count || 0} open / {bot.total_trades || 0} total</p>
            <button onClick={() => handleBotAction(bot.instanceId, 'start')}>
              Start
            </button>
            <button onClick={() => handleBotAction(bot.instanceId, 'stop')}>
              Stop
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Vue.js Integration

#### WebSocket Composable
```javascript
import { ref, onMounted, onUnmounted } from 'vue';

export function useWebSocket(token) {
  const isConnected = ref(false);
  const portfolio = ref(null);
  const bots = ref([]);
  let ws = null;

  const connect = () => {
    // Use production URL for live deployment, development URL for local testing
    const wsUrl = process.env.NODE_ENV === 'production'
      ? `wss://freqtrade.crypto-pilot.dev/ws?token=${token.value}`
      : `ws://localhost:3001/ws?token=${token.value}`;
      
    ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      isConnected.value = true;
      sendMessage({
        type: 'get_portfolio_summary',
        data: { includeTimeSeries: true }
      });
    };

    ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      handleMessage(message);
    };

    ws.onclose = () => {
      isConnected.value = false;
    };
  };

  const handleMessage = (message) => {
    switch (message.type) {
      case 'portfolio_summary':
        portfolio.value = message.data;
        break;
      case 'portfolio_stream_update':
        if (portfolio.value) {
          Object.assign(portfolio.value, message.data);
        }
        break;
      case 'bot_metrics_update':
        bots.value = message.data.bots || [];
        break;
    }
  };

  const sendMessage = (message) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  };

  onMounted(() => {
    if (token.value) {
      connect();
    }
  });

  onUnmounted(() => {
    if (ws) {
      ws.close();
    }
  });

  return {
    isConnected,
    portfolio,
    bots,
    sendMessage
  };
}
```

### Enhanced Portfolio Features Integration

#### Overall P&L Dashboard Component
```javascript
import React from 'react';
import { useWebSocket } from './useWebSocket';

export const EnhancedPortfolioDashboard = ({ token }) => {
  const { isConnected, portfolio, bots } = useWebSocket(token);

  return (
    <div className="enhanced-portfolio-dashboard">
      <div className="connection-status">
        {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
      </div>
      
      {portfolio && (
        <>
          {/* Standard Portfolio Metrics */}
          <div className="portfolio-summary">
            <h2>Portfolio Overview</h2>
            <div className="metrics-grid">
              <div className="metric-card">
                <h3>Current Portfolio</h3>
                <p className="value">${portfolio.portfolioValue?.toFixed(2) || '0.00'}</p>
                <p className="sub">Total Balance: ${portfolio.totalBalance?.toFixed(2) || '0.00'}</p>
              </div>
              
              <div className="metric-card">
                <h3>Trading P&L</h3>
                <p className="value">${portfolio.totalPnL?.toFixed(2) || '0.00'}</p>
                <p className="sub">Unrealized gains/losses</p>
              </div>
            </div>
          </div>

          {/* NEW: Overall P&L Section */}
          <div className="overall-pnl-section">
            <h2>Overall Performance</h2>
            <div className="pnl-grid">
              <div className="pnl-card">
                <h3>Starting Investment</h3>
                <p className="value">${portfolio.totalStartingBalance?.toFixed(2) || '0.00'}</p>
                <p className="sub">Initial capital across all bots</p>
              </div>
              
              <div className="pnl-card">
                <h3>Overall P&L</h3>
                <p className={`value ${(portfolio.overallPnL || 0) >= 0 ? 'positive' : 'negative'}`}>
                  ${portfolio.overallPnL?.toFixed(2) || '0.00'}
                </p>
                <p className="sub">
                  {portfolio.overallPnLPercentage?.toFixed(2) || '0.00'}% 
                  {(portfolio.overallPnL || 0) >= 0 ? ' gain' : ' loss'}
                </p>
              </div>
              
              <div className="pnl-card">
                <h3>Return Rate</h3>
                <p className={`value ${(portfolio.overallPnLPercentage || 0) >= 0 ? 'positive' : 'negative'}`}>
                  {portfolio.overallPnLPercentage?.toFixed(2) || '0.00'}%
                </p>
                <p className="sub">Total return on investment</p>
              </div>
            </div>
          </div>

          {/* NEW: Aggregated Open Positions */}
          <div className="open-positions-section">
            <h2>Open Positions Across All Bots</h2>
            <div className="positions-summary">
              <p>Total Open Positions: <strong>{portfolio.totalOpenPositions || 0}</strong></p>
              <p>Active Bots: <strong>{portfolio.activeBots || 0}/{portfolio.botCount || 0}</strong></p>
            </div>
            
            {portfolio.aggregatedOpenPositions && portfolio.aggregatedOpenPositions.length > 0 ? (
              <div className="positions-table">
                <table>
                  <thead>
                    <tr>
                      <th>Bot</th>
                      <th>Pair</th>
                      <th>Amount</th>
                      <th>Entry Price</th>
                      <th>Strategy</th>
                      <th>Open Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {portfolio.aggregatedOpenPositions.map((position, index) => (
                      <tr key={`${position.botId}-${position.id}`}>
                        <td className="bot-name">{position.botId}</td>
                        <td className="pair">{position.pair}</td>
                        <td className="amount">{position.amount?.toFixed(6)}</td>
                        <td className="price">${position.open_rate?.toLocaleString()}</td>
                        <td className="strategy">{position.strategy}</td>
                        <td className="date">
                          {new Date(position.open_date).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="no-positions">No open positions</p>
            )}
          </div>

          {/* Positions by Bot Breakdown */}
          <div className="positions-by-bot">
            <h3>Positions by Bot</h3>
            {(() => {
              const botPositions = {};
              (portfolio.aggregatedOpenPositions || []).forEach(pos => {
                if (!botPositions[pos.botId]) {
                  botPositions[pos.botId] = [];
                }
                botPositions[pos.botId].push(pos);
              });

              return Object.keys(botPositions).map(botId => (
                <div key={botId} className="bot-positions">
                  <h4>🤖 {botId}</h4>
                  <p>{botPositions[botId].length} open positions</p>
                  <ul>
                    {botPositions[botId].map(pos => (
                      <li key={pos.id}>
                        {pos.pair}: {pos.amount?.toFixed(6)} @ ${pos.open_rate?.toLocaleString()}
                      </li>
                    ))}
                  </ul>
                </div>
              ));
            })()}
          </div>
        </>
      )}
    </div>
  );
};
```

#### CSS Styles for Enhanced Portfolio
```css
.enhanced-portfolio-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.metrics-grid, .pnl-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card, .pnl-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.value {
  font-size: 2rem;
  font-weight: bold;
  margin: 10px 0;
}

.value.positive {
  color: #28a745;
}

.value.negative {
  color: #dc3545;
}

.positions-table {
  overflow-x: auto;
  margin: 20px 0;
}

.positions-table table {
  width: 100%;
  border-collapse: collapse;
}

.positions-table th,
.positions-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.positions-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.bot-positions {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
}

.bot-positions h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.bot-positions ul {
  list-style: none;
  padding: 0;
  margin: 10px 0 0 0;
}

.bot-positions li {
  padding: 5px 0;
  font-family: monospace;
  font-size: 0.9rem;
}
```

### Portfolio Chart Integration Examples

#### REST API Approach (Recommended for Initial Load)
```javascript
// Fetch initial chart data using REST API
import { useState, useEffect } from 'react';

export const PortfolioChart = ({ timeframe = '24H', token }) => {
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchChartData();
  }, [timeframe]);

  const fetchChartData = async () => {
    try {
      setLoading(true);
      
      // Use environment-appropriate URL
      const baseUrl = process.env.NODE_ENV === 'production' 
        ? 'https://freqtrade.crypto-pilot.dev'
        : 'http://localhost:3001';
        
      const response = await fetch(`${baseUrl}/api/portfolio/chart/${timeframe}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      
      if (result.success) {
        setChartData(result);
        setError(null);
      } else {
        setError(result.message || 'Failed to fetch chart data');
      }
    } catch (err) {
      setError(`Error fetching chart data: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading chart...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!chartData?.data) return <div>No chart data available</div>;

  return (
    <div className="portfolio-chart">
      <h3>Portfolio Performance - {chartData.timeframe}</h3>
      <div className="chart-metadata">
        <span>Data Points: {chartData.metadata.dataPoints}</span>
        <span>Total Return: {chartData.metadata.totalReturn?.toFixed(2)}%</span>
        <span>Range: ${chartData.metadata.minValue?.toFixed(2)} - ${chartData.metadata.maxValue?.toFixed(2)}</span>
      </div>
      <PortfolioLineChart data={chartData.data} />
    </div>
  );
};
```

#### Chart.js Integration
```javascript
// Using Chart.js for portfolio visualization
import { Chart, registerables } from 'chart.js';
import { Line } from 'react-chartjs-2';
Chart.register(...registerables);

export const PortfolioLineChart = ({ data }) => {
  const chartData = {
    labels: data.map(point => new Date(point.timestamp).toLocaleString()),
    datasets: [
      {
        label: 'Portfolio Value',
        data: data.map(point => point.portfolioValue),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.1)',
        tension: 0.1,
        fill: true
      },
      {
        label: 'Total P&L',
        data: data.map(point => point.totalPnL),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
        tension: 0.1,
        yAxisID: 'y1'
      }
    ]
  };

  const options = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: 'Portfolio Performance Over Time'
      },
      legend: {
        display: true,
        position: 'top'
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time'
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Portfolio Value ($)'
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'P&L ($)'
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return <Line data={chartData} options={options} />;
};
```

#### WebSocket Real-time Updates
```javascript
// Combine REST API initial load with WebSocket real-time updates
export const RealTimePortfolioChart = ({ timeframe = '24H', token }) => {
  const [chartData, setChartData] = useState(null);
  const { isConnected, sendMessage } = useWebSocket(token);

  useEffect(() => {
    // Initial load via REST API
    fetchInitialChartData();
  }, [timeframe]);

  useEffect(() => {
    if (isConnected) {
      // Subscribe to real-time time-series updates
      sendMessage({
        type: 'subscribe_updates',
        data: { channels: ['timeseries'] }
      });
    }
  }, [isConnected]);

  const fetchInitialChartData = async () => {
    // Use REST API for initial load (implementation above)
    // ...
  };

  const handleTimeSeriesUpdate = (newDataPoint) => {
    setChartData(prev => {
      if (!prev) return null;
      
      // Add new data point to existing chart data
      const updatedData = [...prev.data, {
        timestamp: newDataPoint.timestamp,
        date: new Date(newDataPoint.timestamp).toISOString(),
        portfolioValue: newDataPoint.portfolioValue,
        totalPnL: newDataPoint.totalPnL,
        totalBalance: newDataPoint.totalBalance,
        botCount: newDataPoint.botCount
      }];

      // Update metadata
      const updatedMetadata = {
        ...prev.metadata,
        endValue: newDataPoint.portfolioValue,
        maxValue: Math.max(prev.metadata.maxValue, newDataPoint.portfolioValue),
        minValue: Math.min(prev.metadata.minValue, newDataPoint.portfolioValue),
        dataPoints: updatedData.length
      };

      return {
        ...prev,
        data: updatedData,
        metadata: updatedMetadata
      };
    });
  };

  return (
    <div className="real-time-chart">
      <div className="connection-indicator">
        {isConnected ? '🔴 Live' : '⚫ Offline'}
      </div>
      <PortfolioChart timeframe={timeframe} token={token} />
    </div>
  );
};
```

#### Multiple Timeframe Selector
```javascript
export const MultiTimeframeChart = ({ token }) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('24H');
  const timeframes = [
    { value: '1H', label: '1 Hour' },
    { value: '4H', label: '4 Hours' },
    { value: '24H', label: '24 Hours' },
    { value: '7D', label: '7 Days' },
    { value: '30D', label: '30 Days' }
  ];

  return (
    <div className="multi-timeframe-chart">
      <div className="timeframe-selector">
        {timeframes.map(({ value, label }) => (
          <button
            key={value}
            className={selectedTimeframe === value ? 'active' : ''}
            onClick={() => setSelectedTimeframe(value)}
          >
            {label}
          </button>
        ))}
      </div>
      <PortfolioChart timeframe={selectedTimeframe} token={token} />
    </div>
  );
};
```

---

## Performance Considerations

### Frontend Optimization
1. **Connection Management**: Implement automatic reconnection
2. **Message Queuing**: Queue messages during disconnection
3. **Data Debouncing**: Debounce rapid portfolio updates
4. **Memory Management**: Clean up event listeners and connections

### Backend Features
1. **Caching**: Multi-layer caching reduces API load
2. **Rate Limiting**: Prevents abuse and ensures fair usage
3. **Connection Pooling**: Efficient resource utilization
4. **Background Processing**: Non-blocking operations

### Best Practices
1. **Heartbeat**: Implement ping/pong for connection health
2. **Error Recovery**: Graceful handling of network issues
3. **State Synchronization**: Periodic full state refresh
4. **Batch Updates**: Group related updates for efficiency

---

## Security & Rate Limiting

### HTTPS/WSS Security
- **Production Domain**: All connections to `freqtrade.crypto-pilot.dev` are secured with TLS/SSL
- **WebSocket Security**: WSS (WebSocket Secure) protocol automatically used over HTTPS
- **Certificate**: Valid SSL certificate ensures encrypted communication
- **Data Protection**: All API calls and real-time streaming data encrypted in transit

### Authentication Security
- **Token Validation**: All requests validate JWT tokens
- **Token Expiration**: Handle expired tokens gracefully
- **Scope Limitation**: Users can only access their own data
- **Secure Transmission**: Tokens transmitted over encrypted HTTPS/WSS connections in production

### Rate Limiting
- **Message Rate**: Max 100 messages per minute per user
- **Connection Rate**: Max 10 connections per minute per IP
- **API Rate**: Max 1000 API calls per hour per user

### Security Headers
```javascript
// CORS configuration for production
{
  origin: [
    'https://freqtrade.crypto-pilot.dev',
    'https://*.crypto-pilot.dev',
    'http://localhost:3000', // Development only
    'http://localhost:3001'  // Development only
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}
```

### Production Security Features
- **TLS 1.3**: Latest TLS protocol for maximum security
- **HSTS Headers**: HTTP Strict Transport Security enabled
- **Certificate Validation**: Automatic SSL certificate verification
- **Encrypted Tokens**: JWT tokens transmitted securely over HTTPS
- **Secure WebSockets**: WSS protocol ensures real-time data encryption

---

## Production Deployment

### Domain Configuration
- **Production Domain**: `freqtrade.crypto-pilot.dev`
- **Protocol**: HTTPS/WSS (SSL/TLS encrypted)
- **Certificate**: Valid SSL certificate for secure communication
- **CDN**: Cloudflare or similar for performance and security

### URL Structure
```
Production URLs:
├── REST API Base: https://freqtrade.crypto-pilot.dev
├── WebSocket: wss://freqtrade.crypto-pilot.dev/ws
├── Health Check: https://freqtrade.crypto-pilot.dev/health
├── Test Clients: https://freqtrade.crypto-pilot.dev/test-streaming-client.html
└── Documentation: https://freqtrade.crypto-pilot.dev/docs

Development URLs (Local):
├── REST API Base: http://localhost:3001
├── WebSocket: ws://localhost:3001/ws  
├── Health Check: http://localhost:3001/health
├── Test Clients: http://localhost:3001/test-streaming-client.html
└── Documentation: http://localhost:3001/docs
```

### SSL/TLS Security
- **Encryption**: All data encrypted in transit using TLS 1.3
- **WebSocket Security**: WSS protocol provides the same encryption as HTTPS
- **Certificate Validation**: Browser automatically validates SSL certificates
- **Token Security**: JWT tokens are transmitted securely over encrypted connections
- **HSTS**: HTTP Strict Transport Security headers prevent downgrade attacks

### Frontend Integration for Production
```javascript
// Environment-aware configuration
const API_CONFIG = {
  production: {
    baseUrl: 'https://freqtrade.crypto-pilot.dev',
    wsUrl: 'wss://freqtrade.crypto-pilot.dev/ws'
  },
  development: {
    baseUrl: 'http://localhost:3001',
    wsUrl: 'ws://localhost:3001/ws'
  }
};

const getCurrentConfig = () => {
  return process.env.NODE_ENV === 'production' 
    ? API_CONFIG.production 
    : API_CONFIG.development;
};

// Usage in your application
const config = getCurrentConfig();
const ws = new WebSocket(`${config.wsUrl}?token=${token}`);
```

### Security Considerations
1. **Token Management**: Use secure token storage (httpOnly cookies or secure localStorage)
2. **CORS Policy**: Production domain configured in CORS allowlist
3. **Rate Limiting**: Enhanced rate limiting for production traffic
4. **Monitoring**: SSL certificate monitoring and renewal alerts
5. **Firewall**: Production server protected by firewall rules

---

## Testing & Development

### Local Development Setup
```bash
# Start the bot manager service
cd /root/Crypto-Pilot-Freqtrade/bot-manager
npm start

# Development URLs:
# REST API: http://localhost:3001
# WebSocket: ws://localhost:3001/ws
```

### Production URLs
```bash
# Production URLs (HTTPS/WSS Secured):
# REST API: https://freqtrade.crypto-pilot.dev
# WebSocket: wss://freqtrade.crypto-pilot.dev/ws
```

### Current System State
- **Bot Manager Service**: Running as systemd service `bot-manager`
- **Active Bots**: 3 running instances (anshjarvis2003-bot-1, bot-2, bot-3)
- **Streaming Status**: ✅ Real-time discovery working correctly
- **Bot Detection**: ✅ Automatic discovery every 2 minutes + connection-triggered
- **Data Accuracy**: ✅ Shows "3/3 bots" with live metrics

### Testing Endpoints
```bash
# Health check (Development)
curl http://localhost:3001/health

# Health check (Production)
curl https://freqtrade.crypto-pilot.dev/health

# Portfolio summary (Development - requires auth)
curl -H "Authorization: Bearer <token>" \
     http://localhost:3001/api/portfolio/summary

# Portfolio summary (Production - requires auth) 
curl -H "Authorization: Bearer <token>" \
     https://freqtrade.crypto-pilot.dev/api/portfolio/summary

# Generate test token for development
cd /root/Crypto-Pilot-Freqtrade/bot-manager
node generate-user-token.js
```

### Authentication Tokens
For development, use the pre-generated tokens:
- **Test Token**: `/root/test_token.txt` (for general testing)
- **User Token**: `/root/ansh_token.txt` (for user: Js1Gaz4sMPPiDNgFbmAgDFLe4je2)

Example token usage:
```javascript
// Development
const token = fs.readFileSync('/root/ansh_token.txt', 'utf8').trim();
const ws = new WebSocket(`ws://127.0.0.1:3001/ws?token=${token}`);

// Production (replace with your actual token)
// const token = 'your_firebase_jwt_token';
// const ws = new WebSocket(`wss://freqtrade.crypto-pilot.dev/ws?token=${token}`);
```

### Environment-Based Configuration
```javascript
// Recommended approach for frontend applications
const getApiUrl = () => {
  return process.env.NODE_ENV === 'production'
    ? 'https://freqtrade.crypto-pilot.dev'
    : 'http://localhost:3001';
};

const getWebSocketUrl = () => {
  return process.env.NODE_ENV === 'production'
    ? 'wss://freqtrade.crypto-pilot.dev/ws'
    : 'ws://localhost:3001/ws';
};
```

### WebSocket Testing
Use the test clients available at:
- **Live Streaming Test**: `node /root/Crypto-Pilot-Freqtrade/test-live-streaming.js` (Development)
- **HTML Test Client**: `https://freqtrade.crypto-pilot.dev/test-streaming-client.html` (Production)
- **HTML Test Client**: `http://localhost:3001/test-streaming-client.html` (Development)
- **Advanced Portfolio**: `https://freqtrade.crypto-pilot.dev/phase3-test-client.html` (Production)
- **Advanced Portfolio**: `http://localhost:3001/phase3-test-client.html` (Development)

### Quick Test Commands
```bash
# Test real-time streaming (Node.js)
cd /root/Crypto-Pilot-Freqtrade
node test-live-streaming.js

# Test enhanced portfolio features (NEW)
cd /root/Crypto-Pilot-Freqtrade
./test-enhanced-portfolio-features.sh

# Test enhanced WebSocket streaming (NEW)
node test-enhanced-portfolio-streaming.js

# Test REST API portfolio summary (NEW)
TOKEN=$(cat /root/ansh_token.txt)
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/portfolio/summary | jq .

# Test from bot-manager directory
cd /root/Crypto-Pilot-Freqtrade/bot-manager/tests
node quick-streaming-test.js

# Check service status
sudo systemctl status bot-manager

# View live logs
sudo journalctl -u bot-manager -f
```

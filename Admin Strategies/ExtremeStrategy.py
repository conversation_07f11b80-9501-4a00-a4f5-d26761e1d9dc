from freqtrade.strategy import IStrategy
import talib.abstract as ta
from pandas import DataFrame

class ExtremeStrategy(IStrategy):
    """
    ExtremeStrategy: An aggressive high-risk, high-reward strategy designed to capture rapid breakouts.
    
    WARNING: This strategy is extremely aggressive. It uses a high stoploss of 20% and a very tight
    minimal ROI (0.5%) to attempt to maximize returns in the shortest possible time. It is not recommended
    for live trading without extensive backtesting and risk management.
    """
    # Use a low timeframe for frequent trading
    timeframe = '5m'
    
    # Set an aggressive stoploss: allow up to 20% loss per trade.
    stoploss = -0.20
    
    # Minimal ROI: take profit when a 0.5% gain is reached.
    minimal_roi = {"0": 0.005}
    
    # Disable trailing stop (you can enable it with aggressive parameters if desired)
    trailing_stop = False
    trailing_stop_positive = 0.0
    trailing_stop_positive_offset = 0.0

    # Only process new candles.
    process_only_new_candles = True
    
    # Require 50 candles to warm up indicators.
    startup_candle_count = 50

    # Complete order types mapping. Freqtrade expects a full mapping.
    order_types = {
        "entry": "limit",
        "exit": "limit",
        "stoploss": "limit",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
    }
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Bollinger Bands: used to detect breakouts.
        bollinger = ta.BBANDS(dataframe, timeperiod=20)
        dataframe['bb_upperband'] = bollinger['upperband']
        dataframe['bb_middleband'] = bollinger['middleband']
        dataframe['bb_lowerband'] = bollinger['lowerband']
        
        # MACD: used for momentum signals.
        macd = ta.MACD(dataframe)
        dataframe['macd'] = macd['macd']
        dataframe['macdsignal'] = macd['macdsignal']
        dataframe['macdhist'] = macd['macdhist']
        
        # RSI: used to filter out overbought conditions.
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Generate buy signals when:
        - Price breaks above the upper Bollinger Band,
        - RSI is below 70 (not too overbought),
        - MACD shows bullish momentum (MACD crosses above its signal line).
        """
        dataframe.loc[
            (
                (dataframe['close'] > dataframe['bb_upperband']) &
                (dataframe['rsi'] < 70) &
                (dataframe['macd'] > dataframe['macdsignal']) &
                (dataframe['macd'].shift(1) <= dataframe['macdsignal'].shift(1))
            ),
            'enter_long'
        ] = 1
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Generate sell signals when either:
        - RSI exceeds 80 (overbought conditions) or
        - MACD crosses below its signal line (momentum reversal).
        """
        dataframe.loc[
            (
                (dataframe['rsi'] > 80) |
                (
                    (dataframe['macd'] < dataframe['macdsignal']) &
                    (dataframe['macd'].shift(1) >= dataframe['macdsignal'].shift(1))
                )
            ),
            'exit_long'
        ] = 1
        return dataframe

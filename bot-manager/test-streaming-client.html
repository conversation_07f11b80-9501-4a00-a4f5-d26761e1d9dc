<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Streaming SSE Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }

        .connected {
            background-color: #d4edda;
            color: #155724;
        }

        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .metric-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .update-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #28a745;
            margin-left: 10px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .channel-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }

        .channel-item {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }

        .channel-item.active {
            background: #007bff;
            color: white;
        }
    </style>
</head>

<body>
    <h1>🚀 Live Streaming (SSE) Test</h1>

    <div class="container">
        <h2>Connection Status</h2>
        <div id="connectionStatus" class="status disconnected">Disconnected</div>

        <div>
            <label for="tokenInput">Access Token:</label>
            <input type="text" id="tokenInput" placeholder="Enter JWT token or use 'test' for demo"
                style="width: 400px; padding: 5px;">
        </div>

        <div style="margin: 10px 0;">
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>

        <div>
            <h3>Active Subscriptions:</h3>
            <div id="activeChannels" class="channel-list">
                <span>portfolio (SSE)</span>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Live Portfolio Metrics</h2>
        <div class="metrics" id="portfolioMetrics">
            <div class="metric-card">
                <div class="metric-value" id="portfolioValue">$0.00</div>
                <div class="metric-label">Portfolio Value</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="totalPnL">$0.00</div>
                <div class="metric-label">Total P&L</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="dailyPnL">$0.00</div>
                <div class="metric-label">Daily P&L</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="activeBots">0/0</div>
                <div class="metric-label">Active Bots</div>
            </div>
        </div>

        <h3>Update Statistics</h3>
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value" id="portfolioUpdates">0</div>
                <div class="metric-label">Portfolio Updates</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="immediateUpdates">0</div>
                <div class="metric-label">Immediate Updates</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Live Activity Log</h2>
        <div id="activityLog" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let es = null;
        let updateCounts = { portfolio: 0, immediate: 0 };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('activityLog');
            const colorMap = { info: '#000', success: '#28a745', error: '#dc3545', warning: '#ffc107' };
            logElement.innerHTML += `<div style="color: ${colorMap[type] || '#000'}; margin: 2px 0;">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            if (connected) {
                statusEl.textContent = 'Connected';
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function updatePortfolioMetrics(data) {
            document.getElementById('portfolioValue').textContent = `$${(data.portfolioValue || 0).toFixed(2)}`;
            document.getElementById('totalPnL').textContent = `$${(data.totalPnL || 0).toFixed(2)}`;
            document.getElementById('dailyPnL').textContent = `$${(data.dailyPnL || 0).toFixed(2)}`;
            document.getElementById('activeBots').textContent = `${data.activeBots || 0}/${data.botCount || 0}`;
        }

        function updateCountsUI() {
            document.getElementById('portfolioUpdates').textContent = updateCounts.portfolio;
            document.getElementById('immediateUpdates').textContent = updateCounts.immediate;
        }

        function connect() {
            const token = document.getElementById('tokenInput').value || 'test';
            const base = window.location.origin;
            const url = `${base.replace(/^http:/, 'http:')}/api/stream?token=${encodeURIComponent(token)}`;
            log(`Connecting to ${url}...`);

            // EventSource cannot set Authorization headers; token must be in query string
            es = new EventSource(url);

            es.onopen = function () {
                log('✅ SSE connected successfully!', 'success');
                updateConnectionStatus(true);
            };

            es.onerror = function (e) {
                log('❌ SSE error or disconnected', 'error');
                updateConnectionStatus(false);
            };

            es.addEventListener('portfolio', function (event) {
                try {
                    const message = JSON.parse(event.data);
                    updateCounts.portfolio++;
                    updatePortfolioMetrics(message);
                    updateCountsUI();
                    log(`💰 Portfolio Update #${updateCounts.portfolio}: $${(message.portfolioValue || 0).toFixed(2)} | P&L: $${(message.totalPnL || 0).toFixed(2)} | Bots: ${message.activeBots || 0}/${message.botCount || 0}`, 'success');
                } catch (err) {
                    log(`❌ Error parsing portfolio event: ${err}`, 'error');
                }
            });

            es.addEventListener('error', function (event) {
                // Server-sent custom error events
                try {
                    const data = JSON.parse(event.data);
                    log(`⚠️ Stream error: ${data.message || 'unknown'}`, 'warning');
                } catch (_) { }
            });
        }

        function disconnect() {
            if (es) {
                es.close();
                es = null;
                updateConnectionStatus(false);
            }
        }

        function clearLog() { document.getElementById('activityLog').innerHTML = ''; }

        // Initialize UI
        updateConnectionStatus(false);
        updateCountsUI();
        setTimeout(() => {
            log('🚀 SSE Test Client Ready');
            log('💡 Enter a JWT token or use "test", then click Connect');
        }, 100);
    </script>
</body>

</html>
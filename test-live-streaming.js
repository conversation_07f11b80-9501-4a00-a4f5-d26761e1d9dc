#!/usr/bin/env node

/**
 * Test Live Streaming WebSocket Functionality
 * Tests the new subscription channels and live updates
 */

const WebSocket = require('ws');

class StreamingTester {
    constructor() {
        this.ws = null;
        this.receivedUpdates = {
            portfolio: 0,
            bot_metrics: 0,
            timeseries: 0,
            immediate: 0
        };
        this.startTime = Date.now();
        this.testDuration = 5 * 60 * 1000; // 5 minutes
    }

    async runTest() {
        console.log('🚀 Starting Live Streaming WebSocket Test\n');
        
        try {
            await this.connectWebSocket();
            await this.subscribeToChannels();
            await this.monitorUpdates();
            
            this.displayResults();
        } catch (error) {
            console.error('❌ Test failed:', error);
        } finally {
            if (this.ws) {
                this.ws.close();
            }
        }
    }

    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            // Use a test token (you'll need a valid Firebase JWT token for real testing)
            const fs = require('fs');
            const testToken = fs.readFileSync('/root/ansh_token.txt', 'utf8').trim();
            
            console.log('🔌 Connecting to WebSocket server...');
            this.ws = new WebSocket(`ws://127.0.0.1:3001/ws?token=${testToken}`);
            
            this.ws.on('open', () => {
                console.log('✅ WebSocket connected successfully');
                resolve();
            });
            
            this.ws.on('error', (error) => {
                console.error('❌ WebSocket connection error:', error);
                reject(error);
            });
            
            this.ws.on('close', (code, reason) => {
                console.log(`🔌 WebSocket disconnected: ${code} - ${reason}`);
            });
            
            this.ws.on('message', (data) => {
                this.handleMessage(JSON.parse(data.toString()));
            });
        });
    }

    async subscribeToChannels() {
        console.log('📡 Subscribing to live streaming channels...');
        
        const subscriptionMessage = {
            type: 'subscribe_updates',
            data: {
                channels: [
                    'portfolio',
                    'bot_metrics', 
                    'timeseries',
                    'trade_alerts',
                    'bot_status'
                ]
            },
            timestamp: Date.now()
        };
        
        this.ws.send(JSON.stringify(subscriptionMessage));
        console.log('📡 Subscription request sent');
    }

    handleMessage(message) {
        const timestamp = new Date().toLocaleTimeString();
        
        switch (message.type) {
            case 'connection_established':
                console.log(`🎯 [${timestamp}] Connection established:`, message.data.features);
                break;
                
            case 'subscription_confirmed':
                console.log(`✅ [${timestamp}] Subscribed to channels:`, message.data.subscribedChannels);
                console.log(`⏱️  Streaming intervals:`, message.data.streamingIntervals);
                break;
                
            case 'portfolio_stream_update':
                this.receivedUpdates.portfolio++;
                if (message.immediate) this.receivedUpdates.immediate++;
                
                console.log(`💰 [${timestamp}] Portfolio Update #${this.receivedUpdates.portfolio}:`);
                console.log(`   📈 Portfolio Value: $${message.data.portfolioValue.toFixed(2)}`);
                console.log(`   💵 Total P&L: $${message.data.totalPnL.toFixed(2)}`);
                console.log(`   📊 Daily P&L: $${message.data.dailyPnL.toFixed(2)}`);
                console.log(`   🤖 Active Bots: ${message.data.activeBots}/${message.data.botCount}`);
                if (message.immediate) console.log('   ⚡ (Immediate update)');
                console.log('');
                break;
                
            case 'bot_metrics_stream_update':
                this.receivedUpdates.bot_metrics++;
                if (message.immediate) this.receivedUpdates.immediate++;
                
                console.log(`🤖 [${timestamp}] Bot Metrics Update #${this.receivedUpdates.bot_metrics}:`);
                message.data.bots.forEach(bot => {
                    console.log(`   🔹 ${bot.instanceId}: ${bot.status} | Balance: $${bot.balance.toFixed(2)} | P&L: $${bot.totalPnL.toFixed(2)} | Trades: ${bot.openTrades}/${bot.closedTrades}`);
                });
                if (message.immediate) console.log('   ⚡ (Immediate update)');
                console.log('');
                break;
                
            case 'timeseries_datapoint_update':
                this.receivedUpdates.timeseries++;
                
                console.log(`📊 [${timestamp}] Time-Series Data Point #${this.receivedUpdates.timeseries}:`);
                console.log(`   📈 New Point: $${message.data.newDataPoint.portfolioValue.toFixed(2)} at ${new Date(message.data.newDataPoint.timestamp).toLocaleTimeString()}`);
                console.log('');
                break;
                
            case 'trade_update':
                console.log(`🔄 [${timestamp}] Trade Update:`, message.data);
                break;
                
            case 'bot_status_update':
                console.log(`🔧 [${timestamp}] Bot Status Update:`, message.data);
                break;
                
            case 'error':
                console.error(`❌ [${timestamp}] Error:`, message.data);
                break;
                
            default:
                console.log(`📨 [${timestamp}] Other message:`, message.type);
        }
    }

    async monitorUpdates() {
        console.log(`⏱️  Monitoring live updates for ${this.testDuration / 1000} seconds...`);
        console.log('📊 Expected updates:');
        console.log('   💰 Portfolio updates: every 60 seconds');
        console.log('   🤖 Bot metrics: every 30 seconds');
        console.log('   📈 Time-series points: every 5 minutes');
        console.log('   ⚡ Immediate updates: on subscription\n');
        
        return new Promise(resolve => {
            setTimeout(() => {
                console.log('⏰ Test monitoring period completed\n');
                resolve();
            }, this.testDuration);
            
            // Status updates every 30 seconds
            const statusInterval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                console.log(`⏱️  Status at ${elapsed}s: Portfolio(${this.receivedUpdates.portfolio}) | Bots(${this.receivedUpdates.bot_metrics}) | TimeSeries(${this.receivedUpdates.timeseries}) | Immediate(${this.receivedUpdates.immediate})`);
            }, 30000);
            
            setTimeout(() => clearInterval(statusInterval), this.testDuration);
        });
    }

    displayResults() {
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        
        console.log('📋 Live Streaming Test Results');
        console.log('='.repeat(50));
        console.log(`⏱️  Test Duration: ${elapsed} seconds`);
        console.log(`💰 Portfolio Updates: ${this.receivedUpdates.portfolio}`);
        console.log(`🤖 Bot Metrics Updates: ${this.receivedUpdates.bot_metrics}`);
        console.log(`📈 Time-Series Updates: ${this.receivedUpdates.timeseries}`);
        console.log(`⚡ Immediate Updates: ${this.receivedUpdates.immediate}`);
        
        // Calculate expected vs actual
        const expectedPortfolio = Math.floor(elapsed / 60); // Every 60 seconds
        const expectedBots = Math.floor(elapsed / 30); // Every 30 seconds
        const expectedTimeSeries = Math.floor(elapsed / 300); // Every 5 minutes
        
        console.log('\n📊 Expected vs Actual:');
        console.log(`💰 Portfolio: ${this.receivedUpdates.portfolio}/${expectedPortfolio} (${this.receivedUpdates.portfolio >= expectedPortfolio ? '✅' : '❌'})`);
        console.log(`🤖 Bot Metrics: ${this.receivedUpdates.bot_metrics}/${expectedBots} (${this.receivedUpdates.bot_metrics >= expectedBots ? '✅' : '❌'})`);
        console.log(`📈 Time-Series: ${this.receivedUpdates.timeseries}/${expectedTimeSeries} (${this.receivedUpdates.timeseries >= expectedTimeSeries ? '✅' : '❌'})`);
        
        if (this.receivedUpdates.immediate > 0) {
            console.log('⚡ Immediate updates working: ✅');
        } else {
            console.log('⚡ Immediate updates: ❌');
        }
        
        const totalUpdates = this.receivedUpdates.portfolio + this.receivedUpdates.bot_metrics + this.receivedUpdates.timeseries;
        if (totalUpdates > 0) {
            console.log('\n🎉 Live streaming system is working!');
        } else {
            console.log('\n⚠️  No updates received - check system status');
        }
    }
}

// Run the test
if (require.main === module) {
    const tester = new StreamingTester();
    tester.runTest().catch(console.error);
}

module.exports = StreamingTester;

#!/usr/bin/env node

/**
 * Quick test to verify active bot count fix
 */

const fetch = require('node-fetch');

async function testActiveBotCount() {
    console.log('🔧 Testing Active Bot Count Fix...\n');

    try {
        // Test the health endpoint
        console.log('📊 Checking /health endpoint...');
        const healthResponse = await fetch('http://localhost:3000/health');
        const healthData = await healthResponse.json();

        console.log('Health Response:');
        console.log(`   Status: ${healthData.status}`);
        console.log(`   Active Bots: ${healthData.activeBots}`);
        console.log(`   Active Users: ${healthData.activeUsers}`);
        console.log(`   DB Connections: ${healthData.dbConnections}`);
        console.log(`   Cache Status: ${healthData.cacheStatus}`);
        console.log(`   Uptime: ${(healthData.uptime / 1000).toFixed(0)}s`);

        // Test the metrics endpoint
        console.log('\n📈 Checking /metrics endpoint...');
        const metricsResponse = await fetch('http://localhost:3000/metrics');
        const metricsData = await metricsResponse.json();

        console.log('Business Metrics:');
        console.log(`   Active Users: ${metricsData.business?.activeUsers || 0}`);
        console.log(`   Active Bots: ${metricsData.business?.activeBots || 0}`);
        console.log(`   Active Services: ${metricsData.business?.activeServices || 0}`);
        console.log(`   Messages Processed: ${metricsData.business?.messagesProcessed || 0}`);

        // Check if the fix is working
        if (healthData.activeBots !== undefined && healthData.activeBots >= 0) {
            console.log('\n✅ Active bot count is now being properly tracked!');
            if (healthData.activeBots > 0) {
                console.log(`🤖 Found ${healthData.activeBots} active bot(s)`);
            } else {
                console.log('📝 Note: No active bots detected (this might be correct if no bots are running)');
            }
        } else {
            console.log('\n❌ Active bot count still not properly tracked');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Make sure the server is running: node index.js');
        }
    }
}

if (require.main === module) {
    testActiveBotCount();
}

module.exports = { testActiveBotCount };

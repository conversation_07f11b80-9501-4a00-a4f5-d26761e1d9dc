# Crypto-Pilot-Freqtrade

A comprehensive cryptocurrency trading bot management system built on FreqTrade with multi-bot support, real-time portfolio tracking, automated deployment, and SSE and REST APIs.

---

## 🚀 Overview

Crypto-Pilot-Freqtrade is a complete trading bot ecosystem that provides:

- **Multi-Bot Management**: Deploy and manage multiple FreqTrade bot instances simultaneously
- **Portfolio Tracking**: Real-time portfolio value tracking with historical snapshots since account creation
- **Server-Sent Events (SSE) Streaming**: Real-time, one-way event stream for live portfolio updates
- **Authentication System**: Firebase and JWT authentication with role-based access
- **Local-First Architecture**: SQLite databases with optional Turso cloud sync for backup
- **Docker Integration**: Containerized bot deployment with isolated environments
- **Strategy Management**: Centralized strategy distribution across all bot instances
- **API Gateway**: Unified REST API for all bot operations and data access
- **Security Hardening**: Rate limiting, connection limits, and message validation

---

## 📁 Project Structure

```
Crypto-Pilot-Freqtrade/
├── bot-manager/                    # Main bot management service
│   ├── index.js                   # Primary server application
│   ├── auth.js                    # Authentication middleware
│   ├── tests/                     # Test utilities and SSE test client
│   ├── lib/                       # Utility libraries
│   │   ├── urlFormatter.js        # Database URL formatting
│   │   └── urlFormatter.test.js   # URL formatter tests
│   ├── *.service                  # System service files
│   └── README.md                  # Detailed bot manager documentation
├── freqtrade-instances/           # Individual bot instance data
├── freqtrade-shared/              # Shared strategies and configurations
├── freqtrade_shared_data/         # Shared market data
├── ta-lib/                        # Technical Analysis Library
├── local-to-turso-sync.py         # Database synchronization script
├── add-sync-configs.sh            # Sync configuration utility
├── check-sync-status.sh           # Sync status monitoring
├── strategy-sync-m.sh             # Strategy synchronization
└── sync-strategy-main-shared.sh   # Main-to-shared strategy sync
```

---

## 🏗️ Architecture & Core Components

### Bot Manager (bot-manager/)

- **index.js**: Main server application with REST API endpoints and SSE live streaming
- **auth.js**: Authentication middleware for Firebase and JWT tokens
- **portfolio-monitor-service.js**: (legacy) standalone portfolio monitoring service
- **lib/urlFormatter.js**: Database URL formatting utilities
- **tests/**: Test utilities and the HTML SSE test client

---

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- Python 3.8+ (for sync scripts and utilities)
- Firebase Admin SDK credentials (optional)
- Turso account and API key (optional)

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/Git-Ansh/Crypto-Pilot-Freqtrade.git
cd Crypto-Pilot-Freqtrade
```

2. **Install dependencies:**
```bash
cd bot-manager
npm install
```

3. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start the bot manager:**
```bash
npm start
```

5. **Access the system:**
- **API**: http://localhost:3001
- **SSE Stream**: http://localhost:3001/api/stream
- **SSE Test Client**: http://localhost:3001/test-streaming-client.html

### Environment Variables

Create a `.env` file in the `bot-manager` directory:

```bash
# Server Configuration
PORT=3001
BOT_BASE_DIR=/root/freqtrade-instances
SHARED_DATA_DIR=/root/freqtrade-shared

# Authentication
JWT_SECRET=your-secure-jwt-secret
FIREBASE_SERVICE_ACCOUNT_PATH=./serviceAccountKey.json

# Turso Configuration (Optional)
TURSO_API_KEY=your-turso-api-key
TURSO_ORG=your-turso-organization
TURSO_REGION=us-east-1

# Security
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

---

## 🔧 API Usage

### Authentication

**Firebase ID Token:**
```javascript
const token = await user.getIdToken();
fetch('/api/user/dashboard', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

**JWT Token:**
```javascript
const token = jwt.sign(
  { id: userId, email: userEmail, role: 'user' },
  process.env.JWT_SECRET
);
```

### Streaming (SSE)

Connect to the live stream and listen for portfolio updates.

```html
<script>
  const token = 'YOUR_TOKEN';
  const es = new EventSource(`/api/stream?token=${token}`);

  es.addEventListener('portfolio', (evt) => {
    const data = JSON.parse(evt.data);
    console.log('Portfolio update', data);
  });

  es.onerror = () => {
    console.warn('Stream error, closing');
    es.close();
  };
</script>
```

---

## 🧪 Testing

```bash
cd bot-manager
# Open the HTML test client in a browser
# http://localhost:3001/test-streaming-client.html
```

---

## 📄 License

MIT License - See LICENSE file for details.

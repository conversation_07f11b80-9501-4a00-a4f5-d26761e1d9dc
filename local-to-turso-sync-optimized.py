#!/usr/bin/env python3
"""
Optimized Local-First Database Sync with Turso
Only syncs when data actually changes, reducing unnecessary operations
"""

import os
import sys
import json
import sqlite3
import time
import subprocess
import argparse
import hashlib
from pathlib import Path

def log(message):
    """Log with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_turso_command(cmd_args):
    """Run turso CLI command and return output"""
    try:
        # Use TURSO_CMD environment variable if set, otherwise use 'turso'
        turso_cmd = os.environ.get('TURSO_CMD', 'turso')
        result = subprocess.run([turso_cmd] + cmd_args, 
                              capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        log(f"Turso command failed: {' '.join(cmd_args)}")
        log(f"Error: {e.stderr}")
        raise

def get_table_hash(cursor, table):
    """Get a hash of table data to detect changes"""
    try:
        # Get all data sorted by primary key or first column for consistent hash
        cursor.execute(f"SELECT * FROM {table} ORDER BY 1")
        rows = cursor.fetchall()
        
        # Create hash of all row data
        data_str = str(rows)
        return hashlib.md5(data_str.encode()).hexdigest()
    except Exception:
        return None

def ensure_turso_database(instance_id, user_id, turso_org, turso_region="us-east-1"):
    """Ensure Turso database exists for the bot instance"""
    db_name = f"bot-{user_id}-{instance_id}".lower().replace('_', '-')
    
    try:
        # Check if database exists
        dbs_output = run_turso_command(['db', 'list'])
        if db_name in dbs_output:
            log(f"Turso database '{db_name}' already exists")
        else:
            # Create database
            log(f"Creating Turso database '{db_name}' in region '{turso_region}'...")
            run_turso_command(['db', 'create', db_name, '--location', turso_region, '--wait'])
            log(f"✓ Turso database '{db_name}' created successfully")
        
        # Get and log the database URL
        db_url = get_turso_url(db_name)
        return db_name, db_url
        
    except Exception as e:
        log(f"Failed to ensure Turso database: {e}")
        raise

def get_turso_url(db_name):
    """Get Turso database URL"""
    try:
        url = run_turso_command(['db', 'show', db_name, '--url'])
        log(f"Retrieved Turso URL for '{db_name}'")
        return url
    except Exception as e:
        log(f"Failed to get Turso URL: {e}")
        raise

def sync_local_to_turso_optimized(local_db_path, turso_db_name):
    """Sync local SQLite database to Turso with incremental change detection"""
    try:
        log(f"Starting optimized sync from {local_db_path} to Turso database '{turso_db_name}'")
        
        # Check if local database exists
        if not os.path.exists(local_db_path):
            log(f"Local database not found: {local_db_path}")
            return False
        
        # Get list of tables in local database
        local_conn = sqlite3.connect(local_db_path)
        cursor = local_conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        if not tables:
            log("No tables found in local database")
            local_conn.close()
            return True
        
        log(f"Found {len(tables)} tables: {', '.join(tables)}")
        
        # Load sync metadata (track table hashes to detect changes)
        sync_metadata_path = local_db_path + '.sync_metadata'
        metadata = {}
        if os.path.exists(sync_metadata_path):
            try:
                with open(sync_metadata_path, 'r') as f:
                    metadata = json.load(f)
                log(f"Loaded sync metadata with {len(metadata)} table records")
            except Exception as e:
                log(f"Warning: Could not load sync metadata: {e}")
                metadata = {}
        else:
            log("No sync metadata found, will perform full sync")
        
        sync_count = 0
        tables_synced = 0
        unchanged_tables = 0
        
        # For each table, check if data changed and sync only if needed
        for table in tables:
            try:
                log(f"Processing table '{table}'...")
                    
                # Get table schema
                cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
                schema_result = cursor.fetchone()
                if not schema_result:
                    continue
                    
                create_sql = schema_result[0]
                
                # Convert CREATE TABLE to CREATE TABLE IF NOT EXISTS
                if create_sql.upper().startswith('CREATE TABLE'):
                    create_sql = create_sql.replace('CREATE TABLE', 'CREATE TABLE IF NOT EXISTS', 1)
                
                # Create table in Turso (if not exists)
                run_turso_command(['db', 'shell', turso_db_name, create_sql])
                
                # Get row count and data hash
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                if row_count == 0:
                    # Check if we need to clear remote table
                    last_row_count = metadata.get(table, {}).get('row_count', 0)
                    if last_row_count > 0:
                        log(f"Table '{table}' is now empty, clearing remote data")
                        run_turso_command(['db', 'shell', turso_db_name, f"DELETE FROM {table}"])
                        metadata[table] = {'hash': '', 'row_count': 0, 'last_sync': time.time()}
                    else:
                        log(f"Table '{table}' is empty, skipping")
                    continue
                
                # Check if data changed using hash
                current_hash = get_table_hash(cursor, table)
                last_hash = metadata.get(table, {}).get('hash')
                
                if current_hash == last_hash and current_hash is not None:
                    log(f"Table '{table}' unchanged (hash: {current_hash[:8]}...), skipping sync")
                    unchanged_tables += 1
                    continue
                
                log(f"Table '{table}' changed, syncing {row_count} rows...")
                log(f"  Previous hash: {last_hash[:8] + '...' if last_hash else 'none'}")
                log(f"  Current hash:  {current_hash[:8] + '...' if current_hash else 'error'}")
                
                # Get all data from table
                cursor.execute(f"SELECT * FROM {table}")
                rows = cursor.fetchall()
                
                # Get column names
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [col[1] for col in cursor.fetchall()]
                
                # Clear existing data in Turso table
                run_turso_command(['db', 'shell', turso_db_name, f"DELETE FROM {table}"])
                
                # Insert data in batches
                batch_size = 100
                rows_synced = 0
                for i in range(0, len(rows), batch_size):
                    batch = rows[i:i + batch_size]
                    
                    # Convert batch to proper format for turso shell
                    for row in batch:
                        # Escape and format values
                        values = []
                        for val in row:
                            if val is None:
                                values.append('NULL')
                            elif isinstance(val, str):
                                # Escape single quotes
                                escaped = val.replace("'", "''")
                                values.append(f"'{escaped}'")
                            else:
                                values.append(str(val))
                        
                        final_sql = f"INSERT INTO {table} ({','.join(columns)}) VALUES ({','.join(values)})"
                        run_turso_command(['db', 'shell', turso_db_name, final_sql])
                        rows_synced += 1
                    
                    if len(rows) > batch_size:
                        log(f"  Synced batch {i//batch_size + 1}/{(len(rows) + batch_size - 1)//batch_size}")
                
                # Update metadata
                metadata[table] = {
                    'hash': current_hash,
                    'row_count': row_count,
                    'last_sync': time.time()
                }
                
                sync_count += row_count
                tables_synced += 1
                log(f"✓ Table '{table}' sync complete ({row_count} rows)")
                
            except Exception as e:
                log(f"Failed to sync table '{table}': {e}")
                continue
        
        # Save metadata
        try:
            with open(sync_metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            log(f"Saved sync metadata to {sync_metadata_path}")
        except Exception as e:
            log(f"Warning: Could not save sync metadata: {e}")
        
        local_conn.close()
        
        if tables_synced > 0:
            log(f"✓ Sync complete! Synced {sync_count} rows across {tables_synced} changed tables")
            log(f"  {unchanged_tables} tables were unchanged and skipped")
        else:
            log(f"✓ No changes detected. All {len(tables)} tables are up to date (operations saved: ~{unchanged_tables * 2})")
        
        return True
        
    except Exception as e:
        log(f"Sync failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Optimized sync: local SQLite database to Turso')
    parser.add_argument('--instance-id', required=True, help='Bot instance ID')
    parser.add_argument('--user-id', required=True, help='User ID')
    parser.add_argument('--local-db', required=True, help='Path to local SQLite database')
    parser.add_argument('--turso-org', help='Turso organization (from env if not provided)')
    parser.add_argument('--turso-region', default='us-east-1', help='Turso region')
    parser.add_argument('--create-if-missing', action='store_true', help='Create Turso DB if missing')
    
    args = parser.parse_args()
    
    # Get Turso org from environment if not provided
    turso_org = args.turso_org or os.getenv('TURSO_ORG')
    if not turso_org:
        log("ERROR: TURSO_ORG must be provided via --turso-org or environment variable")
        sys.exit(1)
    
    # Check if Turso API key is available
    if not os.getenv('TURSO_API_KEY'):
        log("ERROR: TURSO_API_KEY environment variable is required")
        sys.exit(1)
    
    try:
        # Ensure Turso database exists
        if args.create_if_missing:
            turso_db_name, turso_db_url = ensure_turso_database(args.instance_id, args.user_id, turso_org, args.turso_region)
            log(f"Using Turso database: {turso_db_name} (URL: {turso_db_url})")
        else:
            turso_db_name = f"bot-{args.user_id}-{args.instance_id}".lower().replace('_', '-')
            log(f"Using existing Turso database: {turso_db_name}")
        
        # Perform optimized sync
        success = sync_local_to_turso_optimized(args.local_db, turso_db_name)
        
        if success:
            log("✅ Optimized sync completed successfully")
            sys.exit(0)
        else:
            log("❌ Sync failed")
            sys.exit(1)
            
    except Exception as e:
        log(f"❌ Sync failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

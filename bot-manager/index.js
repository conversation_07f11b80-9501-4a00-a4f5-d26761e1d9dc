const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const fs = require('fs-extra'); // Use fs-extra for ensureDir etc.
const path = require('path');
const dotenv = require('dotenv');
const { formatDbUrl } = require('./lib/urlFormatter');
const { URL } = require('url');
const jwt = require('jsonwebtoken');
const admin = require('firebase-admin');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const fetch = require('node-fetch'); // Added for FreqTrade API proxy functionality
const UniversalRiskManager = require('./universal-risk-manager');

// Load environment variables from the bot-manager/.env file
dotenv.config({ path: path.join(__dirname, '.env') });
// Turso CLI command (allow overriding via env if path differs)
const TURSO_CMD = process.env.TURSO_CMD || 'turso';
// Log which Turso CLI binary will be used
console.log(`Using TURSO_CMD: ${TURSO_CMD}`);

// Portfolio snapshot throttling - track last save time per user
const userLastSnapshotTime = new Map();
const savingInProgress = new Map(); // Track concurrent saves per user

// Turso configuration: API key, organization, and region for remote SQLite DB
const TURSO_API_KEY = process.env.TURSO_API_KEY;
const TURSO_ORG = process.env.TURSO_ORG;
const TURSO_REGION = process.env.TURSO_REGION || 'us-east-1';

// Global reference to portfolio monitor for tracking bot creation
let globalPortfolioMonitor = null;

// Set portfolio monitor reference (to be called by portfolio monitor service)
function setPortfolioMonitor(monitor) {
  globalPortfolioMonitor = monitor;
  console.log('✓ Portfolio monitor reference set for bot tracking');
}

// Global variable for Firebase initialization status
let firebaseInitialized = false;

// Initialize Firebase Admin SDK with service account
try {
  const serviceAccountPath = path.join(__dirname, 'serviceAccountKey.json');
  if (fs.existsSync(serviceAccountPath)) {
    // Load the service account file directly instead of using require
    const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
    console.log("Firebase Admin SDK initialized with service account");
    firebaseInitialized = true;
  } else {
    console.warn("Service account file not found at:", serviceAccountPath);
    firebaseInitialized = false;
  }
} catch (error) {
  console.error("Failed to initialize Firebase Admin SDK:", error);
  firebaseInitialized = false;
}

// Now import authentication middlewares after Firebase initialization
const { authenticateToken, authorize, checkInstanceOwnership } = require('./auth');

// Add JWKS client for Firebase token verification without Admin SDK
const jwksRsa = require('jwks-rsa');
const firebaseJwksClient = jwksRsa({
  jwksUri: 'https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>',
  cache: true,
  cacheMaxEntries: 10,
  cacheMaxAge: 24 * 60 * 60 * 1000,
  rateLimit: true,
  jwksRequestsPerMinute: 10
});

async function verifyFirebaseIdTokenWithoutAdmin(token) {
  try {
    const decodedHeader = jwt.decode(token, { complete: true });
    const kid = decodedHeader?.header?.kid;
    if (!kid) throw new Error('Missing kid in token header');

    const key = await firebaseJwksClient.getSigningKeyAsync(kid);
    const publicKey = key.getPublicKey();

    // Prefer configured project id, else fall back to token aud
    const prelimPayload = jwt.decode(token);
    const projectId = process.env.FIREBASE_PROJECT_ID || prelimPayload?.aud;
    if (!projectId) throw new Error('Unknown Firebase projectId (set FIREBASE_PROJECT_ID)');
    const issuer = `https://securetoken.google.com/${projectId}`;

    const verified = jwt.verify(token, publicKey, {
      algorithms: ['RS256'],
      audience: projectId,
      issuer
    });

    return {
      id: verified.user_id || verified.uid || verified.sub,
      uid: verified.user_id || verified.uid || verified.sub,
      email: verified.email,
      role: verified.admin ? 'admin' : 'user'
    };
  } catch (err) {
    console.warn(`[Auth] Firebase JWKS verification failed: ${err.message}`);
    return null;
  }
}

// --- Constants ---
const PORT = process.env.PORT || 3001;
const BOT_BASE_DIR = process.env.BOT_BASE_DIR || path.join(__dirname, '..', 'freqtrade-instances'); // Base dir for individual bot instances
const FREQTRADE_IMAGE = 'freqtradeorg/freqtrade:stable'; // Always use stable FreqTrade image for local SQLite
console.log(`Using FREQTRADE_IMAGE: ${FREQTRADE_IMAGE}`);
console.log(`CRITICAL: All new bots will use the stable image with local SQLite DB: ${FREQTRADE_IMAGE}`);
// Shared strategies dir (used ONLY for fallback default strategy creation if main source is empty/missing)
const STRATEGIES_DIR = process.env.STRATEGIES_DIR || path.join(__dirname, 'freqtrade-shared', 'strategies');
// Main source directory on HOST where strategies are copied FROM during provisioning
const MAIN_STRATEGIES_SOURCE_DIR = process.env.MAIN_STRATEGIES_SOURCE_DIR || '/root/Crypto-Pilot-Freqtrade/Admin Strategies/';
// SHARED data directory on HOST where historical data resides (must be managed separately)
const SHARED_DATA_DIR = process.env.SHARED_DATA_DIR || '/root/freqtrade-shared'; // All bots will read data from subdirs here

// --- Queue System for Provisioning ---
const provisioningQueue = [];
let isProvisioning = false;

// --- Create Express App ---
const app = express();

// Configure Express to trust proxy headers for rate limiting (localhost + nginx only)
app.set('trust proxy', 'loopback');

// --- Middleware ---
// Apply Helmet security headers with enhanced CSP for API access
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"], // Allow inline scripts for test client
      scriptSrcAttr: ["'unsafe-inline'"], // Allow inline event handlers
      styleSrc: ["'self'", "https:", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:"],
      fontSrc: ["'self'", "https:", "data:"],
      connectSrc: ["'self'", "https://freqtrade.crypto-pilot.dev", "wss://freqtrade.crypto-pilot.dev"], // Allow EventSource, API, and WebSocket connections
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
}));

// Configure global rate limiter
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per 15-minute window (more reasonable for API usage)
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    success: false,
    message: "Too many requests, please try again later."
  }
});

// Apply global rate limiter to all requests except critical endpoints
app.use((req, res, next) => {
  // Skip rate limiting for critical endpoints that need frequent access
  const exemptPaths = [
    '/api/stream',           // SSE streaming endpoint
    '/api/verify-token',     // Token verification
    '/api/bots',             // Bot listing (needed for dashboard)
    '/api/portfolio/history', // Portfolio history (needed for charts)
    '/api/charts/portfolio',  // Portfolio charts
    '/api/strategies'         // Strategy management endpoints
  ];

  if (exemptPaths.some(path => req.path.startsWith(path))) {
    return next();
  }

  // Apply rate limiting to other endpoints
  return globalLimiter(req, res, next);
});

// Additional strict rate limiter for token verification endpoint
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 verify attempts per 15-minute window
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    message: "Too many token verification attempts, please try again later."
  }
});

// Implement proper CORS with allowed origins from environment variable
const allowedOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(',')
  : [
    'http://localhost:5173',
    'http://localhost:3001',
    'https://freqtrade.crypto-pilot.dev',
    'https://crypto-pilot.dev',
    'https://www.crypto-pilot.dev',
    'https://app.crypto-pilot.dev',
    'https://dashboard.crypto-pilot.dev'
  ];

console.log('🌐 CORS allowed origins:', allowedOrigins);

// CORS disabled in Node.js - handled by Nginx proxy to prevent duplicate headers
// app.use(cors({
//   origin: function (origin, callback) {
//     // Allow requests with no origin (like mobile apps, curl, Postman, etc)
//     if (!origin) {
//       console.log('🌐 CORS: Allowing request with no origin (direct API call)');
//       return callback(null, true);
//     }

//     console.log(`🌐 CORS: Checking origin: ${origin}`);

//     if (allowedOrigins.indexOf(origin) !== -1) {
//       console.log(`✅ CORS: Origin ${origin} allowed`);
//       callback(null, true);
//     } else {
//       console.warn(`❌ CORS: Policy violation from origin: ${origin}`);
//       console.warn(`   Allowed origins: ${allowedOrigins.join(', ')}`);
//       callback(new Error('CORS policy violation'), false);
//     }
//   },
//   credentials: true,
//   methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
//   allowedHeaders: ['Content-Type', 'Authorization'],
//   optionsSuccessStatus: 200 // For legacy browser support
// }));

app.use(express.json()); // Parse JSON request bodies

// Serve static files from tests directory
app.use('/tests', express.static(path.join(__dirname, 'tests')));

// Healthcheck endpoints
function healthPayload() {
  return {
    ok: true,
    status: 'ok',
    service: 'bot-manager',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  };
}
app.get('/health', (req, res) => {
  res.set('Cache-Control', 'no-store');
  res.status(200).json(healthPayload());
});
app.head('/health', (req, res) => res.status(200).end());
app.get('/api/health', (req, res) => {
  res.set('Cache-Control', 'no-store');
  res.status(200).json(healthPayload());
});
app.head('/api/health', (req, res) => res.status(200).end());

// Serve the test streaming client
app.get('/test-streaming-client.html', (req, res) => {
  const clientPath = path.join(__dirname, 'tests', 'test-streaming-client.html');
  res.sendFile(clientPath);
});

// Serve the test streaming client (alternate path)
app.get('/api/tests/test-streaming-client.html', (req, res) => {
  const clientPath = path.join(__dirname, 'tests', 'test-streaming-client.html');
  res.sendFile(clientPath);
});

// Helper: collect used bot API ports from existing instance configs
async function getUsedBotPorts() {
  const used = new Set();
  if (!(await fs.pathExists(BOT_BASE_DIR))) return used;
  const users = await fs.readdir(BOT_BASE_DIR);
  for (const uid of users) {
    const userDir = path.join(BOT_BASE_DIR, uid);
    try {
      const insts = await fs.readdir(userDir);
      for (const inst of insts) {
        const cfg = path.join(userDir, inst, 'config.json');
        if (await fs.pathExists(cfg)) {
          try {
            const data = JSON.parse(await fs.readFile(cfg, 'utf8'));
            const p = data?.api_server?.listen_port;
            if (p) used.add(Number(p));
          } catch { /* ignore */ }
        }
      }
    } catch { /* ignore */ }
  }
  return used;
}

async function getNextAvailablePort(start = 8100) {
  const used = await getUsedBotPorts();
  let p = start;
  while (used.has(p)) p += 1;
  return p;
}

function defaultInstanceIdForUser(user) {
  const prefix = (user?.email?.split('@')[0] || user?.uid || user?.id || 'user').replace(/[^a-zA-Z0-9_-]/g, '');
  const suffix = new Date().toISOString().replace(/[-:.TZ]/g, '').slice(0, 14);
  return `${prefix}-bot-${suffix}`;
}

// --- Enhanced Provisioning API with Risk Management ---
app.post('/api/provision-enhanced', authenticateToken, async (req, res) => {
  try {
    const user = req.user || {};
    const userId = user.uid || user.id;
    if (!userId) return res.status(401).json({ success: false, message: 'Unauthorized' });

    let {
      instanceId,
      port,
      apiUsername,
      apiPassword,
      strategy,
      riskTemplate,
      customRiskConfig,
      tradingPairs,
      initialBalance,
      exchangeConfig
    } = req.body || {};

    // Set defaults
    if (!instanceId) instanceId = defaultInstanceIdForUser(user);
    if (!port) port = await getNextAvailablePort(8100);
    if (!apiUsername) apiUsername = process.env.DEFAULT_BOT_API_USERNAME || 'admin';
    if (!apiPassword) apiPassword = process.env.DEFAULT_BOT_API_PASSWORD || 'password';
    if (!strategy) strategy = 'EnhancedRiskManagedStrategy'; // Default to enhanced strategy
    if (!riskTemplate) riskTemplate = 'balanced'; // Default risk template
    if (!tradingPairs) tradingPairs = ["BTC/USD", "ETH/USD", "ADA/USD", "SOL/USD"];
    if (!initialBalance) initialBalance = 10000;

    // Enhanced provisioning parameters
    const enhancedParams = {
      instanceId,
      port,
      userId,
      apiUsername,
      apiPassword,
      strategy,
      riskTemplate,
      customRiskConfig,
      tradingPairs,
      initialBalance,
      exchangeConfig,
      enhanced: true
    };

    provisioningQueue.push({ params: enhancedParams, res });

    // Kick the processor if idle
    if (!isProvisioning) processProvisioningQueue();
  } catch (e) {
    console.error('[API] /api/provision-enhanced error:', e.message);
    if (!res.headersSent) res.status(500).json({ success: false, message: e.message });
  }
});

// --- Original Provisioning API (maintained for compatibility) ---
app.post('/api/provision', authenticateToken, async (req, res) => {
  try {
    const user = req.user || {};
    const userId = user.uid || user.id;
    if (!userId) return res.status(401).json({ success: false, message: 'Unauthorized' });

    let { instanceId, port, apiUsername, apiPassword, strategy } = req.body || {};
    if (!instanceId) instanceId = defaultInstanceIdForUser(user);
    if (!port) port = await getNextAvailablePort(8100);
    if (!apiUsername) apiUsername = process.env.DEFAULT_BOT_API_USERNAME || 'admin';
    if (!apiPassword) apiPassword = process.env.DEFAULT_BOT_API_PASSWORD || 'password';
    if (!strategy) strategy = 'EmaRsiStrategy'; // Keep original default for compatibility

    provisioningQueue.push({ params: { instanceId, port, userId, apiUsername, apiPassword, strategy }, res });

    // Kick the processor if idle
    if (!isProvisioning) processProvisioningQueue();
  } catch (e) {
    console.error('[API] /api/provision error:', e.message);
    if (!res.headersSent) res.status(500).json({ success: false, message: e.message });
  }
});

// --- Get enhanced strategy options ---
app.get('/api/strategies/enhanced', authenticateToken, async (req, res) => {
  try {
    console.log('[API] Getting enhanced strategy options');

    const strategies = [
      {
        name: 'EnhancedRiskManagedStrategy',
        displayName: 'Enhanced Risk Management',
        description: 'Advanced strategy with dynamic position sizing, risk management, and DCA capabilities',
        features: ['Dynamic Position Sizing', 'Advanced Risk Management', 'Volatility-based Stops', 'Portfolio Risk Assessment'],
        riskLevel: 'Medium',
        recommendedFor: 'Experienced traders who want comprehensive risk management',
        defaultRiskTemplate: 'balanced'
      },
      {
        name: 'DCAStrategy',
        displayName: 'Dollar Cost Averaging',
        description: 'Systematic buying strategy that averages down on dips with multiple entry levels',
        features: ['Multi-level DCA', 'Smart Entry Timing', 'Position Size Scaling', 'Time-based Spacing'],
        riskLevel: 'Medium-Low',
        recommendedFor: 'Long-term investors who prefer systematic accumulation',
        defaultRiskTemplate: 'dcaFocused'
      },
      {
        name: 'PortfolioRebalancingStrategy',
        displayName: 'Portfolio Rebalancing',
        description: 'Maintains target allocations across multiple assets with automatic rebalancing',
        features: ['Target Allocation Management', 'Drift Detection', 'Automated Rebalancing', 'Risk Parity'],
        riskLevel: 'Low-Medium',
        recommendedFor: 'Portfolio managers who want automated allocation management',
        defaultRiskTemplate: 'portfolioRebalancing'
      },
      {
        name: 'EmaRsiStrategy',
        displayName: 'EMA-RSI Classic',
        description: 'Traditional EMA crossover strategy with RSI confirmation (original strategy)',
        features: ['EMA Crossover', 'RSI Filter', 'Simple Logic', 'Proven Approach'],
        riskLevel: 'Medium',
        recommendedFor: 'Beginners who want a simple, well-tested strategy',
        defaultRiskTemplate: 'conservative'
      },
      {
        name: 'HighFrequencyStrategy',
        displayName: 'High Frequency Trading',
        description: 'Fast-moving strategy for quick trades and scalping opportunities',
        features: ['Fast Execution', 'Short Timeframes', 'Quick Profits', 'High Turnover'],
        riskLevel: 'High',
        recommendedFor: 'Active traders comfortable with high-frequency trading',
        defaultRiskTemplate: 'aggressive'
      }
    ];

    console.log(`[API] Returning ${strategies.length} enhanced strategies`);
    res.json({ success: true, strategies });
  } catch (e) {
    console.error('[API] Error getting enhanced strategies:', e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Bot proxy convenience endpoints ---
app.get('/api/bots/:instanceId/balance', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const data = await proxyFreqtradeApiRequest(req.params.instanceId, '/api/v1/balance');
    res.json(data);
  } catch (e) {
    res.status(502).json({ error: e.message });
  }
});

app.get('/api/bots/:instanceId/status', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const data = await proxyFreqtradeApiRequest(req.params.instanceId, '/api/v1/status');
    res.json(data);
  } catch (e) {
    res.status(502).json({ error: e.message });
  }
});

app.get('/api/bots/:instanceId/profit', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const data = await proxyFreqtradeApiRequest(req.params.instanceId, '/api/v1/profit');
    res.json(data);
  } catch (e) {
    res.status(502).json({ error: e.message });
  }
});

// --- List all bots for authenticated user ---
app.get('/api/bots', authenticateToken, async (req, res) => {
  try {
    const user = req.user || {};
    const userId = user.uid || user.id;
    if (!userId) return res.status(401).json({ success: false, message: 'Unauthorized' });

    const bots = await listUserBotInstances(userId);
    res.json({ success: true, bots });
  } catch (e) {
    console.error('[API] /api/bots error:', e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Stop a bot ---
app.post('/api/bots/:instanceId/stop', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const containerName = `freqtrade-${instanceId}`;

    console.log(`[API] Stopping bot ${instanceId} (container: ${containerName})`);

    // Stop the Docker container
    await runDockerCommand(['stop', containerName]);
    console.log(`[API] ✓ Container ${containerName} stopped successfully`);

    res.json({ success: true, message: `Bot ${instanceId} stopped successfully` });
  } catch (e) {
    console.error(`[API] Error stopping bot ${req.params.instanceId}:`, e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Start a bot ---
app.post('/api/bots/:instanceId/start', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const containerName = `freqtrade-${instanceId}`;

    console.log(`[API] Starting bot ${instanceId} (container: ${containerName})`);

    // Start the Docker container
    await runDockerCommand(['start', containerName]);
    console.log(`[API] ✓ Container ${containerName} started successfully`);

    res.json({ success: true, message: `Bot ${instanceId} started successfully` });
  } catch (e) {
    console.error(`[API] Error starting bot ${req.params.instanceId}:`, e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Delete a bot ---
app.delete('/api/bots/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const user = req.user || {};
    const userId = user.uid || user.id;
    const containerName = `freqtrade-${instanceId}`;

    console.log(`[API] Deleting bot ${instanceId} for user ${userId}`);

    // 1. Stop and remove the Docker container
    try {
      await runDockerCommand(['stop', containerName]);
      console.log(`[API] ✓ Container ${containerName} stopped`);
    } catch (stopErr) {
      console.log(`[API] Container ${containerName} was not running: ${stopErr.message}`);
    }

    try {
      await runDockerCommand(['rm', '-f', containerName]);
      console.log(`[API] ✓ Container ${containerName} removed`);
    } catch (rmErr) {
      console.log(`[API] Container ${containerName} removal failed: ${rmErr.message}`);
    }

    // 2. Remove the instance directory
    const instanceDir = path.join(BOT_BASE_DIR, userId, instanceId);
    if (await fs.pathExists(instanceDir)) {
      await fs.remove(instanceDir);
      console.log(`[API] ✓ Instance directory removed: ${instanceDir}`);
    }

    // 3. Optional: Clean up Turso database if it exists
    if (TURSO_API_KEY && TURSO_ORG && !tursoGloballyDisabled) {
      try {
        const tursoName = `bot-${userId}-${instanceId}`.toLowerCase()
          .replace(/[^a-z0-9-]/g, '-')
          .replace(/-+/g, '-')
          .replace(/(^-|-$)/g, '');

        const deleteResponse = await fetch(`https://api.turso.tech/v1/organizations/${TURSO_ORG}/databases/${tursoName}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${TURSO_API_KEY}`,
          }
        });

        if (deleteResponse.ok) {
          console.log(`[API] ✓ Turso database ${tursoName} deleted`);
        } else {
          console.log(`[API] Turso database deletion failed (non-critical): ${deleteResponse.status}`);
        }
      } catch (tursoErr) {
        console.log(`[API] Turso cleanup failed (non-critical): ${tursoErr.message}`);
      }
    }

    res.json({ success: true, message: `Bot ${instanceId} deleted successfully` });
  } catch (e) {
    console.error(`[API] Error deleting bot ${req.params.instanceId}:`, e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Get bot details ---
app.get('/api/bots/:instanceId', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const user = req.user || {};
    const userId = user.uid || user.id;

    // Get bot configuration
    const instanceDir = path.join(BOT_BASE_DIR, userId, instanceId);
    const configPath = path.join(instanceDir, 'config.json');

    if (!await fs.pathExists(configPath)) {
      return res.status(404).json({ success: false, message: 'Bot not found' });
    }

    const config = JSON.parse(await fs.readFile(configPath, 'utf8'));
    const containerName = `freqtrade-${instanceId}`;

    // Check container status
    let containerStatus = 'unknown';
    try {
      const statusOutput = await runDockerCommand(['ps', '-f', `name=${containerName}`, '--format', '{{.Names}}']);
      containerStatus = statusOutput.includes(containerName) ? 'running' : 'stopped';
    } catch (statusErr) {
      console.warn(`[API] Could not check container status: ${statusErr.message}`);
    }

    const botInfo = {
      instanceId,
      userId,
      strategy: config.strategy,
      port: config.api_server?.listen_port,
      containerName,
      containerStatus,
      exchange: config.exchange?.name,
      dry_run: config.dry_run,
      stake_currency: config.stake_currency,
      stake_amount: config.stake_amount,
      max_open_trades: config.max_open_trades,
      created: instanceDir // You might want to add actual creation timestamp
    };

    res.json({ success: true, bot: botInfo });
  } catch (e) {
    console.error(`[API] Error getting bot details ${req.params.instanceId}:`, e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Strategy Management Endpoints ---

// Get all available strategies
app.get('/api/strategies', authenticateToken, async (req, res) => {
  try {
    console.log('[API] Getting available strategies');

    const strategies = [];

    // Read strategies from main source directory
    if (await fs.pathExists(MAIN_STRATEGIES_SOURCE_DIR)) {
      const files = await fs.readdir(MAIN_STRATEGIES_SOURCE_DIR);
      const pyFiles = files.filter(f => f.endsWith('.py'));

      for (const file of pyFiles) {
        const strategyName = file.replace('.py', '');
        const filePath = path.join(MAIN_STRATEGIES_SOURCE_DIR, file);

        try {
          const content = await fs.readFile(filePath, 'utf8');

          // Extract class name and basic info from strategy file
          const classMatch = content.match(/class\s+(\w+)\s*\(/);
          const className = classMatch ? classMatch[1] : strategyName;

          // Extract docstring if available
          const docMatch = content.match(/class\s+\w+\s*\([^)]*\):\s*"""([^"]+)"""/);
          const description = docMatch ? docMatch[1].trim() : `${strategyName} trading strategy`;

          strategies.push({
            name: strategyName,
            className: className,
            description: description,
            fileName: file
          });
        } catch (err) {
          console.warn(`[API] Error reading strategy file ${file}: ${err.message}`);
          strategies.push({
            name: strategyName,
            className: strategyName,
            description: `${strategyName} trading strategy`,
            fileName: file
          });
        }
      }
    }

    console.log(`[API] Found ${strategies.length} strategies`);
    res.json({ success: true, strategies });
  } catch (e) {
    console.error('[API] Error getting strategies:', e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// Get current strategy for a specific bot
app.get('/api/bots/:instanceId/strategy', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const user = req.user || {};
    const userId = user.uid || user.id;

    console.log(`[API] Getting strategy for bot ${instanceId}`);

    const instanceDir = path.join(BOT_BASE_DIR, userId, instanceId);
    const configPath = path.join(instanceDir, 'config.json');

    if (!await fs.pathExists(configPath)) {
      return res.status(404).json({ success: false, message: 'Bot configuration not found' });
    }

    const config = JSON.parse(await fs.readFile(configPath, 'utf8'));

    res.json({
      success: true,
      strategy: {
        current: config.strategy,
        instanceId: instanceId
      }
    });
  } catch (e) {
    console.error(`[API] Error getting bot strategy ${req.params.instanceId}:`, e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// Update strategy for a specific bot and restart it
app.put('/api/bots/:instanceId/strategy', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const { strategy } = req.body;
    const user = req.user || {};
    const userId = user.uid || user.id;

    if (!strategy) {
      return res.status(400).json({ success: false, message: 'Strategy name is required' });
    }

    console.log(`[API] Updating strategy for bot ${instanceId} to ${strategy}`);

    const instanceDir = path.join(BOT_BASE_DIR, userId, instanceId);
    const configPath = path.join(instanceDir, 'config.json');

    if (!await fs.pathExists(configPath)) {
      return res.status(404).json({ success: false, message: 'Bot configuration not found' });
    }

    // Verify the strategy exists
    const strategyFile = path.join(MAIN_STRATEGIES_SOURCE_DIR, `${strategy}.py`);
    if (!await fs.pathExists(strategyFile)) {
      return res.status(400).json({ success: false, message: 'Strategy not found' });
    }

    // Update the config file
    const config = JSON.parse(await fs.readFile(configPath, 'utf8'));
    const oldStrategy = config.strategy;
    config.strategy = strategy;

    await fs.writeFile(configPath, JSON.stringify(config, null, 2));
    console.log(`[API] ✓ Updated config.json strategy from ${oldStrategy} to ${strategy}`);

    // Copy the new strategy file to the bot's strategies directory
    const instanceStrategiesDir = path.join(instanceDir, 'user_data', 'strategies');
    await fs.ensureDir(instanceStrategiesDir);

    const destStrategyFile = path.join(instanceStrategiesDir, `${strategy}.py`);
    await fs.copy(strategyFile, destStrategyFile);
    console.log(`[API] ✓ Copied strategy file to ${destStrategyFile}`);

    // Restart the bot to apply the new strategy
    const containerName = `freqtrade-${instanceId}`;

    try {
      // Check if container is running
      const statusOutput = await runDockerCommand(['ps', '--filter', `name=${containerName}`, '--format', '{{.Names}}']);
      const isRunning = statusOutput.includes(containerName);

      if (isRunning) {
        console.log(`[API] Restarting bot ${instanceId} to apply new strategy...`);
        await runDockerCommand(['restart', containerName]);
        console.log(`[API] ✓ Container ${containerName} restarted successfully`);
      } else {
        console.log(`[API] Bot ${instanceId} is not running, strategy updated but not restarted`);
      }

      res.json({
        success: true,
        message: `Strategy updated to ${strategy}${isRunning ? ' and bot restarted' : ' (bot was not running)'}`,
        strategy: {
          previous: oldStrategy,
          current: strategy,
          restarted: isRunning
        }
      });
    } catch (restartErr) {
      console.error(`[API] Error restarting bot ${instanceId}:`, restartErr.message);
      res.json({
        success: true,
        message: `Strategy updated to ${strategy} but failed to restart bot: ${restartErr.message}`,
        strategy: {
          previous: oldStrategy,
          current: strategy,
          restarted: false
        }
      });
    }
  } catch (e) {
    console.error(`[API] Error updating bot strategy ${req.params.instanceId}:`, e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Get historical portfolio data ---
app.get('/api/portfolio/history', authenticateToken, async (req, res) => {
  try {
    const user = req.user || {};
    const userId = user.uid || user.id;
    if (!userId) return res.status(401).json({ success: false, message: 'Unauthorized' });

    // Parse query parameters
    const limit = Math.min(parseInt(req.query.limit) || 100, 1000); // Max 1000 snapshots
    const from = req.query.from ? parseInt(req.query.from) : null;
    const to = req.query.to ? parseInt(req.query.to) : null;

    // Get portfolio snapshots
    const userDir = path.join(BOT_BASE_DIR, userId);
    const snapshotsFile = path.join(userDir, 'portfolio_snapshots.json');

    if (!await fs.pathExists(snapshotsFile)) {
      return res.json({
        success: true,
        snapshots: [],
        count: 0,
        period: { from: null, to: null }
      });
    }

    const snapshotsData = JSON.parse(await fs.readFile(snapshotsFile, 'utf8'));
    let snapshots = snapshotsData.snapshots || [];

    // Filter by time range if specified
    if (from || to) {
      snapshots = snapshots.filter(snapshot => {
        const ts = snapshot.timestamp;
        if (from && ts < from) return false;
        if (to && ts > to) return false;
        return true;
      });
    }

    // Apply limit (get most recent)
    if (snapshots.length > limit) {
      snapshots = snapshots.slice(-limit);
    }

    const period = {
      from: snapshots.length > 0 ? snapshots[0].timestamp : null,
      to: snapshots.length > 0 ? snapshots[snapshots.length - 1].timestamp : null
    };

    res.json({
      success: true,
      snapshots,
      count: snapshots.length,
      period
    });

  } catch (e) {
    console.error('[API] /api/portfolio/history error:', e.message);
    res.status(500).json({ success: false, message: e.message });
  }
});

// --- Ensure Base Directories Exist ---
(async () => {
  try {
    console.log(`Ensuring bot instance base directory exists: ${BOT_BASE_DIR}`);
    await fs.ensureDir(BOT_BASE_DIR);
    console.log(`Ensuring shared strategies directory exists (for fallback): ${STRATEGIES_DIR}`);
    await fs.ensureDir(STRATEGIES_DIR);
    console.log(`Checking main strategies source directory: ${MAIN_STRATEGIES_SOURCE_DIR}`);
    if (!await fs.pathExists(MAIN_STRATEGIES_SOURCE_DIR)) {
      console.warn(`WARNING: Main strategies source directory (${MAIN_STRATEGIES_SOURCE_DIR}) does not exist.`);
    } else { console.log(`Main strategies source directory found.`); }
    console.log(`Ensuring base shared data directory exists: ${SHARED_DATA_DIR}`);
    await fs.ensureDir(SHARED_DATA_DIR); // Create the base shared data dir if it doesn't exist
    console.log("Base directories ensured/checked.");

    // Validate Turso API token on startup
    if (TURSO_API_KEY && TURSO_ORG) {
      console.log("Validating Turso API token on startup...");
      const tokenValid = await validateAndRefreshTursoToken();
      if (!tokenValid) {
        console.warn('⚠️  Turso API token validation failed on startup');
        console.warn('   Turso sync is globally disabled to prevent repeated failed API calls');
        console.warn('   To resolve: node refresh-turso-token.js --renew');
      }
    } else {
      console.log("Turso API configuration not found (TURSO_API_KEY or TURSO_ORG missing)");
      console.log("Turso sync will be disabled");
      tursoGloballyDisabled = true;
    }

    // Create fallback default strategy in SHARED strategy dir if it's empty
    const sharedStrategyFiles = await fs.readdir(STRATEGIES_DIR);
    if (sharedStrategyFiles.filter(f => f.endsWith('.py')).length === 0) {
      const dummyStrategyPath = path.join(STRATEGIES_DIR, 'DefaultStrategy.py');
      const dummyStrategyContent = `
import talib.abstract as ta
from pandas import DataFrame # Ensure DataFrame is imported
from freqtrade.strategy import IStrategy, IntParameter
import freqtrade.vendor.qtpylib.indicators as qtpylib
class DefaultStrategy(IStrategy):
    INTERFACE_VERSION = 3; minimal_roi = {"0": 0.01}; stoploss = -0.10; timeframe = '5m'
    process_only_new_candles = True; startup_candle_count: int = 20; use_exit_signal = True; exit_profit_only = False
    buy_rsi = IntParameter(10, 40, default=30, space='buy'); sell_rsi = IntParameter(60, 90, default=70, space='sell')
    def populate_indicators(self, df: DataFrame, md: dict) -> DataFrame: df['rsi'] = ta.RSI(df); return df
    def populate_entry_trend(self, df: DataFrame, md: dict) -> DataFrame: df.loc[(qtpylib.crossed_below(df['rsi'], self.buy_rsi.value)), 'enter_long'] = 1; return df
    def populate_exit_trend(self, df: DataFrame, md: dict) -> DataFrame: df.loc[(qtpylib.crossed_above(df['rsi'], self.sell_rsi.value)), 'exit_long'] = 1; return df
`;
      if (!await fs.pathExists(dummyStrategyPath)) { await fs.writeFile(dummyStrategyPath, dummyStrategyContent); console.log(`Created fallback DefaultStrategy: ${dummyStrategyPath}`); }
    }
  } catch (err) { console.error("FATAL: Directory setup failed.", err); process.exit(1); }
})();


// --- Process the Provisioning Queue (Using Strategy Copy & CORRECTED Shared Data Volume) ---
async function processProvisioningQueue() {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [Queue Processor] processProvisioningQueue called. isProvisioning: ${isProvisioning}, Queue length: ${provisioningQueue.length}`);

  if (isProvisioning || provisioningQueue.length === 0) {
    console.log(`[${timestamp}] [Queue Processor] Exiting early - isProvisioning: ${isProvisioning}, queue empty: ${provisioningQueue.length === 0}`);
    return;
  }

  isProvisioning = true;
  const task = provisioningQueue.shift();
  console.log(`[${timestamp}] [Queue Processor] Dequeued task. Params:`, JSON.stringify(task?.params, null, 2));

  const {
    instanceId = 'ERR_ID',
    port = -1,
    userId = 'ERR_USR',
    apiUsername = 'ERR_API',
    apiPassword = 'ERR_PW',
    strategy = 'EmaRsiStrategy',
    riskTemplate = null,
    customRiskConfig = null,
    tradingPairs = ["BTC/USD", "ETH/USD", "ADA/USD", "SOL/USD"],
    initialBalance = 10000,
    exchangeConfig = null,
    enhanced = false
  } = task?.params || {};

  console.log(`[${timestamp}] [${instanceId}] PROVISIONING START - userId: ${userId}, port: ${port}, strategy: ${strategy}, enhanced: ${enhanced}`);

  if (enhanced) {
    console.log(`[${timestamp}] [${instanceId}] Enhanced provisioning with risk template: ${riskTemplate}, pairs: ${tradingPairs.join(', ')}`);
  }

  // Define paths: ensure user directory, then instance directory
  const userDir = path.join(BOT_BASE_DIR, userId);
  await fs.ensureDir(userDir);
  const instanceDir = path.join(userDir, instanceId);
  const userDataDir = path.join(instanceDir, 'user_data');
  const instanceStrategiesDir = path.join(userDataDir, 'strategies');
  const instanceDataDir = path.join(userDataDir, 'data');

  console.log(`[${instanceId}] STEP 0: Directory paths defined:
    userDir: ${userDir}
    instanceDir: ${instanceDir}
    userDataDir: ${userDataDir}
    instanceStrategiesDir: ${instanceStrategiesDir}
    instanceDataDir: ${instanceDataDir}`);

  try {
    console.log(`[${instanceId}] STEP 1: Creating instance directories...`);
    // --- 1. Create Instance Dirs ---
    await fs.ensureDir(userDir);
    console.log(`[${instanceId}] ✓ User directory created: ${userDir}`);

    await fs.ensureDir(instanceDir);
    console.log(`[${instanceId}] ✓ Instance directory created: ${instanceDir}`);

    await fs.ensureDir(userDataDir);
    console.log(`[${instanceId}] ✓ User data directory created: ${userDataDir}`);

    await fs.ensureDir(instanceStrategiesDir);
    console.log(`[${instanceId}] ✓ Strategies directory created: ${instanceStrategiesDir}`);

    await fs.ensureDir(path.join(userDataDir, 'logs'));
    console.log(`[${instanceId}] ✓ Logs directory created: ${path.join(userDataDir, 'logs')}`);

    await fs.ensureDir(instanceDataDir); // Create structure like user_data/data/
    console.log(`[${instanceId}] ✓ Data directory created: ${instanceDataDir}`);

    console.log(`[${instanceId}] STEP 1 COMPLETE: Instance directories created.`);

    console.log(`[${instanceId}] STEP 2: Setting permissions on user data directory...`);
    // --- 2. Set Permissions ---
    try {
      const chmod = spawn('chmod', ['-R', '777', userDataDir]);
      chmod.stderr.on('data', (data) => { console.error(`[${instanceId}] chmod stderr: ${data}`); });
      await new Promise((resolve, reject) => {
        chmod.on('error', (err) => {
          console.error(`[${instanceId}] Failed spawn chmod:`, err);
          reject(err);
        });
        chmod.on('close', (code) => {
          if (code === 0) {
            console.log(`[${instanceId}] ✓ Permissions set successfully on ${userDataDir}`);
            resolve();
          } else {
            console.error(`[${instanceId}] chmod exited with code ${code}`);
            reject(new Error(`chmod exited code ${code}`));
          }
        });
      });
      console.log(`[${instanceId}] STEP 2 COMPLETE: Permissions set.`);
    } catch (chmodError) {
      console.error(`[${instanceId}] STEP 2 FAILED: Permissions error:`, chmodError);
      throw new Error(`Permissions failed: ${chmodError.message}`);
    }

    console.log(`[${instanceId}] STEP 3: Copying strategies from ${MAIN_STRATEGIES_SOURCE_DIR} to ${instanceStrategiesDir}...`);
    // --- 3. Copy Strategies ---
    try {
      if (await fs.pathExists(MAIN_STRATEGIES_SOURCE_DIR)) {
        console.log(`[${instanceId}] ✓ Source strategies directory exists, starting copy...`);
        await fs.copy(MAIN_STRATEGIES_SOURCE_DIR, instanceStrategiesDir, {
          overwrite: true, filter: (src) => {
            const stats = fs.statSync(src);
            return stats.isDirectory() || path.extname(src) === '.py';
          }
        });
        console.log(`[${instanceId}] ✓ Strategies copied successfully.`);

        const copiedFiles = await fs.readdir(instanceStrategiesDir);
        const pyFiles = copiedFiles.filter(f => f.endsWith('.py'));
        console.log(`[${instanceId}] Copied strategy files: ${pyFiles.join(', ') || 'none'}`);

        if (pyFiles.length === 0) {
          console.warn(`[${instanceId}] WARNING: Source dir had no .py files, using fallback.`);
          const defaultStrategySource = path.join(STRATEGIES_DIR, 'DefaultStrategy.py');
          if (await fs.pathExists(defaultStrategySource)) {
            await fs.copy(defaultStrategySource, path.join(instanceStrategiesDir, 'DefaultStrategy.py'));
            console.log(`[${instanceId}] ✓ Copied fallback DefaultStrategy.`);
          }
        }
      } else {
        console.warn(`[${instanceId}] WARNING: Main source dir ${MAIN_STRATEGIES_SOURCE_DIR} not found, using fallback.`);
        const defaultStrategySource = path.join(STRATEGIES_DIR, 'DefaultStrategy.py');
        if (await fs.pathExists(defaultStrategySource)) {
          await fs.copy(defaultStrategySource, path.join(instanceStrategiesDir, 'DefaultStrategy.py'));
          console.log(`[${instanceId}] ✓ Copied fallback DefaultStrategy.`);
        } else {
          console.error(`[${instanceId}] ERROR: No strategies found anywhere!`);
        }
      }
      console.log(`[${instanceId}] STEP 3 COMPLETE: Strategy copying finished.`);
    } catch (copyError) {
      console.error(`[${instanceId}] STEP 3 ERROR: Strategy copy failed:`, copyError);
      console.warn(`[${instanceId}] Continuing despite copy error.`);
    }

    console.log(`[${instanceId}] STEP 4: Creating base configuration...`);
    // --- 4. Create config.json ---
    let defaultStrategyToUse = strategy; // Use the strategy from parameters (enhanced provisioning)
    const instanceStrategyFiles = await fs.readdir(instanceStrategiesDir);
    const pyFiles = instanceStrategyFiles.filter(f => f.endsWith('.py'));
    console.log(`[${instanceId}] Available strategy files: ${pyFiles.join(', ') || 'none'}`);

    // Check if requested strategy is available, otherwise fall back
    if (strategy && pyFiles.includes(`${strategy}.py`)) {
      defaultStrategyToUse = strategy;
      console.log(`[${instanceId}] Using requested strategy: ${strategy}`);
    } else if (pyFiles.length > 0) {
      // Priority order: EnhancedRiskManagedStrategy > EmaRsiStrategy > HighFrequencyStrategy > balancedStrat > any other > DefaultStrategy
      if (pyFiles.includes('EnhancedRiskManagedStrategy.py')) {
        defaultStrategyToUse = 'EnhancedRiskManagedStrategy';
        console.log(`[${instanceId}] Selected enhanced strategy: EnhancedRiskManagedStrategy`);
      } else if (pyFiles.includes('EmaRsiStrategy.py')) {
        defaultStrategyToUse = 'EmaRsiStrategy';
        console.log(`[${instanceId}] Selected classic strategy: EmaRsiStrategy`);
      } else if (pyFiles.includes('DCAStrategy.py')) {
        defaultStrategyToUse = 'DCAStrategy';
        console.log(`[${instanceId}] Selected DCA strategy`);
      } else if (pyFiles.includes('PortfolioRebalancingStrategy.py')) {
        defaultStrategyToUse = 'PortfolioRebalancingStrategy';
        console.log(`[${instanceId}] Selected portfolio rebalancing strategy`);
      } else if (pyFiles.includes('HighFrequencyStrategy.py')) {
        defaultStrategyToUse = 'HighFrequencyStrategy';
        console.log(`[${instanceId}] Selected HighFrequencyStrategy`);
      } else if (pyFiles.includes('balancedStrat.py')) {
        defaultStrategyToUse = 'balancedStrat';
        console.log(`[${instanceId}] Selected balancedStrat`);
      } else if (pyFiles.includes('DefaultStrategy.py')) {
        defaultStrategyToUse = 'DefaultStrategy';
        console.log(`[${instanceId}] Selected DefaultStrategy`);
      } else {
        defaultStrategyToUse = pyFiles[0].replace('.py', '');
        console.log(`[${instanceId}] Selected first available strategy: ${defaultStrategyToUse}`);
      }
    } else {
      console.warn(`[${instanceId}] No strategies in instance dir. Defaulting to ${defaultStrategyToUse}.`);
    }

    // Determine bot-level CORS origins (empty disables FreqTrade CORS middleware to avoid duplicate headers)
    const botCorsOriginsEnv = (process.env.BOT_CORS_ORIGINS || '')
      .split(',')
      .map(o => o.trim())
      .filter(o => o.length > 0);

    // Use enhanced trading pairs if provided
    const finalTradingPairs = tradingPairs && tradingPairs.length > 0 ? tradingPairs : ["BTC/USD", "ETH/USD", "ADA/USD", "SOL/USD", "DOT/USD"];

    // Use enhanced initial balance if provided
    const finalInitialBalance = initialBalance || 10000;

    // Enhanced timeframe selection based on strategy
    let timeframe = '15m'; // default
    if (defaultStrategyToUse === 'DCAStrategy') {
      timeframe = '1h'; // DCA strategies work better on longer timeframes
    } else if (defaultStrategyToUse === 'PortfolioRebalancingStrategy') {
      timeframe = '4h'; // Portfolio rebalancing needs longer timeframes
    } else if (defaultStrategyToUse === 'HighFrequencyStrategy') {
      timeframe = '5m'; // High frequency needs shorter timeframes
    }

    // Enforce local SQLite DB for local-first architecture with Turso sync
    const configJson = {
      userId: userId,
      max_open_trades: enhanced ? 20 : 25, // Slightly fewer for enhanced strategies for better risk management
      stake_currency: "USD",
      stake_amount: enhanced ? Math.floor(finalInitialBalance * 0.05) : 100, // Dynamic stake based on balance
      tradable_balance_ratio: 1,
      // CRITICAL: Set dry_run to true for safe trading that writes to local SQLite
      dry_run: true,
      dry_run_wallet: {
        "USD": finalInitialBalance,
        "BTC": Math.floor(finalInitialBalance * 0.0001), // Proportional BTC
        "ETH": Math.floor(finalInitialBalance * 0.003)   // Proportional ETH
      },
      cancel_open_orders_on_exit: false,
      trading_mode: "spot",
      margin_mode: "isolated",
      strategy: defaultStrategyToUse,
      // Local SQLite database with WAL mode for better concurrent access
      db_url: "sqlite:///user_data/tradesv3.sqlite?check_same_thread=False&timeout=60",
      logfile: "/freqtrade/user_data/logs/freqtrade.log",
      timeframe: timeframe,
      unfilledtimeout: { entry: 10, exit: 10, exit_timeout_count: 0, unit: "minutes" },
      entry_pricing: { price_side: "same", use_order_book: true, order_book_top: 1, price_last_balance: 0, check_depth_of_market: { enabled: false, bids_to_ask_delta: 1 } },
      exit_pricing: { price_side: "same", use_order_book: true, order_book_top: 1 },
      exchange: exchangeConfig || {
        name: "kraken",
        key: "", // Will need to be set via config update for live trading
        secret: "", // Will need to be set via config update for live trading
        ccxt_config: {},
        ccxt_async_config: {},
        pair_whitelist: finalTradingPairs,
        pair_blacklist: [],
        // Disable sandbox mode for proper trading (even in dry-run)
        sandbox: false
      },
      pairlists: [{ method: "StaticPairList" }],
      telegram: { enabled: false, token: "", chat_id: "" },
      api_server: {
        enabled: true,
        listen_ip_address: "0.0.0.0",
        listen_port: port,
        verbosity: "info",
        enable_openapi: true,
        jwt_secret_key: `aVeryStr0ngStaticPrefix!_${instanceId}_KeepSecret`,
        CORS_origins: botCorsOriginsEnv,
        username: apiUsername,
        password: apiPassword
      },
      bot_name: `${userId}-bot`,
      initial_state: "running",
      force_entry_enable: false,
      internals: {
        process_throttle_secs: enhanced ? 45 : 60, // Enhanced strategies can be more responsive
        heartbeat_interval: 300,   // 5 minutes heartbeat to reduce DB operations
        sd_notify: false           // Disable systemd notifications that can cause DB locks
      }
    };

    // Enhanced strategy-specific configurations
    if (enhanced && defaultStrategyToUse === 'EnhancedRiskManagedStrategy') {
      configJson.stoploss = -0.08; // Dynamic stoploss will override this
      configJson.trailing_stop = true;
      configJson.trailing_stop_positive = 0.02;
      configJson.trailing_stop_positive_offset = 0.04;
    } else if (enhanced && defaultStrategyToUse === 'DCAStrategy') {
      configJson.stoploss = -0.12; // More patient stoploss for DCA
      configJson.max_open_trades = 8; // Fewer positions for DCA focus
    } else if (enhanced && defaultStrategyToUse === 'PortfolioRebalancingStrategy') {
      configJson.stoploss = -0.15; // Even more patient for portfolio strategy
      configJson.max_open_trades = 10; // Moderate number for portfolio management
    }

    console.log(`[${instanceId}] Generated ${enhanced ? 'enhanced' : 'standard'} config with strategy: ${configJson.strategy}, pairs: ${finalTradingPairs.length}, timeframe: ${timeframe}, port: ${port}`);

    // Write base config.json immediately
    const configPath = path.join(instanceDir, 'config.json');
    await fs.writeFile(configPath, JSON.stringify(configJson, null, 2));
    console.log(`[${instanceId}] ✓ Base config.json written to: ${configPath}`);

    // If enhanced provisioning, also create and apply risk configuration
    if (enhanced && (riskTemplate || customRiskConfig)) {
      console.log(`[${instanceId}] ✓ Applying enhanced risk configuration...`);
      try {
        let riskConfig = customRiskConfig;

        // If using a template, load it
        if (riskTemplate && !customRiskConfig) {
          const riskTemplates = {
            conservative: {
              maxDrawdown: 0.10,
              maxTotalRisk: 0.15,
              riskPerTrade: 0.015,
              positionSizing: { baseStakePercent: 0.08, maxStakePercent: 0.15, volatilityAdjustment: true },
              stopLoss: { enabled: true, baseStopLoss: -0.06, trailingStop: true, dynamicAdjustment: true },
              dca: { enabled: true, maxOrders: 2, triggerPercent: -0.05, sizeMultiplier: 1.2 }
            },
            balanced: {
              maxDrawdown: 0.15,
              maxTotalRisk: 0.25,
              riskPerTrade: 0.02,
              positionSizing: { baseStakePercent: 0.10, maxStakePercent: 0.25, volatilityAdjustment: true },
              stopLoss: { enabled: true, baseStopLoss: -0.08, trailingStop: true, dynamicAdjustment: true },
              dca: { enabled: true, maxOrders: 3, triggerPercent: -0.08, sizeMultiplier: 1.5 }
            },
            aggressive: {
              maxDrawdown: 0.25,
              maxTotalRisk: 0.35,
              riskPerTrade: 0.03,
              positionSizing: { baseStakePercent: 0.15, maxStakePercent: 0.35, volatilityAdjustment: true },
              stopLoss: { enabled: true, baseStopLoss: -0.12, trailingStop: true, dynamicAdjustment: true },
              dca: { enabled: true, maxOrders: 5, triggerPercent: -0.12, sizeMultiplier: 2.0 }
            },
            dcaFocused: {
              maxDrawdown: 0.20,
              maxTotalRisk: 0.30,
              riskPerTrade: 0.02,
              positionSizing: { baseStakePercent: 0.12, maxStakePercent: 0.45, volatilityAdjustment: true },
              stopLoss: { enabled: true, baseStopLoss: -0.12, trailingStop: true, dynamicAdjustment: true },
              dca: { enabled: true, maxOrders: 5, triggerPercent: -0.05, sizeMultiplier: 1.5 }
            },
            portfolioRebalancing: {
              maxDrawdown: 0.18,
              maxTotalRisk: 0.25,
              riskPerTrade: 0.02,
              positionSizing: { baseStakePercent: 0.10, maxStakePercent: 0.30, volatilityAdjustment: true },
              stopLoss: { enabled: true, baseStopLoss: -0.10, trailingStop: false, dynamicAdjustment: true },
              rebalancing: { enabled: true, threshold: 0.15, frequency: 24, targetAllocations: { btc: 0.40, eth: 0.25, alt: 0.20, stable: 0.10, other: 0.05 } }
            }
          };

          riskConfig = riskTemplates[riskTemplate];
        }

        if (riskConfig) {
          const riskConfigPath = path.join(instanceDir, 'risk-config.json');
          await fs.writeFile(riskConfigPath, JSON.stringify(riskConfig, null, 2));
          console.log(`[${instanceId}] ✓ Risk configuration applied: ${riskTemplate || 'custom'}`);
        }
      } catch (riskErr) {
        console.warn(`[${instanceId}] Failed to apply risk configuration: ${riskErr.message}`);
      }
    }

    console.log(`[${instanceId}] STEP 4 COMPLETE: Base configuration created with local SQLite DB.`);

    console.log(`[${instanceId}] STEP 4.1: Initializing Universal Risk Management...`);
    // --- 4.1. Initialize Universal Risk Management for new bot ---
    try {
      const riskManager = new UniversalRiskManager(instanceId, instanceDir);

      // Initialize with enhanced settings if this is an enhanced bot
      if (enhanced) {
        const enhancedSettings = {
          riskLevel: Math.floor(50 + (Math.random() * 30)), // 50-80 range for enhanced bots
          autoRebalance: true,
          dcaEnabled: true,
          enabled: true
        };
        await riskManager.updateSettings(enhancedSettings);
        console.log(`[${instanceId}] ✓ Enhanced universal settings initialized:`, enhancedSettings);
      } else {
        // Use default settings for standard bots
        await riskManager.loadSettings();
        await riskManager.saveSettings();
        await riskManager.updateRiskConfig();
        console.log(`[${instanceId}] ✓ Default universal settings initialized`);
      }

      console.log(`[${instanceId}] STEP 4.1 COMPLETE: Universal Risk Management initialized.`);
    } catch (riskErr) {
      console.warn(`[${instanceId}] Failed to initialize universal risk management: ${riskErr.message}`);
    }

    console.log(`[${instanceId}] STEP 4.5: Setting up data directory structure...`);
    // --- 4.5. Set up data directory structure ---
    const instanceExchangeDataDir = path.join(instanceDataDir, configJson.exchange.name);

    try {
      // Ensure the exchange data directory exists in the instance
      await fs.ensureDir(instanceExchangeDataDir);
      console.log(`[${instanceId}] ✓ Created instance data directory: ${instanceExchangeDataDir}`);

      // If shared data exists, copy a subset for local use (to avoid read-only mount issues)
      const hostSharedExchangeDataPath = path.join(SHARED_DATA_DIR, configJson.exchange.name);
      if (await fs.pathExists(hostSharedExchangeDataPath)) {
        console.log(`[${instanceId}] Copying essential data from ${hostSharedExchangeDataPath}...`);

        // Copy only essential files (not all data to avoid huge copies)
        const sharedFiles = await fs.readdir(hostSharedExchangeDataPath);
        for (const file of sharedFiles) {
          const sourcePath = path.join(hostSharedExchangeDataPath, file);
          const destPath = path.join(instanceExchangeDataDir, file);
          const stats = await fs.stat(sourcePath);

          if (stats.isFile() && file.endsWith('.json')) {
            // Copy metadata files
            await fs.copy(sourcePath, destPath);
            console.log(`[${instanceId}] ✓ Copied metadata file: ${file}`);
          }
        }
      } else {
        console.log(`[${instanceId}] No shared data found at ${hostSharedExchangeDataPath}, will download fresh data`);
      }

      console.log(`[${instanceId}] STEP 4.5 COMPLETE: Data directory structure set up.`);
    } catch (dataSetupError) {
      console.warn(`[${instanceId}] Data directory setup failed (non-critical): ${dataSetupError.message}`);
    }

    console.log(`[${instanceId}] STEP 5: Creating local SQLite database file...`);
    // --- Create empty SQLite database file ---
    const dbPath = path.join(userDataDir, 'tradesv3.sqlite');
    console.log(`[${instanceId}] Creating SQLite database at: ${dbPath}`);

    // Create empty database file with proper permissions
    try {
      // Ensure the file exists (touch equivalent)
      await fs.ensureFile(dbPath);

      // Set proper permissions (readable/writable by all)
      const chmod = spawn('chmod', ['666', dbPath]);
      await new Promise((resolve, reject) => {
        chmod.on('error', reject);
        chmod.on('close', (code) => {
          if (code === 0) {
            console.log(`[${instanceId}] ✓ SQLite database file created with proper permissions`);
            resolve();
          } else {
            reject(new Error(`chmod failed with code ${code}`));
          }
        });
      });
    } catch (dbError) {
      console.error(`[${instanceId}] Error creating SQLite database file:`, dbError);
      throw new Error(`Failed to create SQLite database: ${dbError.message}`);
    }

    console.log(`[${instanceId}] STEP 5 COMPLETE: Local SQLite database created.`);
    console.log(`[${instanceId}] Final DB URL (local SQLite): ${configJson.db_url}`);

    console.log(`[${instanceId}] STEP 5.5: Setting up Turso sync for backup...`);
    // --- Optional: Create Turso database for syncing (if TURSO_API_KEY available) ---
    let tursoSyncEnabled = false;

    // Skip Turso entirely if previously disabled globally
    if (tursoGloballyDisabled) {
      console.log(`[${instanceId}] Turso operations globally disabled due to previous authentication failure`);
    } else if (TURSO_API_KEY && TURSO_ORG) {
      try {
        console.log(`[${instanceId}] Checking Turso configuration...`);

        // Only validate if not already validated recently
        let tokenValid = true;
        const timeSinceLastValidation = Date.now() - lastTursoValidationAttempt;

        if (timeSinceLastValidation > TURSO_VALIDATION_COOLDOWN) {
          console.log(`[${instanceId}] Validating Turso API token...`);
          tokenValid = await validateAndRefreshTursoToken();
        } else {
          console.log(`[${instanceId}] Using recent token validation (${Math.round(timeSinceLastValidation / 1000)}s ago)`);
        }

        if (tokenValid && !tursoGloballyDisabled) {
          // Reload the token from environment in case it was renewed
          const updatedEnv = require('dotenv').config({ path: path.join(__dirname, '.env') });
          const currentToken = updatedEnv.parsed?.TURSO_API_KEY || TURSO_API_KEY;

          const tursoNameOrig = `bot-${userId}-${instanceId}`;
          let tursoName = tursoNameOrig.toLowerCase()
            .replace(/[^a-z0-9-]/g, '-')
            .replace(/-+/g, '-')
            .replace(/(^-|-$)/g, '');

          console.log(`[${instanceId}] Creating Turso backup database: ${tursoName}`);

          // Create Turso database using REST API instead of CLI
          const fetch = require('node-fetch');

          const requestBody = {
            name: tursoName,
            group: 'default'
          };

          console.log(`[${instanceId}] API Request - URL: https://api.turso.tech/v1/organizations/${TURSO_ORG}/databases`);
          console.log(`[${instanceId}] API Request - Body: ${JSON.stringify(requestBody)}`);

          const createResponse = await fetch(`https://api.turso.tech/v1/organizations/${TURSO_ORG}/databases`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${currentToken}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
          });

          console.log(`[${instanceId}] Turso API Response Status: ${createResponse.status}`);

          if (createResponse.ok) {
            const responseData = await createResponse.json();
            console.log(`[${instanceId}] ✓ Turso backup database created successfully: ${tursoName}`);
            console.log(`[${instanceId}] Database details: ${JSON.stringify(responseData, null, 2)}`);

            // Create sync configuration file
            const syncConfigPath = path.join(instanceDir, 'sync-config.json');
            const syncConfig = {
              tursoDatabase: tursoName,
              localDatabase: dbPath,
              userId: userId,
              instanceId: instanceId,
              syncInterval: 300, // 5 minutes
              enabled: true
            };
            await fs.writeFile(syncConfigPath, JSON.stringify(syncConfig, null, 2));
            console.log(`[${instanceId}] ✓ Sync configuration created: ${syncConfigPath}`);

            tursoSyncEnabled = true;
          } else {
            const errorText = await createResponse.text();
            console.error(`[${instanceId}] Turso database creation failed (${createResponse.status}): ${errorText}`);

            // If database already exists, don't fail - just use it
            if (createResponse.status === 409 || errorText.includes('already exists')) {
              console.log(`[${instanceId}] Database ${tursoName} already exists, proceeding with sync setup...`);

              // Create sync configuration file anyway
              const syncConfigPath = path.join(instanceDir, 'sync-config.json');
              const syncConfig = {
                tursoDatabase: tursoName,
                localDatabase: dbPath,
                userId: userId,
                instanceId: instanceId,
                syncInterval: 300, // 5 minutes
                enabled: true
              };
              await fs.writeFile(syncConfigPath, JSON.stringify(syncConfig, null, 2));
              console.log(`[${instanceId}] ✓ Sync configuration created for existing database: ${syncConfigPath}`);

              tursoSyncEnabled = true;
            } else {
              // Only disable globally for authentication failures, not API errors
              if (createResponse.status === 401 || createResponse.status === 403) {
                console.warn(`[${instanceId}] Turso authentication failed - disabling globally`);
                tursoGloballyDisabled = true;
              }
              throw new Error(`Turso database creation failed (${createResponse.status}): ${errorText}`);
            }
          }
        } else {
          console.warn(`[${instanceId}] Turso token validation failed - skipping sync for this instance only`);
          console.warn(`[${instanceId}] This does NOT disable Turso globally - future instances can still try`);
        }
      } catch (tursoErr) {
        console.warn(`[${instanceId}] Turso sync setup failed: ${tursoErr.message}`);
        console.warn(`[${instanceId}] This is non-critical - bot will work without Turso sync`);

        // Only disable globally for authentication failures, not temporary API issues
        if (tursoErr.message.includes('authentication') || tursoErr.message.includes('401') || tursoErr.message.includes('403')) {
          console.warn(`[${instanceId}] Authentication error detected - disabling Turso globally`);
          tursoGloballyDisabled = true;
        } else {
          console.warn(`[${instanceId}] Temporary API error - NOT disabling Turso globally`);
        }
      }
    } else {
      console.log(`[${instanceId}] Turso sync disabled (missing TURSO_API_KEY or TURSO_ORG)`);
    }

    console.log(`[${instanceId}] STEP 5.5 COMPLETE: Turso sync ${tursoSyncEnabled ? 'enabled' : 'disabled'}.`);

    console.log(`[${instanceId}] STEP 6: Preparing Docker container...`);
    // --- 6. Start Container via docker compose ---
    const exchangeName = configJson.exchange.name;
    if (!exchangeName) {
      console.error(`[${instanceId}] Exchange name missing in configJson, cannot mount shared data.`);
      throw new Error("Exchange name missing in configJson, cannot mount shared data.");
    }
    console.log(`[${instanceId}] Exchange name: ${exchangeName}`);

    const hostSharedExchangeDataPath = path.join(SHARED_DATA_DIR, exchangeName);
    console.log(`[${instanceId}] Checking shared data path: ${hostSharedExchangeDataPath}`);
    if (!await fs.pathExists(hostSharedExchangeDataPath)) {
      console.warn(`[${instanceId}] WARNING: Shared data directory for exchange '${exchangeName}' (${hostSharedExchangeDataPath}) does not exist on host. Bot may fail if data is required.`);
    } else {
      console.log(`[${instanceId}] ✓ Shared data directory exists: ${hostSharedExchangeDataPath}`);
    }

    const containerName = `freqtrade-${instanceId}`;
    console.log(`[${instanceId}] Container name: ${containerName}`);

    // Remove any existing container with the same name to prevent conflicts
    console.log(`[${instanceId}] Removing any existing container '${containerName}' (if exists)...`);
    try {
      await runDockerCommand(['rm', '-f', containerName]);
      console.log(`[${instanceId}] ✓ Removed existing container '${containerName}'.`);
    } catch (rmErr) {
      console.log(`[${instanceId}] No existing container to remove: ${rmErr.message}`);
    }
    console.log(`[${instanceId}] Creating docker-compose configuration...`);
    // Use absolute paths for mounts
    const userDataHostPath = path.join(instanceDir, 'user_data');
    const configHostPath = path.join(instanceDir, 'config.json');

    console.log(`[${instanceId}] Mount paths:
      userDataHostPath: ${userDataHostPath}
      configHostPath: ${configHostPath}
      hostSharedExchangeDataPath: ${hostSharedExchangeDataPath}`);

    // --- Write docker-compose.yml for this instance (mybot1 style) ---
    const composePath = path.join(instanceDir, 'docker-compose.yml');
    const composeContent = `version: '3.8'
services:
  freqtrade:
    image: ${FREQTRADE_IMAGE}
    container_name: freqtrade-${instanceId}
    restart: unless-stopped
    volumes:
      # 1. Mount instance's user_data (logs, db, COPIED strategies, etc.) - Read/Write
      - ./user_data:/freqtrade/user_data
      # 2. Mount instance's config read-only
      - ./config.json:/freqtrade/config.json:ro
      # 3. Mount the specific SHARED host exchange data dir read-only to a separate location
      #    FreqTrade will use the data from user_data/data but we can symlink or copy from shared data
      - ${hostSharedExchangeDataPath}:/freqtrade/shared_data/${exchangeName}:ro
    ports:
      - "0.0.0.0:${port}:${port}"
    environment:
      - FREQTRADE_USER_DATA_DIR=/freqtrade/user_data
    command: trade --config /freqtrade/config.json --db-url ${configJson.db_url} --logfile /freqtrade/user_data/logs/freqtrade.log
`;
    await fs.writeFile(composePath, composeContent);
    console.log(`[${instanceId}] ✓ docker-compose.yml written to: ${composePath}`);

    console.log(`[${instanceId}] STEP 7: Starting Docker container...`);

    // Try starting with docker compose, fallback to direct docker run on failure
    try {
      console.log(`[${instanceId}] Attempting to start container via docker-compose...`);
      // Use 'docker-compose' binary directly for compatibility
      await runDockerCommand([
        '-f', composePath,
        'up', '-d'
      ], instanceDir, 'docker-compose');
      console.log(`[${instanceId}] ✓ Container started successfully via 'docker-compose'.`);
    } catch (composeErr) {
      console.warn(`[${instanceId}] docker-compose up failed: ${composeErr.message}. Falling back to direct docker run.`);
      console.log(`[${instanceId}] Attempting fallback: direct docker run...`);

      try {
        await runDockerCommand([
          'run', '-d', '--name', containerName,
          '-p', `${port}:${port}`,
          '-v', `${userDataHostPath}:/freqtrade/user_data`,
          '-v', `${configHostPath}:/freqtrade/config.json:ro`,
          '-v', `${hostSharedExchangeDataPath}:/freqtrade/user_data/data/${exchangeName}:ro`,
          FREQTRADE_IMAGE,
          'trade', '--config', '/freqtrade/config.json', '--db-url', configJson.db_url, '--logfile', '/freqtrade/user_data/logs/freqtrade.log'
        ], instanceDir);
        console.log(`[${instanceId}] ✓ Container started successfully via direct docker run fallback.`);
      } catch (dockerRunErr) {
        console.error(`[${instanceId}] STEP 7 FAILED: Both docker-compose and docker run failed.`);
        console.error(`[${instanceId}] docker-compose error:`, composeErr);
        console.error(`[${instanceId}] docker run error:`, dockerRunErr);
        throw dockerRunErr;
      }
    }

    console.log(`[${instanceId}] STEP 7 COMPLETE: Docker container started.`);

    // STEP 7.5: Perform initial sync to Turso (if enabled)
    if (tursoSyncEnabled) {
      console.log(`[${instanceId}] STEP 7.5: Performing initial ultra-optimized row-level sync to Turso...`);
      try {
        const syncScript = path.join(__dirname, '..', 'local-to-turso-sync-rowlevel.py');
        const syncArgs = [
          syncScript,
          '--instance-id', instanceId,
          '--user-id', userId,
          '--local-db', dbPath,
          '--turso-org', TURSO_ORG,
          '--turso-region', TURSO_REGION || 'us-east-1',
          '--create-if-missing'
        ];

        const syncProcess = spawn('python3', syncArgs, {
          env: {
            ...process.env,
            TURSO_API_KEY: TURSO_API_KEY
          }
        });

        await new Promise((resolve) => {
          syncProcess.on('close', (code) => {
            if (code === 0) {
              console.log(`[${instanceId}] ✓ Initial sync to Turso completed successfully`);
            } else {
              console.log(`[${instanceId}] Initial sync to Turso failed (non-critical), will retry in background`);
            }
            resolve();
          });
          syncProcess.on('error', (error) => {
            console.log(`[${instanceId}] Initial sync error (non-critical): ${error.message}`);
            resolve();
          });
        });
      } catch (syncError) {
        console.log(`[${instanceId}] Initial sync failed (non-critical): ${syncError.message}`);
      }
      console.log(`[${instanceId}] STEP 7.5 COMPLETE: Initial sync attempt finished.`);
    }

    // STEP 7.7: Download historical market data for the trading pairs
    console.log(`[${instanceId}] STEP 7.7: Downloading historical market data...`);
    try {
      // Wait a few seconds for container to fully start
      console.log(`[${instanceId}] Waiting 15 seconds for container to initialize...`);
      await new Promise(resolve => setTimeout(resolve, 15000));

      // Check if container is actually running before attempting data download
      const containerStatus = await runDockerCommand(['ps', '-f', `name=${containerName}`, '--format', '{{.Names}}']);
      if (!containerStatus.includes(containerName)) {
        console.warn(`[${instanceId}] Container not running, skipping data download`);
      } else {
        console.log(`[${instanceId}] Container confirmed running, downloading minimal data...`);

        // Download a minimal dataset (1 day) to get started quickly
        const downloadProcess = spawn('docker', [
          'exec', containerName,
          'freqtrade', 'download-data',
          '--exchange', 'kraken',
          '--pairs', 'BTC/USD', 'ETH/USD', // Just download main pairs initially
          '--timeframe', '15m',
          '--days', '1'
        ], { timeout: 60000 }); // 60 second timeout

        let downloadOutput = '';
        downloadProcess.stdout.on('data', (data) => {
          downloadOutput += data.toString();
        });

        downloadProcess.stderr.on('data', (data) => {
          downloadOutput += data.toString();
        });

        await Promise.race([
          new Promise((resolve) => {
            downloadProcess.on('close', (code) => {
              console.log(`[${instanceId}] Data download completed with code ${code}`);
              if (downloadOutput) {
                console.log(`[${instanceId}] Download output: ${downloadOutput.slice(-500)}`); // Last 500 chars
              }
              resolve();
            });
            downloadProcess.on('error', (error) => {
              console.log(`[${instanceId}] Data download error (non-critical): ${error.message}`);
              resolve();
            });
          }),
          new Promise(resolve => setTimeout(() => {
            console.log(`[${instanceId}] Data download timeout, proceeding anyway`);
            downloadProcess.kill();
            resolve();
          }, 60000))
        ]);
      }

    } catch (dataError) {
      console.log(`[${instanceId}] Data download failed (non-critical, bot will work without historical data): ${dataError.message}`);
    }
    console.log(`[${instanceId}] STEP 7.7 COMPLETE: Historical data download attempt finished.`);

    // Verify container is running
    console.log(`[${instanceId}] STEP 8: Verifying container status...`);
    try {
      const containerStatus = await runDockerCommand(['ps', '-f', `name=${containerName}`, '--format', 'table {{.Names}}\\t{{.Status}}']);
      console.log(`[${instanceId}] Container status: ${containerStatus}`);
      if (containerStatus.includes(containerName)) {
        console.log(`[${instanceId}] ✓ Container is running.`);
      } else {
        console.warn(`[${instanceId}] Container may not be running properly.`);
      }
    } catch (statusErr) {
      console.warn(`[${instanceId}] Could not verify container status:`, statusErr.message);
    }

    console.log(`[${instanceId}] STEP 8 COMPLETE: Container verification finished.`);

    // STEP 8.5: Wait for bot to fully stabilize and become responsive
    console.log(`[${instanceId}] STEP 8.5: Waiting for bot API to become responsive...`);
    let apiResponsive = false;
    const maxWaitTime = 60000; // 1 minute max wait
    const startWait = Date.now();

    while (!apiResponsive && (Date.now() - startWait) < maxWaitTime) {
      try {
        const response = await fetch(`http://localhost:${port}/api/v1/ping`, {
          method: 'GET',
          timeout: 3000
        });
        if (response.ok) {
          apiResponsive = true;
          console.log(`[${instanceId}] ✅ Bot API is responsive`);
        }
      } catch (error) {
        // Bot not ready yet, continue waiting
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds before retry
      }
    }

    if (!apiResponsive) {
      console.warn(`[${instanceId}] ⚠️  Bot API not responsive after ${maxWaitTime / 1000}s, but continuing...`);
    }

    console.log(`[${instanceId}] STEP 8.5 COMPLETE: Bot stability check finished.`);

    // Track bot creation time for portfolio monitoring grace period
    if (globalPortfolioMonitor) {
      globalPortfolioMonitor.trackBotCreation(instanceId);
    }

    // --- Provisioning Complete ---
    console.log(`[${instanceId}] ==========================================`);
    console.log(`[${instanceId}] PROVISIONING COMPLETE - SUCCESS!`);
    console.log(`[${instanceId}] Summary:`);
    console.log(`[${instanceId}]   User ID: ${userId}`);
    console.log(`[${instanceId}]   Instance ID: ${instanceId}`);
    console.log(`[${instanceId}]   Port: ${port}`);
    console.log(`[${instanceId}]   Container: ${containerName}`);
    console.log(`[${instanceId}]   Strategy: ${configJson.strategy}`);
    console.log(`[${instanceId}]   Database URL: ${configJson.db_url}`);
    console.log(`[${instanceId}]   Instance Directory: ${instanceDir}`);
    console.log(`[${instanceId}] ==========================================`);

    if (task.res && !task.res.headersSent) {
      const response = {
        success: true,
        message: 'Bot provisioned successfully',
        instanceId: instanceId,
        port: port,
        containerName: containerName,
        strategy: configJson.strategy
      };
      task.res.json(response);
      console.log(`[${instanceId}] ✓ Sent final success response:`, JSON.stringify(response, null, 2));
    } else {
      console.warn(`[${instanceId}] Headers already sent, cannot send response.`);
    }

  } catch (error) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] [${instanceId}] ==========================================`);
    console.error(`[${timestamp}] [${instanceId}] PROVISIONING FAILED - CRITICAL ERROR`);
    console.error(`[${timestamp}] [${instanceId}] ==========================================`);
    console.error(`[${timestamp}] [${instanceId}] Error Message:`, error.message);
    console.error(`[${timestamp}] [${instanceId}] Error Stack:`, error.stack);
    console.error(`[${timestamp}] [${instanceId}] Full Error Object:`, JSON.stringify(error, Object.getOwnPropertyNames(error), 2));

    // Try to gather additional debugging info
    try {
      console.error(`[${timestamp}] [${instanceId}] Additional Debug Info:`);
      console.error(`[${timestamp}] [${instanceId}]   TURSO_API_KEY available: ${!!TURSO_API_KEY}`);
      console.error(`[${timestamp}] [${instanceId}]   TURSO_CMD: ${TURSO_CMD}`);
      console.error(`[${timestamp}] [${instanceId}]   TURSO_REGION: ${TURSO_REGION}`);
      console.error(`[${timestamp}] [${instanceId}]   FREQTRADE_IMAGE: ${FREQTRADE_IMAGE}`);
      console.error(`[${timestamp}] [${instanceId}]   BOT_BASE_DIR: ${BOT_BASE_DIR}`);
      console.error(`[${timestamp}] [${instanceId}]   Instance dir exists: ${await fs.pathExists(instanceDir)}`);
      if (await fs.pathExists(instanceDir)) {
        const files = await fs.readdir(instanceDir);
        console.error(`[${timestamp}] [${instanceId}]   Instance dir contents: ${files.join(', ')}`);
      }
    } catch (debugErr) {
      console.error(`[${timestamp}] [${instanceId}] Could not gather debug info:`, debugErr.message);
    }

    console.error(`[${timestamp}] [${instanceId}] ==========================================`);

    if (task.res && !task.res.headersSent) {
      const errorResponse = {
        success: false,
        message: `Server error for ${instanceId}`,
        error: error.message || 'Unknown error',
        instanceId: instanceId,
        timestamp: timestamp
      };
      task.res.status(500).json(errorResponse);
      console.log(`[${timestamp}] [${instanceId}] Sent error response:`, JSON.stringify(errorResponse, null, 2));
    } else {
      console.error(`[${timestamp}] [${instanceId}] Cannot send error response (headers already sent).`);
    }
  } finally {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${instanceId}] Entering FINALLY block - cleaning up...`);
    isProvisioning = false;
    console.log(`[${timestamp}] [${instanceId}] Set isProvisioning = false`);

    setTimeout(() => {
      console.log(`[${timestamp}] [${instanceId}] Scheduling next queue processing in 500ms...`);
      processProvisioningQueue();
    }, 500);

    console.log(`[${timestamp}] [${instanceId}] PROVISIONING PROCESS FINISHED.`);
    console.log(`[${timestamp}] [${instanceId}] Queue length now: ${provisioningQueue.length}`);
  }
}


// --- Start processing the queue periodically (Fallback) ---
setInterval(() => {
  if (!isProvisioning && provisioningQueue.length > 0) {
    console.log("[Queue Interval] Interval check found tasks. Starting processing.");
    processProvisioningQueue();
  }
}, 5000);

// Global flag to prevent repeated Turso calls
let tursoGloballyDisabled = false; // Reset this flag 
let lastTursoValidationAttempt = 0;
const TURSO_VALIDATION_COOLDOWN = 5 * 60 * 1000; // 5 minutes

// Helper function to check if Turso operations should be skipped
function shouldSkipTurso() {
  if (!TURSO_API_KEY || !TURSO_ORG) {
    return true;
  }
  return tursoGloballyDisabled;
}

// Helper function to reset Turso global disable flag (for admin use)
function resetTursoGlobalDisable() {
  tursoGloballyDisabled = false;
  lastTursoValidationAttempt = 0;
  console.log('✓ Turso global disable flag reset');
}

// --- Helper Function to Validate and Refresh Turso Token ---
async function validateAndRefreshTursoToken() {
  if (!TURSO_API_KEY || !TURSO_ORG) {
    tursoGloballyDisabled = true;
    return false;
  }

  // Check if globally disabled
  if (tursoGloballyDisabled) {
    console.log('⚠️  Turso operations are globally disabled due to previous failures');
    return false;
  }

  // Rate limiting: prevent too frequent validation attempts
  const now = Date.now();
  if (now - lastTursoValidationAttempt < TURSO_VALIDATION_COOLDOWN) {
    console.log('⚠️  Turso validation skipped due to rate limiting');
    return false;
  }
  lastTursoValidationAttempt = now;

  try {
    // Use the refresh-turso-token.js utility to validate and renew if needed
    console.log('Using refresh-turso-token.js for validation and renewal...');

    const { spawn } = require('child_process');
    const validationProcess = spawn('node', ['refresh-turso-token.js', '--check'], {
      cwd: __dirname,
      stdio: 'pipe'
    });

    return new Promise((resolve) => {
      let output = '';
      let errorOutput = '';

      validationProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      validationProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      validationProcess.on('error', (err) => {
        console.log('⚠️  Turso validation process failed:', err.message);
        tursoGloballyDisabled = true;
        resolve(false);
      });

      validationProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✓ Turso token validation successful');
          resolve(true);
        } else {
          console.log('⚠️  Turso token validation failed, disabling Turso operations globally');
          console.log('   To resolve: node refresh-turso-token.js --renew');
          console.log('   Output:', output);
          console.log('   Error:', errorOutput);
          tursoGloballyDisabled = true;
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.log('⚠️  Turso validation failed:', error.message);
    tursoGloballyDisabled = true;
    return false;
  }
}

// --- Helper Function to Run Docker Commands ---
async function runDockerCommand(args, cwd = null, cmdName = 'docker') {
  return new Promise((resolve, reject) => {
    const cmd = cmdName;
    const cmdArgs = args;
    const options = cwd ? { cwd } : {};
    options.encoding = 'utf8';

    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [Docker Helper] Starting command: ${cmd} ${cmdArgs.join(' ')} ${cwd ? `(in ${cwd})` : ''}`);

    const command = spawn(cmd, cmdArgs, options);
    let stdout = '';
    let stderr = '';

    command.stdout.on('data', (data) => {
      stdout += data;
      console.log(`[${timestamp}] [Docker Helper] stdout: ${data.toString().trim()}`);
    });

    command.stderr.on('data', (data) => {
      stderr += data;
      console.error(`[${timestamp}] [Docker Helper] stderr: ${data.toString().trim()}`);
    });

    command.on('error', (err) => {
      console.error(`[${timestamp}] [Docker Helper] Spawn Error: ${cmd} ${cmdArgs.join(' ')}`, err);
      reject(err);
    });

    command.on('close', (code) => {
      console.log(`[${timestamp}] [Docker Helper] Command finished: ${cmd} ${cmdArgs.join(' ')} (Exit Code: ${code})`);

      if (stderr && code !== 0) {
        console.error(`[${timestamp}] [Docker Helper] Final stderr: ${stderr.trim()}`);
      }

      if (stdout.trim()) {
        console.log(`[${timestamp}] [Docker Helper] Final stdout: ${stdout.trim()}`);
      }

      if (code !== 0) {
        const errorMsg = `Docker command failed with exit code ${code}: ${stderr.trim() || stdout.trim() || 'No output'}`;
        console.error(`[${timestamp}] [Docker Helper] ${errorMsg}`);
        reject(new Error(errorMsg));
      } else {
        console.log(`[${timestamp}] [Docker Helper] Command succeeded.`);
        resolve(stdout.trim());
      }
    });
  });
}

// --- Simple helpers for API Gateway aggregation (new) ---
async function listUserBotInstances(userId) {
  const bots = [];
  const userDir = path.join(BOT_BASE_DIR, userId);
  if (!(await fs.pathExists(userDir))) return bots;

  try {
    const instanceIds = await fs.readdir(userDir);
    for (const instanceId of instanceIds) {
      try {
        // Skip temp files and non-bot files
        if (instanceId.includes('.tmp') || instanceId.includes('.json') || instanceId.includes('.backup') || instanceId === 'historical_backups') {
          continue;
        }

        const instanceDir = path.join(userDir, instanceId);
        const stats = await fs.stat(instanceDir);
        if (!stats.isDirectory()) continue;

        const configPath = path.join(instanceDir, 'config.json');
        if (!(await fs.pathExists(configPath))) continue;

        const config = JSON.parse(await fs.readFile(configPath, 'utf8'));
        const port = config.api_server?.listen_port;
        if (!port) continue;

        const containerName = `freqtrade-${instanceId}`;

        // Check container status
        let containerStatus = 'unknown';
        try {
          const statusOutput = await runDockerCommand(['ps', '-f', `name=${containerName}`, '--format', '{{.Names}}']);
          containerStatus = statusOutput.includes(containerName) ? 'running' : 'stopped';
        } catch (statusErr) {
          containerStatus = 'error';
        }

        bots.push({
          instanceId,
          userId,
          strategy: config.strategy,
          port,
          containerName,
          containerStatus,
          exchange: config.exchange?.name,
          dry_run: config.dry_run,
          stake_currency: config.stake_currency,
          stake_amount: config.stake_amount,
          max_open_trades: config.max_open_trades,
          username: config.api_server?.username,
          password: config.api_server?.password
        });
      } catch (instanceErr) {
        console.warn(`[listUserBotInstances] Error processing instance ${instanceId}:`, instanceErr.message);
      }
    }
  } catch (readDirErr) {
    console.warn(`[listUserBotInstances] Error reading user directory ${userDir}:`, readDirErr.message);
  }

  return bots;
}

// Resolve absolute instance directory by scanning user folders
async function resolveInstanceDir(instanceId) {
  const users = await fs.readdir(BOT_BASE_DIR);
  for (const uid of users) {
    const instanceDir = path.join(BOT_BASE_DIR, uid, instanceId);
    try {
      const stats = await fs.stat(instanceDir);
      if (stats.isDirectory()) return instanceDir;
    } catch { /* continue */ }
  }
  throw new Error(`Instance directory not found for ${instanceId}`);
}

// Build bot URL and credentials by reading its config.json
async function getBotUrlByInstanceId(instanceId) {
  const instanceDir = await resolveInstanceDir(instanceId);
  const cfgPath = path.join(instanceDir, 'config.json');
  if (!await fs.pathExists(cfgPath)) throw new Error('config.json not found');
  const config = JSON.parse(await fs.readFile(cfgPath, 'utf8'));
  const port = config.api_server?.listen_port;
  if (!port) throw new Error('listen_port missing in config');
  return {
    url: `http://localhost:${port}`,
    username: config.api_server?.username,
    password: config.api_server?.password
  };
}

// Generic proxy to FreqTrade bot API
async function proxyFreqtradeApiRequest(instanceId, endpoint, method = 'GET', body = null) {
  const botConfig = await getBotUrlByInstanceId(instanceId);
  let token = await getBotAuthToken(botConfig, instanceId);
  let headers = { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' };
  const url = `${botConfig.url}${endpoint}`;

  let resp = await fetch(url, { method, headers, body: body ? JSON.stringify(body) : undefined });

  // If token expired/invalid, refresh once and retry
  if (resp.status === 401) {
    botTokenCache.delete(instanceId);
    token = await getBotAuthToken(botConfig, instanceId);
    headers = { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' };
    resp = await fetch(url, { method, headers, body: body ? JSON.stringify(body) : undefined });
  }

  if (!resp.ok) {
    const text = await resp.text().catch(() => '');
    throw new Error(`Proxy error ${resp.status} ${resp.statusText}: ${text?.slice(0, 200)}`);
  }
  const contentType = resp.headers.get('content-type') || '';
  if (contentType.includes('application/json')) return resp.json();
  return resp.text();
}

// In-memory caches for bot auth
const botTokenCache = new Map(); // instanceId -> { token, expMs }
const botAuthBackoff = new Map(); // instanceId -> nextAllowedAt (ms)

// Obtain JWT from FreqTrade bot API with caching and backoff
async function getBotAuthToken(botConfig, instanceId) {
  const now = Date.now();
  // Backoff check
  const nextAllowed = botAuthBackoff.get(instanceId) || 0;
  if (now < nextAllowed) {
    throw new Error(`auth backoff until ${new Date(nextAllowed).toISOString()}`);
  }

  // Cache check
  const cached = botTokenCache.get(instanceId);
  if (cached && cached.expMs && cached.expMs - now > 30 * 1000) {
    return cached.token;
  }

  const username = botConfig?.username || process.env.DEFAULT_BOT_API_USERNAME;
  const password = botConfig?.password || process.env.DEFAULT_BOT_API_PASSWORD;
  if (!username || !password) {
    // Set 60s backoff to avoid spamming login
    botAuthBackoff.set(instanceId, now + 60 * 1000);
    throw new Error('Missing bot API credentials (set api_server.username/password in config or DEFAULT_BOT_API_USERNAME/PASSWORD env)');
  }

  // HTTP Basic auth as required by FreqTrade
  const basic = Buffer.from(`${username}:${password}`).toString('base64');
  const resp = await fetch(`${botConfig.url}/api/v1/token/login`, {
    method: 'POST',
    headers: { 'Authorization': `Basic ${basic}` }
  });

  if (resp.status === 401) {
    botAuthBackoff.set(instanceId, now + 60 * 1000); // 1 minute cooldown
    throw new Error('401 Unauthorized from bot API');
  }

  if (!resp.ok) {
    const text = await resp.text().catch(() => '');
    // Short backoff for other errors
    botAuthBackoff.set(instanceId, now + 30 * 1000);
    throw new Error(`Login failed (${resp.status}): ${text?.slice(0, 200)}`);
  }

  const data = await resp.json();
  const token = data?.access_token || data?.token;
  if (!token) {
    botAuthBackoff.set(instanceId, now + 30 * 1000);
    throw new Error('No access_token in login response');
  }

  // Compute expiry: decode JWT if possible, else 9 minutes
  let expMs = now + 9 * 60 * 1000;
  try {
    const dec = jwt.decode(token);
    if (dec?.exp) expMs = dec.exp * 1000;
  } catch { /* ignore */ }

  botTokenCache.set(instanceId, { token, expMs });
  return token;
}

async function getBotAggregates(instanceId) {
  try {
    const botConfig = await getBotUrlByInstanceId(instanceId);
    const token = await getBotAuthToken(botConfig, instanceId);
    const headers = { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' };

    const [statusRes, balanceRes, profitRes] = await Promise.all([
      fetch(`${botConfig.url}/api/v1/status`, { headers }),
      fetch(`${botConfig.url}/api/v1/balance`, { headers }),
      fetch(`${botConfig.url}/api/v1/profit`, { headers })
    ]);

    const ok = statusRes.ok && balanceRes.ok && profitRes.ok;
    if (!ok) throw new Error('Bot API error');

    const [status, balance, profit] = await Promise.all([
      statusRes.json(), balanceRes.json(), profitRes.json()
    ]);

    // Bot-managed balance and starting capital
    const totalBalance = Number(balance?.total_bot ?? balance?.total ?? 0);
    const startingCapital = Number(balance?.starting_capital ?? 10000);

    // Calculate P&L as current balance - starting capital
    const totalPnL = totalBalance - startingCapital;

    const openTrades = Array.isArray(status) ? status.filter(t => t?.is_open) : [];

    return {
      instanceId,
      status,
      metrics: {
        totalBalance,
        totalPnL,
        startingCapital,
        openTrades: openTrades.length
      }
    };
  } catch (e) {
    return { instanceId, error: e.message, metrics: { totalBalance: 0, totalPnL: 0, startingCapital: 0, openTrades: 0 } };
  }
}

async function aggregateUserPortfolio(userId) {
  const bots = await listUserBotInstances(userId);
  const aggregates = await Promise.all(bots.map(b => getBotAggregates(b.instanceId)));
  const running = aggregates.filter(a => !a.error);
  const totalBalance = running.reduce((s, b) => s + (Number(b.metrics.totalBalance) || 0), 0);
  const totalPnL = running.reduce((s, b) => s + (Number(b.metrics.totalPnL) || 0), 0);
  const activeBots = running.length;
  const botCount = aggregates.length;
  return {
    timestamp: Date.now(),
    portfolioValue: totalBalance, // show bot-managed balance only
    totalBalance,
    totalPnL,
    activeBots,
    botCount,
    bots: aggregates
  };
}

// --- Historical Portfolio Data Functions ---
async function loadUserPortfolioSnapshots(userId) {
  try {
    const userDir = path.join(BOT_BASE_DIR, userId);
    const snapshotPath = path.join(userDir, 'portfolio_snapshots.json');

    if (!(await fs.pathExists(snapshotPath))) {
      return { snapshots: [], metadata: {} };
    }

    const data = await fs.readJson(snapshotPath);
    return {
      snapshots: data.snapshots || [],
      metadata: data.metadata || {}
    };
  } catch (error) {
    console.error(`[HistoricalData] Error loading snapshots for user ${userId}:`, error);
    return { snapshots: [], metadata: {} };
  }
}

function aggregateSnapshotsForInterval(snapshots, intervalMs) {
  if (!snapshots || snapshots.length === 0) return [];

  const now = Date.now();
  const startTime = now - intervalMs;

  // Filter snapshots within the time range
  const relevantSnapshots = snapshots.filter(s => s.timestamp >= startTime);

  if (relevantSnapshots.length === 0) return [];

  // Determine the appropriate aggregation window
  let windowMs;
  if (intervalMs <= 60 * 60 * 1000) { // 1 hour
    windowMs = 5 * 60 * 1000; // 5-minute windows
  } else if (intervalMs <= 24 * 60 * 60 * 1000) { // 24 hours
    windowMs = 30 * 60 * 1000; // 30-minute windows
  } else if (intervalMs <= 7 * 24 * 60 * 60 * 1000) { // 7 days
    windowMs = 4 * 60 * 60 * 1000; // 4-hour windows
  } else { // 30 days
    windowMs = 24 * 60 * 60 * 1000; // 24-hour windows
  }

  // Group snapshots into time windows
  const windowedData = {};

  relevantSnapshots.forEach(snapshot => {
    const windowStart = Math.floor(snapshot.timestamp / windowMs) * windowMs;
    if (!windowedData[windowStart]) {
      windowedData[windowStart] = [];
    }
    windowedData[windowStart].push(snapshot);
  });

  // Create aggregated data points
  const result = Object.keys(windowedData)
    .sort((a, b) => Number(a) - Number(b))
    .map(windowStart => {
      const windowSnapshots = windowedData[windowStart];
      const latestSnapshot = windowSnapshots[windowSnapshots.length - 1];

      // Calculate averages for the window
      const avgPortfolioValue = windowSnapshots.reduce((sum, s) => sum + (s.portfolioValue || 0), 0) / windowSnapshots.length;
      const avgTotalPnL = windowSnapshots.reduce((sum, s) => sum + (s.totalPnL || 0), 0) / windowSnapshots.length;

      return {
        timestamp: Number(windowStart),
        portfolioValue: Number(avgPortfolioValue.toFixed(2)),
        totalPnL: Number(avgTotalPnL.toFixed(2)),
        activeBots: latestSnapshot.activeBots || 0,
        botCount: latestSnapshot.botCount || 0,
        snapshotCount: windowSnapshots.length
      };
    });

  return result;
}

function getChartDataForInterval(snapshots, interval) {
  const intervalMap = {
    '1h': 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000
  };

  const intervalMs = intervalMap[interval];
  if (!intervalMs) {
    throw new Error(`Invalid interval: ${interval}. Supported: 1h, 24h, 7d, 30d`);
  }

  return aggregateSnapshotsForInterval(snapshots, intervalMs);
}

async function savePortfolioSnapshot(userId, portfolioData) {
  try {
    console.log(`[HistoricalData] DEBUG: Starting savePortfolioSnapshot for user ${userId}`);

    // RACE CONDITION PROTECTION: Prevent concurrent saves for the same user
    if (savingInProgress.get(userId)) {
      console.log(`[HistoricalData] DEBUG: Save already in progress for user ${userId}, skipping`);
      return false;
    }

    savingInProgress.set(userId, true);
    console.log(`[HistoricalData] DEBUG: Acquired save lock for user ${userId}`);

    const userDir = path.join(BOT_BASE_DIR, userId);
    const snapshotPath = path.join(userDir, 'portfolio_snapshots.json');
    const tempPath = path.join(userDir, `.portfolio_snapshots_${Date.now()}_${process.pid}.tmp`);
    const backupPath = path.join(userDir, 'portfolio_snapshots.json.backup');

    console.log(`[HistoricalData] DEBUG: Paths defined - userDir: ${userDir}, snapshotPath: ${snapshotPath}`);

    // Ensure user directory exists
    await fs.ensureDir(userDir);
    console.log(`[HistoricalData] DEBUG: User directory ensured`);

    // Check if files exist before proceeding
    console.log(`[HistoricalData] DEBUG: Snapshot file exists: ${await fs.pathExists(snapshotPath)}`);
    console.log(`[HistoricalData] DEBUG: Temp file exists: ${await fs.pathExists(tempPath)}`);
    console.log(`[HistoricalData] DEBUG: Backup file exists: ${await fs.pathExists(backupPath)}`);

    // Load existing data with bulletproof preservation
    const currentTime = Date.now();
    let data = {
      metadata: {
        firstSnapshot: currentTime, // Default for new files only
        lastSnapshot: currentTime,
        totalSnapshots: 0,
        accountCreated: currentTime,
        compressionHistory: []
      },
      snapshots: [],
      lastUpdated: currentTime,
      version: '2.0'
    };

    // Read existing data if file exists
    if (await fs.pathExists(snapshotPath)) {
      try {
        console.log(`[HistoricalData] DEBUG: About to read existing data from ${snapshotPath}`);
        const existingData = await fs.readJson(snapshotPath);
        console.log(`[HistoricalData] DEBUG: Successfully read existing data`);

        // Create backup before any modifications
        console.log(`[HistoricalData] DEBUG: About to create backup from ${snapshotPath} to ${backupPath}`);
        await fs.copy(snapshotPath, backupPath, { overwrite: true });
        console.log(`[HistoricalData] DEBUG: Main backup created successfully`);

        // Additional timestamped backup for extra protection (with unique filename)
        const uniqueTimestamp = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const timestampedBackupPath = path.join(userDir, 'historical_backups', `portfolio_snapshots_${uniqueTimestamp}.json`);
        console.log(`[HistoricalData] DEBUG: Creating timestamped backup at ${timestampedBackupPath}`);
        await fs.ensureDir(path.join(userDir, 'historical_backups'));
        await fs.copy(snapshotPath, timestampedBackupPath, { overwrite: true });
        console.log(`[HistoricalData] DEBUG: Timestamped backup created successfully`);

        // Keep only last 10 timestamped backups to save space  
        const backupDir = path.join(userDir, 'historical_backups');
        const backupFiles = (await fs.readdir(backupDir)).filter(f => f.startsWith('portfolio_snapshots_')).sort();
        if (backupFiles.length > 10) {
          const filesToRemove = backupFiles.slice(0, -10);
          for (const file of filesToRemove) {
            await fs.remove(path.join(backupDir, file));
          }
        }

        // BULLETPROOF: Never overwrite existing historical data
        if (existingData && existingData.snapshots && Array.isArray(existingData.snapshots)) {
          data.snapshots = [...existingData.snapshots]; // Preserve ALL existing snapshots
        }

        // BULLETPROOF: Preserve all existing metadata with careful merging
        if (existingData && existingData.metadata) {
          data.metadata = {
            firstSnapshot: existingData.metadata.firstSnapshot || (existingData.snapshots && existingData.snapshots.length > 0 ? existingData.snapshots[0].timestamp : currentTime),
            lastSnapshot: existingData.metadata.lastSnapshot || currentTime,
            totalSnapshots: existingData.metadata.totalSnapshots || (existingData.snapshots ? existingData.snapshots.length : 0),
            accountCreated: existingData.metadata.accountCreated || currentTime,
            compressionHistory: existingData.metadata.compressionHistory || []
          };
        }

        console.log(`[HistoricalData] Loaded existing data: ${data.snapshots.length} snapshots, account created: ${new Date(data.metadata.accountCreated).toISOString()}`);

      } catch (e) {
        console.error(`[HistoricalData] CRITICAL: Error reading existing snapshots for ${userId}:`, e.message);
        // If backup exists, try to restore from it
        if (await fs.pathExists(backupPath)) {
          try {
            console.log(`[HistoricalData] Attempting to restore from backup...`);
            const backupData = await fs.readJson(backupPath);
            if (backupData && backupData.snapshots) {
              data = backupData;
              console.log(`[HistoricalData] Successfully restored ${data.snapshots.length} snapshots from backup`);
            }
          } catch (backupError) {
            console.error(`[HistoricalData] Backup restore failed:`, backupError.message);
          }
        }
      }
    }

    // Add the new snapshot
    const snapshot = {
      timestamp: Date.now(),
      ...portfolioData
    };

    data.snapshots.push(snapshot);

    // Update metadata (BULLETPROOF: Only update what needs updating)
    data.metadata.lastSnapshot = snapshot.timestamp;
    data.metadata.totalSnapshots = data.snapshots.length;
    data.lastUpdated = Date.now();

    // Only set firstSnapshot if we don't have one or if this is the first snapshot
    if (!data.metadata.firstSnapshot || data.snapshots.length === 1) {
      data.metadata.firstSnapshot = data.snapshots[0].timestamp;
    }

    // Compress old data if needed (keep last 10,000 snapshots)
    if (data.snapshots.length > 10000) {
      const keepCount = 8000;
      const removed = data.snapshots.length - keepCount;
      const oldFirstSnapshot = data.snapshots[0].timestamp;
      data.snapshots = data.snapshots.slice(-keepCount);

      // Update firstSnapshot after compression
      data.metadata.firstSnapshot = data.snapshots[0].timestamp;
      data.metadata.totalSnapshots = data.snapshots.length;

      data.metadata.compressionHistory.push({
        timestamp: Date.now(),
        removedSnapshots: removed,
        oldFirstSnapshot: oldFirstSnapshot,
        newFirstSnapshot: data.metadata.firstSnapshot,
        reason: 'automatic_cleanup'
      });

      console.log(`[HistoricalData] Compressed data: removed ${removed} old snapshots, kept ${keepCount}`);
    }

    // ATOMIC WRITE: Write to unique temp file first, then copy to final location
    console.log(`[HistoricalData] DEBUG: About to write to temp file ${tempPath}`);
    await fs.writeJson(tempPath, data, { spaces: 2 });
    console.log(`[HistoricalData] DEBUG: Temp file written, now copying to ${snapshotPath}`);
    await fs.copy(tempPath, snapshotPath, { overwrite: true });
    console.log(`[HistoricalData] DEBUG: Atomic write completed, cleaning up temp file`);
    await fs.remove(tempPath);

    console.log(`[HistoricalData] SAVED snapshot for user ${userId}: Value=${snapshot.portfolioValue.toFixed(2)}, Bots=${snapshot.activeBots}/${snapshot.botCount}, Total: ${data.snapshots.length} snapshots`);

    // Release the save lock
    savingInProgress.delete(userId);
    return true;
  } catch (error) {
    console.error(`[HistoricalData] CRITICAL ERROR saving snapshot for user ${userId}:`, error);

    // Try to clean up temp file if it exists
    try {
      const tempPath = path.join(BOT_BASE_DIR, userId, 'portfolio_snapshots.json.tmp');
      if (await fs.pathExists(tempPath)) {
        await fs.remove(tempPath);
      }
    } catch (cleanupError) {
      console.error(`[HistoricalData] Temp file cleanup failed:`, cleanupError.message);
    }

    // Release the save lock
    savingInProgress.delete(userId);
    return false;
  }
}

// --- Chart Data API Endpoints ---
app.get('/api/charts/portfolio/:interval', authenticateToken, async (req, res) => {
  try {
    const { interval } = req.params;
    const userId = req.user.id || req.user.uid;

    // Validate interval
    const validIntervals = ['1h', '24h', '7d', '30d'];
    if (!validIntervals.includes(interval)) {
      return res.status(400).json({
        success: false,
        message: `Invalid interval. Supported: ${validIntervals.join(', ')}`
      });
    }

    // Load user's portfolio snapshots
    const { snapshots, metadata } = await loadUserPortfolioSnapshots(userId);

    if (snapshots.length === 0) {
      return res.json({
        success: true,
        interval,
        data: [],
        metadata: {
          totalSnapshots: 0,
          dataPoints: 0,
          timeRange: null
        }
      });
    }

    // Generate chart data for the requested interval
    const chartData = getChartDataForInterval(snapshots, interval);

    res.json({
      success: true,
      interval,
      data: chartData,
      metadata: {
        totalSnapshots: snapshots.length,
        dataPoints: chartData.length,
        firstSnapshot: metadata.firstSnapshot,
        lastSnapshot: metadata.lastSnapshot,
        timeRange: {
          start: chartData.length > 0 ? chartData[0].timestamp : null,
          end: chartData.length > 0 ? chartData[chartData.length - 1].timestamp : null
        }
      }
    });

  } catch (error) {

    console.error('[ChartAPI] Error fetching chart data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch chart data',
      error: error.message
    });
  }
});

app.get('/api/charts/portfolio', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id || req.user.uid;
    console.log(`[ChartAPI] DEBUG: Portfolio request - User ID: ${userId}, User object:`, JSON.stringify(req.user, null, 2));

    // File-based debugging to capture frontend requests
    const fs = require('fs');
    const debugInfo = {
      timestamp: new Date().toISOString(),
      userId: userId,
      userObject: req.user,
      query: req.query,
      userAgent: req.get('User-Agent'),
      authHeader: req.get('Authorization') ? 'present' : 'missing'
    };

    try {
      fs.appendFileSync('/tmp/portfolio_frontend_debug.log',
        JSON.stringify(debugInfo, null, 2) + '\n---\n');
    } catch (e) {
      console.log('Debug file write error:', e.message);
    }

    const { snapshots, metadata } = await loadUserPortfolioSnapshots(userId);
    console.log(`[ChartAPI] DEBUG: Loaded ${snapshots.length} snapshots for user ${userId}`);

    if (snapshots.length === 0) {
      return res.json({
        success: true,
        intervals: {
          '1h': { data: [], dataPoints: 0 },
          '24h': { data: [], dataPoints: 0 },
          '7d': { data: [], dataPoints: 0 },
          '30d': { data: [], dataPoints: 0 }
        },
        metadata: {
          totalSnapshots: 0,
          firstSnapshot: null,
          lastSnapshot: null
        }
      });
    }

    // Generate chart data for all intervals
    const intervals = {
      '1h': getChartDataForInterval(snapshots, '1h'),
      '24h': getChartDataForInterval(snapshots, '24h'),
      '7d': getChartDataForInterval(snapshots, '7d'),
      '30d': getChartDataForInterval(snapshots, '30d')
    };

    // Debug logging for interval data
    Object.entries(intervals).forEach(([interval, data]) => {
      console.log(`[ChartAPI] DEBUG: ${interval} interval generated ${data.length} data points`);
    });

    res.json({
      success: true,
      intervals: Object.fromEntries(
        Object.entries(intervals).map(([key, data]) => [
          key,
          {
            data,
            dataPoints: data.length,
            timeRange: data.length > 0 ? {
              start: data[0].timestamp,
              end: data[data.length - 1].timestamp
            } : null
          }
        ])
      ),
      metadata: {
        totalSnapshots: snapshots.length,
        firstSnapshot: metadata.firstSnapshot,
        lastSnapshot: metadata.lastSnapshot
      }
    });

  } catch (error) {
    console.error('[ChartAPI] Error fetching all chart data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch chart data',
      error: error.message
    });
  }
});

app.get('/api/portfolio/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id || req.user.uid;
    const { limit = 1000, offset = 0 } = req.query;

    const { snapshots, metadata } = await loadUserPortfolioSnapshots(userId);

    // Apply pagination
    const startIndex = Math.max(0, snapshots.length - Number(limit) - Number(offset));
    const endIndex = Math.max(0, snapshots.length - Number(offset));
    const paginatedSnapshots = snapshots.slice(startIndex, endIndex);

    res.json({
      success: true,
      snapshots: paginatedSnapshots,
      pagination: {
        total: snapshots.length,
        limit: Number(limit),
        offset: Number(offset),
        returned: paginatedSnapshots.length
      },
      metadata
    });

  } catch (error) {
    console.error('[HistoryAPI] Error fetching portfolio history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch portfolio history',
      error: error.message
    });
  }
});

// --- Helper functions for streaming data ---
async function getChartDataForUser(userId) {
  try {
    // Get recent portfolio snapshots for chart data
    const userDir = path.join(BOT_BASE_DIR, userId);
    const snapshotsFile = path.join(userDir, 'portfolio_snapshots.json');

    if (!await fs.pathExists(snapshotsFile)) {
      return { points: [], latestValue: 0 };
    }

    const snapshotsData = JSON.parse(await fs.readFile(snapshotsFile, 'utf8'));
    const snapshots = snapshotsData.snapshots || [];

    // Convert snapshots to chart points (last 50 points)
    const points = snapshots.slice(-50).map(snapshot => ({
      timestamp: snapshot.timestamp,
      value: snapshot.portfolioValue || 0,
      pnl: snapshot.totalPnL || 0
    }));

    const latestValue = points.length > 0 ? points[points.length - 1].value : 0;

    return { points, latestValue, count: points.length };
  } catch (error) {
    console.warn(`[Chart] Error getting chart data for ${userId}:`, error.message);
    return { points: [], latestValue: 0 };
  }
}

async function getPositionsForUser(userId) {
  try {
    const bots = await listUserBotInstances(userId);
    const allPositions = [];

    for (const bot of bots) {
      if (bot.containerStatus !== 'running') continue;

      try {
        // Get status which includes open trades data
        const statusData = await proxyFreqtradeApiRequest(bot.instanceId, '/api/v1/status');

        if (statusData && Array.isArray(statusData)) {
          const positions = statusData
            .filter(trade => trade.is_open) // Only open positions
            .map(trade => ({
              botId: bot.instanceId,
              pair: trade.pair,
              side: trade.is_short ? 'short' : 'long',
              amount: trade.amount || 0,
              entryPrice: trade.open_rate || 0,
              currentPrice: trade.current_rate || trade.open_rate || 0,
              pnl: trade.profit_abs || 0,
              pnlPercent: trade.profit_ratio ? (trade.profit_ratio * 100) : 0,
              status: trade.is_open ? 'open' : 'closed'
            }));

          allPositions.push(...positions);
        }
      } catch (botError) {
        console.warn(`[Positions] Error getting positions from ${bot.instanceId}:`, botError.message);
      }
    }

    return { positions: allPositions, count: allPositions.length };
  } catch (error) {
    console.warn(`[Positions] Error getting positions for ${userId}:`, error.message);
    return { positions: [], count: 0 };
  }
}

async function getSecurityData(userId) {
  try {
    // Get actual trading pairs from current positions
    const positionsData = await getPositionsForUser(userId);
    const activePairs = [...new Set(positionsData.positions.map(pos => pos.pair))];

    // If no active positions, don't generate any security data
    if (activePairs.length === 0) {
      console.log(`🔒 No active positions for ${userId} - no security data generated`);
      return null;
    }

    // Randomly select one of the active pairs for this update
    const randomPair = activePairs[Math.floor(Math.random() * activePairs.length)];

    // Generate realistic price based on the actual pair
    let basePrice = 50000; // Default
    if (randomPair.includes('BTC')) basePrice = 45000;
    else if (randomPair.includes('ETH')) basePrice = 3000;
    else if (randomPair.includes('ADA')) basePrice = 0.5;
    else if (randomPair.includes('SOL')) basePrice = 100;
    else if (randomPair.includes('DOT')) basePrice = 25;

    // Add some random variation (+/- 5%)
    const variation = (Math.random() - 0.5) * 0.1; // -5% to +5%
    const price = basePrice * (1 + variation);

    console.log(`🔒 Security data: ${randomPair} = $${price.toFixed(4)} (from active positions: ${activePairs.join(', ')})`);

    return {
      pair: randomPair,
      price: price,
      timestamp: Date.now(),
      exchange: 'kraken'
    };
  } catch (error) {
    console.warn(`[Security] Error generating security data:`, error.message);
    return null;
  }
}

// --- SSE Streaming with Historical Data Saving ---
app.get('/api/stream', async (req, res) => {
  try {
    const user = await authenticateSSE(req);
    if (!user || !(user.id || user.uid)) {
      return res.status(401).end();
    }

    // SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache, no-transform');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // Disable buffering on Nginx
    res.flushHeaders?.();

    const userId = user.id || user.uid;
    const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    console.log(`[SSE] Opened stream for ${userId} from ${ip}`);

    let closed = false;
    let streamInterval;

    // Send initial connection event
    res.write(`event: connected\ndata: ${JSON.stringify({ timestamp: Date.now(), status: 'connected' })}\n\n`);

    // Portfolio streaming function
    async function streamPortfolioData() {
      if (closed) return;

      try {
        const portfolioData = await aggregateUserPortfolio(userId);

        // Save snapshot automatically (with throttling - every 30 seconds)
        if (portfolioData && portfolioData.activeBots > 0) {
          const lastSnapshotTime = userLastSnapshotTime.get(userId) || 0;
          const currentTime = Date.now();
          const throttleInterval = 30000; // 30 seconds

          if (currentTime - lastSnapshotTime > throttleInterval) {
            console.log(`[HistoricalData] Saving throttled snapshot for user ${userId} (${Math.round((currentTime - lastSnapshotTime) / 1000)}s since last)`);

            // Check if already saving to prevent race conditions
            if (savingInProgress.get(userId)) {
              console.log(`[HistoricalData] DEBUG: Snapshot save already in progress for ${userId}, skipping duplicate call`);
            } else {
              console.log(`[HistoricalData] DEBUG: About to call savePortfolioSnapshot for ${userId}`);
              await savePortfolioSnapshot(userId, portfolioData);
            }
            userLastSnapshotTime.set(userId, currentTime);
          }
        }

        // Send portfolio update via SSE
        const eventData = JSON.stringify(portfolioData);
        res.write(`event: portfolio\ndata: ${eventData}\n\n`);

        // Send chart data (historical points for charting)
        const chartData = await getChartDataForUser(userId);
        if (chartData && chartData.points) {
          res.write(`event: chart\ndata: ${JSON.stringify(chartData)}\n\n`);
        }

        // Send positions data
        const positionsData = await getPositionsForUser(userId);
        if (positionsData) {
          res.write(`event: positions\ndata: ${JSON.stringify(positionsData)}\n\n`);
        }

        // Send security data for active positions
        const securityData = await getSecurityData(userId);
        if (securityData) {
          res.write(`event: security\ndata: ${JSON.stringify(securityData)}\n\n`);
        }

      } catch (error) {
        console.error(`[SSE] Error streaming for ${userId}:`, error);
        if (!closed) {
          res.write(`event: error\ndata: ${JSON.stringify({ error: 'Stream error', message: error.message })}\n\n`);
        }
      }
    }

    // Start streaming - send updates every 5 seconds
    streamPortfolioData(); // Send initial data immediately
    streamInterval = setInterval(streamPortfolioData, 5000);

    // Handle client disconnect
    req.on('close', () => {
      if (!closed) {
        closed = true;
        console.log(`[SSE] Closed stream for ${userId}`);
        if (streamInterval) {
          clearInterval(streamInterval);
        }
      }
    });

    req.on('error', (err) => {
      console.error(`[SSE] Error for ${userId}:`, err);
      closed = true;
      if (streamInterval) {
        clearInterval(streamInterval);
      }
    });

  } catch (error) {
    console.error('[SSE] Authentication or setup error:', error);
    res.status(500).end();
  }
});

// --- SSE Authentication helper (supports ?token= for EventSource) ---
async function authenticateSSE(req) {
  try {
    const header = req.header('Authorization');
    let token = null;
    if (header && header.startsWith('Bearer ')) token = header.split(' ')[1];
    if (!token) token = req.query.token; // EventSource cannot set headers
    if (!token) {
      console.warn('[SSE] Missing token');
      return null;
    }

    // Try Firebase Admin first if available
    if (firebaseInitialized) {
      try {
        const decoded = await admin.auth().verifyIdToken(token);
        const user = { id: decoded.uid, uid: decoded.uid, email: decoded.email, role: decoded.admin ? 'admin' : 'user' };
        console.log(`[SSE] Auth via Firebase Admin: ${user.uid}`);
        return user;
      } catch (e) {
        console.warn(`[SSE] Firebase Admin verify failed: ${e.message}. Falling back to JWKS.`);
      }
    }

    // Try Firebase verification via JWKS (no service account required)
    const firebaseUser = await verifyFirebaseIdTokenWithoutAdmin(token);
    if (firebaseUser) {
      console.log(`[SSE] Auth via Firebase JWKS: ${firebaseUser.uid}`);
      return firebaseUser;
    }

    // Fallback: Local JWT (for non-Firebase tokens)
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const u = decoded.user || decoded;
      const user = { id: u.id || u.uid, uid: u.uid || u.id, email: u.email, role: u.role || 'user' };
      console.log(`[SSE] Auth via local JWT: ${user.id}`);
      return user;
    } catch (e) {
      console.warn(`[SSE] Local JWT verify failed: ${e.message}`);
    }

    return null;
  } catch (error) {
    console.error('[SSE] Authentication error:', error);
    return null;
  }
}

// --- Constants ---
const timestamp = new Date().toISOString();
console.log(`=================================================`);
console.log(` Freqtrade Bot Manager (API Gateway + SSE)`);
console.log(`-------------------------------------------------`);
console.log(` HTTP Server: http://0.0.0.0:${PORT}`);
console.log(` SSE Stream: GET /api/stream?token=...`);
console.log(` Bot Instance Base Dir: ${BOT_BASE_DIR}`);
console.log(` Main Strategies Source: ${MAIN_STRATEGIES_SOURCE_DIR}`);
console.log(` SHARED Data Directory: ${SHARED_DATA_DIR}`);
console.log(` Firebase Admin: ${firebaseInitialized ? 'enabled' : 'not initialized'}; Project: ${process.env.FIREBASE_PROJECT_ID || 'n/a'}`);
console.log(`=================================================`);

// --- Server Start ---
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`=================================================`);
  console.log(` Freqtrade Bot Manager (API Gateway + SSE)`);
  console.log(`-------------------------------------------------`);
  console.log(` HTTP Server: http://0.0.0.0:${PORT}`);
  console.log(` SSE Stream: GET /api/stream?token=...`);
  console.log(` Bot Instance Base Dir: ${BOT_BASE_DIR}`);
  console.log(` Main Strategies Source: ${MAIN_STRATEGIES_SOURCE_DIR}`);
  console.log(` SHARED Data Directory: ${SHARED_DATA_DIR}`);
  console.log(` Firebase Admin: ${firebaseInitialized ? 'enabled' : 'not initialized'}; Project: ${process.env.FIREBASE_PROJECT_ID || 'n/a'}`);
  console.log(`=================================================`);
});

// Export functions used by other modules
module.exports = {
  proxyFreqtradeApiRequest,
  runDockerCommand,
  resolveInstanceDir,
  BOT_BASE_DIR,
  setPortfolioMonitor
};

// Graceful shutdown (unchanged)
process.on('SIGTERM', () => { console.log('SIGTERM: closing server'); server.close(() => { console.log('Server closed'); process.exit(0); }); setTimeout(() => process.exit(1), 10000); });
process.on('SIGINT', () => { console.log('SIGINT: closing server'); server.close(() => { console.log('Server closed'); process.exit(0); }); setTimeout(() => process.exit(1), 10000); });

// --- Positions API Endpoint ---
app.get('/api/portfolio/positions', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id || req.user.uid;
    const { status = 'all' } = req.query; // 'open', 'closed', 'all'

    // Get current portfolio data which includes all bot positions
    const portfolioData = await aggregateUserPortfolio(userId);

    if (!portfolioData || !portfolioData.bots) {
      return res.json({
        success: true,
        positions: [],
        summary: {
          totalPositions: 0,
          openPositions: 0,
          closedPositions: 0,
          totalUnrealizedPnL: 0,
          totalRealizedPnL: 0
        }
      });
    }

    let allPositions = [];
    let totalUnrealizedPnL = 0;
    let totalRealizedPnL = 0;

    // Extract positions from all bots
    for (const bot of portfolioData.bots) {
      if (bot.status && Array.isArray(bot.status)) {
        for (const trade of bot.status) {
          // Filter based on status query parameter
          if (status === 'open' && !trade.is_open) continue;
          if (status === 'closed' && trade.is_open) continue;

          const position = {
            // Bot information
            botId: bot.instanceId,

            // Trade identification
            tradeId: trade.trade_id,
            pair: trade.pair,
            baseCurrency: trade.base_currency,
            quoteCurrency: trade.quote_currency,
            exchange: trade.exchange,
            strategy: trade.strategy,

            // Position details
            isOpen: trade.is_open,
            isShort: trade.is_short || false,
            amount: trade.amount,
            stakeAmount: trade.stake_amount,
            leverage: trade.leverage || 1,
            tradingMode: trade.trading_mode || 'spot',

            // Entry information
            openDate: trade.open_date,
            openTimestamp: trade.open_timestamp,
            openRate: trade.open_rate,
            openTradeValue: trade.open_trade_value,

            // Exit information (null if position is open)
            closeDate: trade.close_date,
            closeTimestamp: trade.close_timestamp,
            closeRate: trade.close_rate,
            exitReason: trade.exit_reason,

            // P&L information
            profitRatio: trade.profit_ratio || 0,
            profitPct: trade.profit_pct || 0,
            profitAbs: trade.profit_abs || 0,
            totalProfitAbs: trade.total_profit_abs || 0,
            totalProfitRatio: trade.total_profit_ratio || 0,
            realizedProfit: trade.realized_profit || 0,

            // Current market information
            currentRate: trade.current_rate,
            minRate: trade.min_rate,
            maxRate: trade.max_rate,

            // Risk management
            stopLossAbs: trade.stop_loss_abs,
            stopLossRatio: trade.stop_loss_ratio,
            stopLossPct: trade.stop_loss_pct,

            // Fee information
            feeOpen: trade.fee_open || 0,
            feeOpenCost: trade.fee_open_cost || 0,
            feeClose: trade.fee_close || 0,
            feeCloseCost: trade.fee_close_cost || 0,

            // Order information
            hasOpenOrders: trade.has_open_orders || false,
            orderCount: trade.orders ? trade.orders.length : 0,

            // Additional metrics
            timeframe: trade.timeframe,
            enterTag: trade.enter_tag || '',

            // Calculated fields
            durationMinutes: trade.is_open && trade.open_timestamp ?
              Math.floor((Date.now() - trade.open_timestamp) / (1000 * 60)) : null,
            profitUsd: trade.total_profit_abs || trade.profit_abs || 0
          };

          allPositions.push(position);

          // Accumulate P&L
          if (trade.is_open) {
            totalUnrealizedPnL += (trade.total_profit_abs || trade.profit_abs || 0);
          } else {
            totalRealizedPnL += (trade.total_profit_abs || trade.profit_abs || 0);
          }
        }
      }
    }

    // Sort positions by open timestamp (newest first)
    allPositions.sort((a, b) => (b.openTimestamp || 0) - (a.openTimestamp || 0));

    // Calculate summary statistics
    const openPositions = allPositions.filter(p => p.isOpen);
    const closedPositions = allPositions.filter(p => !p.isOpen);

    const summary = {
      totalPositions: allPositions.length,
      openPositions: openPositions.length,
      closedPositions: closedPositions.length,
      totalUnrealizedPnL: Number(totalUnrealizedPnL.toFixed(2)),
      totalRealizedPnL: Number(totalRealizedPnL.toFixed(2)),
      totalPnL: Number((totalUnrealizedPnL + totalRealizedPnL).toFixed(2)),

      // Additional summary stats
      uniquePairs: [...new Set(allPositions.map(p => p.pair))].length,
      uniqueBots: [...new Set(allPositions.map(p => p.botId))].length,
      totalStakeAmount: Number(allPositions.reduce((sum, p) => sum + (p.stakeAmount || 0), 0).toFixed(2)),
      averagePositionSize: allPositions.length > 0 ?
        Number((allPositions.reduce((sum, p) => sum + (p.stakeAmount || 0), 0) / allPositions.length).toFixed(2)) : 0,

      // Profit statistics
      profitablePositions: allPositions.filter(p => (p.profitAbs || 0) > 0).length,
      losingPositions: allPositions.filter(p => (p.profitAbs || 0) < 0).length,
      winRate: allPositions.length > 0 ?
        Number(((allPositions.filter(p => (p.profitAbs || 0) > 0).length / allPositions.length) * 100).toFixed(2)) : 0
    };

    res.json({
      success: true,
      positions: allPositions,
      summary,
      metadata: {
        timestamp: Date.now(),
        userId,
        filter: status,
        source: 'live_data'
      }
    });

  } catch (error) {
    console.error('[PositionsAPI] Error fetching positions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch positions',
      error: error.message
    });
  }
});

app.get('/api/portfolio/positions/:botId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id || req.user.uid;
    const { botId } = req.params;
    const { status = 'all' } = req.query;

    // Get current portfolio data
    const portfolioData = await aggregateUserPortfolio(userId);

    if (!portfolioData || !portfolioData.bots) {
      return res.status(404).json({
        success: false,
        message: 'No bots found for user'
      });
    }

    // Find the specific bot
    const bot = portfolioData.bots.find(b => b.instanceId === botId);
    if (!bot) {
      return res.status(404).json({
        success: false,
        message: `Bot ${botId} not found`
      });
    }

    let positions = [];

    if (bot.status && Array.isArray(bot.status)) {
      for (const trade of bot.status) {
        // Filter based on status query parameter
        if (status === 'open' && !trade.is_open) continue;
        if (status === 'closed' && trade.is_open) continue;

        const position = {
          tradeId: trade.trade_id,
          pair: trade.pair,
          baseCurrency: trade.base_currency,
          quoteCurrency: trade.quote_currency,
          exchange: trade.exchange,
          strategy: trade.strategy,
          isOpen: trade.is_open,
          isShort: trade.is_short || false,
          amount: trade.amount,
          stakeAmount: trade.stake_amount,
          openDate: trade.open_date,
          openTimestamp: trade.open_timestamp,
          openRate: trade.open_rate,
          currentRate: trade.current_rate,
          profitAbs: trade.profit_abs || 0,
          profitPct: trade.profit_pct || 0,
          profitRatio: trade.profit_ratio || 0,
          totalProfitAbs: trade.total_profit_abs || 0,
          stopLossAbs: trade.stop_loss_abs,
          hasOpenOrders: trade.has_open_orders || false,
          orders: trade.orders || []
        };

        positions.push(position);
      }
    }

    // Sort by open timestamp (newest first)
    positions.sort((a, b) => (b.openTimestamp || 0) - (a.openTimestamp || 0));

    res.json({
      success: true,
      botId,
      positions,
      summary: {
        totalPositions: positions.length,
        openPositions: positions.filter(p => p.isOpen).length,
        closedPositions: positions.filter(p => !p.isOpen).length,
        totalPnL: positions.reduce((sum, p) => sum + (p.totalProfitAbs || 0), 0)
      }
    });

  } catch (error) {
    console.error('[PositionsAPI] Error fetching bot positions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bot positions',
      error: error.message
    });
  }
});

// --- ENHANCED RISK MANAGEMENT API ENDPOINTS ---

// Get risk management configuration for a bot
app.get('/api/bots/:instanceId/risk-config', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const user = req.user || {};
    const userId = user.uid || user.id;

    const instanceDir = path.join(BOT_BASE_DIR, userId, instanceId);
    const riskConfigPath = path.join(instanceDir, 'risk-config.json');

    let riskConfig = {
      // Default risk management settings
      maxDrawdown: 0.15,
      maxTotalRisk: 0.25,
      riskPerTrade: 0.02,
      positionSizing: {
        baseStakePercent: 0.10,
        maxStakePercent: 0.25,
        volatilityAdjustment: true
      },
      stopLoss: {
        enabled: true,
        baseStopLoss: -0.08,
        trailingStop: true,
        dynamicAdjustment: true
      },
      dca: {
        enabled: false,
        maxOrders: 3,
        triggerPercent: -0.08,
        sizeMultiplier: 1.5
      },
      rebalancing: {
        enabled: false,
        threshold: 0.15,
        frequency: 24,
        targetAllocations: {
          btc: 0.40,
          eth: 0.25,
          alt: 0.20,
          stable: 0.10,
          other: 0.05
        }
      },
      leverageSettings: {
        maxLeverage: 1.0,
        conservativeMode: true
      }
    };

    // Load existing config if it exists
    if (await fs.pathExists(riskConfigPath)) {
      try {
        const existingConfig = JSON.parse(await fs.readFile(riskConfigPath, 'utf8'));
        riskConfig = { ...riskConfig, ...existingConfig };
      } catch (err) {
        console.warn(`[RiskAPI] Error reading risk config for ${instanceId}: ${err.message}`);
      }
    }

    res.json({
      success: true,
      instanceId,
      riskConfig
    });

  } catch (error) {
    console.error(`[RiskAPI] Error getting risk config for ${req.params.instanceId}:`, error.message);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Update risk management configuration for a bot
app.put('/api/bots/:instanceId/risk-config', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const { riskConfig } = req.body;
    const user = req.user || {};
    const userId = user.uid || user.id;

    if (!riskConfig) {
      return res.status(400).json({ success: false, message: 'Risk configuration is required' });
    }

    const instanceDir = path.join(BOT_BASE_DIR, userId, instanceId);
    const riskConfigPath = path.join(instanceDir, 'risk-config.json');
    const configPath = path.join(instanceDir, 'config.json');

    // Validate risk configuration
    const validatedConfig = validateRiskConfig(riskConfig);
    if (!validatedConfig.valid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid risk configuration',
        errors: validatedConfig.errors
      });
    }

    // Save risk configuration
    await fs.writeFile(riskConfigPath, JSON.stringify(riskConfig, null, 2));
    console.log(`[RiskAPI] ✓ Risk config saved for ${instanceId}`);

    // Update bot's main config.json with risk settings
    if (await fs.pathExists(configPath)) {
      try {
        const botConfig = JSON.parse(await fs.readFile(configPath, 'utf8'));

        // Update strategy-specific parameters based on risk config
        botConfig.max_open_trades = Math.min(Math.floor(1 / riskConfig.riskPerTrade), 25);
        botConfig.stoploss = riskConfig.stopLoss.baseStopLoss;

        // Update trailing stop settings
        if (riskConfig.stopLoss.trailingStop) {
          botConfig.trailing_stop = true;
          botConfig.trailing_stop_positive = 0.02;
          botConfig.trailing_stop_positive_offset = 0.04;
        } else {
          botConfig.trailing_stop = false;
        }

        await fs.writeFile(configPath, JSON.stringify(botConfig, null, 2));
        console.log(`[RiskAPI] ✓ Bot config updated for ${instanceId}`);

        // Restart bot if running to apply new settings
        const containerName = `freqtrade-${instanceId}`;
        try {
          const statusOutput = await runDockerCommand(['ps', '--filter', `name=${containerName}`, '--format', '{{.Names}}']);
          const isRunning = statusOutput.includes(containerName);

          if (isRunning) {
            console.log(`[RiskAPI] Restarting ${instanceId} to apply risk settings...`);
            await runDockerCommand(['restart', containerName]);
            console.log(`[RiskAPI] ✓ Bot restarted successfully`);
          }
        } catch (restartErr) {
          console.warn(`[RiskAPI] Failed to restart bot: ${restartErr.message}`);
        }

      } catch (configErr) {
        console.warn(`[RiskAPI] Failed to update bot config: ${configErr.message}`);
      }
    }

    res.json({
      success: true,
      message: 'Risk configuration updated successfully',
      instanceId,
      riskConfig
    });

  } catch (error) {
    console.error(`[RiskAPI] Error updating risk config for ${req.params.instanceId}:`, error.message);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get available risk management templates
app.get('/api/risk-templates', authenticateToken, async (req, res) => {
  try {
    const templates = {
      conservative: {
        name: 'Conservative',
        description: 'Low risk, steady growth approach',
        maxDrawdown: 0.10,
        maxTotalRisk: 0.15,
        riskPerTrade: 0.015,
        positionSizing: {
          baseStakePercent: 0.08,
          maxStakePercent: 0.15,
          volatilityAdjustment: true
        },
        stopLoss: {
          enabled: true,
          baseStopLoss: -0.06,
          trailingStop: true,
          dynamicAdjustment: true
        },
        dca: {
          enabled: true,
          maxOrders: 2,
          triggerPercent: -0.05,
          sizeMultiplier: 1.2
        }
      },
      balanced: {
        name: 'Balanced',
        description: 'Moderate risk with balanced growth potential',
        maxDrawdown: 0.15,
        maxTotalRisk: 0.25,
        riskPerTrade: 0.02,
        positionSizing: {
          baseStakePercent: 0.10,
          maxStakePercent: 0.25,
          volatilityAdjustment: true
        },
        stopLoss: {
          enabled: true,
          baseStopLoss: -0.08,
          trailingStop: true,
          dynamicAdjustment: true
        },
        dca: {
          enabled: true,
          maxOrders: 3,
          triggerPercent: -0.08,
          sizeMultiplier: 1.5
        }
      },
      aggressive: {
        name: 'Aggressive',
        description: 'Higher risk for maximum growth potential',
        maxDrawdown: 0.25,
        maxTotalRisk: 0.35,
        riskPerTrade: 0.03,
        positionSizing: {
          baseStakePercent: 0.15,
          maxStakePercent: 0.35,
          volatilityAdjustment: true
        },
        stopLoss: {
          enabled: true,
          baseStopLoss: -0.12,
          trailingStop: true,
          dynamicAdjustment: true
        },
        dca: {
          enabled: true,
          maxOrders: 5,
          triggerPercent: -0.12,
          sizeMultiplier: 2.0
        }
      },
      dcaFocused: {
        name: 'DCA Focused',
        description: 'Dollar Cost Averaging strategy with systematic buying',
        maxDrawdown: 0.20,
        maxTotalRisk: 0.30,
        riskPerTrade: 0.02,
        positionSizing: {
          baseStakePercent: 0.12,
          maxStakePercent: 0.45,
          volatilityAdjustment: true
        },
        stopLoss: {
          enabled: true,
          baseStopLoss: -0.12,
          trailingStop: true,
          dynamicAdjustment: true
        },
        dca: {
          enabled: true,
          maxOrders: 5,
          triggerPercent: -0.05,
          sizeMultiplier: 1.5,
          levels: [-0.05, -0.10, -0.18, -0.28],
          multipliers: [1.2, 1.5, 2.0, 2.5]
        }
      },
      portfolioRebalancing: {
        name: 'Portfolio Rebalancing',
        description: 'Maintains target allocations across multiple assets',
        maxDrawdown: 0.18,
        maxTotalRisk: 0.25,
        riskPerTrade: 0.02,
        positionSizing: {
          baseStakePercent: 0.10,
          maxStakePercent: 0.30,
          volatilityAdjustment: true
        },
        stopLoss: {
          enabled: true,
          baseStopLoss: -0.10,
          trailingStop: false,
          dynamicAdjustment: true
        },
        rebalancing: {
          enabled: true,
          threshold: 0.15,
          frequency: 24,
          targetAllocations: {
            btc: 0.40,
            eth: 0.25,
            alt: 0.20,
            stable: 0.10,
            other: 0.05
          }
        }
      }
    };

    res.json({
      success: true,
      templates
    });

  } catch (error) {
    console.error('[RiskAPI] Error getting risk templates:', error.message);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Apply a risk template to a bot
app.post('/api/bots/:instanceId/apply-risk-template', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const { templateName, customizations } = req.body;
    const user = req.user || {};
    const userId = user.uid || user.id;

    if (!templateName) {
      return res.status(400).json({ success: false, message: 'Template name is required' });
    }

    // Get the template
    const templatesResponse = await fetch(`${req.protocol}://${req.get('host')}/api/risk-templates`, {
      headers: { 'Authorization': req.get('Authorization') }
    });
    const templatesData = await templatesResponse.json();

    if (!templatesData.success || !templatesData.templates[templateName]) {
      return res.status(400).json({ success: false, message: 'Invalid template name' });
    }

    let riskConfig = { ...templatesData.templates[templateName] };

    // Apply customizations if provided
    if (customizations) {
      riskConfig = { ...riskConfig, ...customizations };
    }

    // Apply the configuration
    const applyResponse = await fetch(`${req.protocol}://${req.get('host')}/api/bots/${instanceId}/risk-config`, {
      method: 'PUT',
      headers: {
        'Authorization': req.get('Authorization'),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ riskConfig })
    });

    const applyResult = await applyResponse.json();

    if (applyResult.success) {
      res.json({
        success: true,
        message: `${riskConfig.name} template applied successfully`,
        instanceId,
        appliedTemplate: templateName,
        riskConfig
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to apply template',
        error: applyResult.message
      });
    }

  } catch (error) {
    console.error(`[RiskAPI] Error applying risk template for ${req.params.instanceId}:`, error.message);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get enhanced bot metrics including risk metrics
app.get('/api/bots/:instanceId/metrics', authenticateToken, checkInstanceOwnership, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const user = req.user || {};
    const userId = user.uid || user.id;

    // Get basic bot data
    const botData = await getBotAggregates(instanceId);

    // Get risk configuration
    const riskConfigResponse = await fetch(`${req.protocol}://${req.get('host')}/api/bots/${instanceId}/risk-config`, {
      headers: { 'Authorization': req.get('Authorization') }
    });
    const riskConfigData = await riskConfigResponse.json();

    // Calculate risk metrics
    const riskMetrics = await calculateRiskMetrics(instanceId, userId, riskConfigData.riskConfig);

    res.json({
      success: true,
      instanceId,
      botData,
      riskMetrics,
      riskConfig: riskConfigData.riskConfig
    });

  } catch (error) {
    console.error(`[RiskAPI] Error getting bot metrics for ${req.params.instanceId}:`, error.message);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Helper function to validate risk configuration
function validateRiskConfig(config) {
  const errors = [];

  // Validate maxDrawdown
  if (typeof config.maxDrawdown !== 'number' || config.maxDrawdown < 0.05 || config.maxDrawdown > 0.50) {
    errors.push('maxDrawdown must be between 0.05 and 0.50');
  }

  // Validate maxTotalRisk
  if (typeof config.maxTotalRisk !== 'number' || config.maxTotalRisk < 0.10 || config.maxTotalRisk > 0.50) {
    errors.push('maxTotalRisk must be between 0.10 and 0.50');
  }

  // Validate riskPerTrade
  if (typeof config.riskPerTrade !== 'number' || config.riskPerTrade < 0.005 || config.riskPerTrade > 0.10) {
    errors.push('riskPerTrade must be between 0.005 and 0.10');
  }

  // Validate position sizing
  if (config.positionSizing) {
    if (typeof config.positionSizing.baseStakePercent !== 'number' ||
      config.positionSizing.baseStakePercent < 0.02 ||
      config.positionSizing.baseStakePercent > 0.30) {
      errors.push('baseStakePercent must be between 0.02 and 0.30');
    }

    if (typeof config.positionSizing.maxStakePercent !== 'number' ||
      config.positionSizing.maxStakePercent < 0.05 ||
      config.positionSizing.maxStakePercent > 0.60) {
      errors.push('maxStakePercent must be between 0.05 and 0.60');
    }
  }

  // Validate DCA settings
  if (config.dca && config.dca.enabled) {
    if (typeof config.dca.maxOrders !== 'number' || config.dca.maxOrders < 1 || config.dca.maxOrders > 10) {
      errors.push('DCA maxOrders must be between 1 and 10');
    }

    if (typeof config.dca.triggerPercent !== 'number' || config.dca.triggerPercent > -0.01 || config.dca.triggerPercent < -0.50) {
      errors.push('DCA triggerPercent must be between -0.50 and -0.01');
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Helper function to calculate risk metrics
async function calculateRiskMetrics(instanceId, userId, riskConfig) {
  try {
    const metrics = {
      currentDrawdown: 0,
      totalRiskExposure: 0,
      positionCount: 0,
      averagePositionSize: 0,
      riskUtilization: 0,
      stopLossCount: 0,
      dcaOrdersActive: 0,
      rebalanceSignals: 0
    };

    // Get current open trades
    const tradesData = await proxyFreqtradeApiRequest(instanceId, '/api/v1/status');

    if (tradesData && Array.isArray(tradesData)) {
      const openTrades = tradesData.filter(trade => trade.is_open);
      metrics.positionCount = openTrades.length;

      if (openTrades.length > 0) {
        // Calculate total position value and risk
        let totalPositionValue = 0;
        let totalUnrealizedLoss = 0;
        let stopLossCount = 0;
        let dcaOrders = 0;

        openTrades.forEach(trade => {
          totalPositionValue += trade.stake_amount || 0;

          if (trade.profit_abs < 0) {
            totalUnrealizedLoss += Math.abs(trade.profit_abs);
          }

          if (trade.stop_loss_abs) {
            stopLossCount++;
          }

          // Count DCA orders (simplified check)
          if (trade.orders && trade.orders.length > 1) {
            dcaOrders += trade.orders.length - 1;
          }
        });

        metrics.averagePositionSize = totalPositionValue / openTrades.length;
        metrics.stopLossCount = stopLossCount;
        metrics.dcaOrdersActive = dcaOrders;

        // Calculate risk utilization
        const totalStake = await getTotalStakeAmount(userId);
        if (totalStake > 0) {
          metrics.totalRiskExposure = totalPositionValue / totalStake;
          metrics.currentDrawdown = totalUnrealizedLoss / totalStake;
          metrics.riskUtilization = metrics.totalRiskExposure / (riskConfig.maxTotalRisk || 0.25);
        }
      }
    }

    // Add timestamp
    metrics.lastCalculated = new Date().toISOString();

    return metrics;

  } catch (error) {
    console.error(`Error calculating risk metrics for ${instanceId}:`, error.message);
    return {
      error: error.message,
      lastCalculated: new Date().toISOString()
    };
  }
}

// Helper function to get total stake amount for a user
async function getTotalStakeAmount(userId) {
  try {
    // This would ideally get the total stake from the bot's wallet
    // For now, return a default value
    return 10000; // Default $10,000 portfolio
  } catch (error) {
    console.error(`Error getting total stake for ${userId}:`, error.message);
    return 0;
  }
}

// =============================================================================
// UNIVERSAL RISK MANAGEMENT API ENDPOINTS
// Frontend Integration for Risk Level, Auto-Rebalance, and DCA toggles
// =============================================================================

// Helper function to resolve bot instance path
async function resolveBotInstancePath(instanceId) {
  // Try direct path first (for backwards compatibility)
  let instanceDir = path.join(INSTANCES_DIR, instanceId);
  if (await fs.pathExists(instanceDir)) {
    return instanceDir;
  }

  // Search in user directories for the bot
  const userDirs = await fs.readdir(INSTANCES_DIR);
  for (const userId of userDirs) {
    const userDir = path.join(INSTANCES_DIR, userId);
    const stat = await fs.stat(userDir).catch(() => null);
    if (stat && stat.isDirectory()) {
      const botDir = path.join(userDir, instanceId);
      if (await fs.pathExists(botDir)) {
        return botDir;
      }
    }
  }

  return null;
}

// Get current universal settings for a bot
app.get('/api/universal-settings/:instanceId', authenticateToken, async (req, res) => {
  try {
    const { instanceId } = req.params;
    console.log(`[API] Getting universal settings for: ${instanceId}`);

    const instanceDir = await resolveBotInstancePath(instanceId);
    console.log(`[API] Resolved instance directory: ${instanceDir}`);

    if (!instanceDir) {
      console.log(`[API] Bot instance not found: ${instanceId}`);
      return res.status(404).json({ success: false, error: 'Bot instance not found' });
    }

    console.log(`[API] Creating UniversalRiskManager for: ${instanceId}`);
    const riskManager = new UniversalRiskManager(instanceId, instanceDir);

    console.log(`[API] Loading settings...`);
    await riskManager.loadSettings();

    console.log(`[API] Settings loaded successfully: ${JSON.stringify(riskManager.settings)}`);

    // Include default values for frontend initialization
    const response = {
      success: true,
      settings: riskManager.settings,
      defaults: riskManager.defaultSettings,
      schema: {
        riskLevel: {
          type: 'number',
          min: 0,
          max: 100,
          default: riskManager.defaultSettings.riskLevel,
          description: 'Risk level from conservative (0) to aggressive (100)'
        },
        autoRebalance: {
          type: 'boolean',
          default: riskManager.defaultSettings.autoRebalance,
          description: 'Enable automatic portfolio rebalancing'
        },
        dcaEnabled: {
          type: 'boolean',
          default: riskManager.defaultSettings.dcaEnabled,
          description: 'Enable Dollar Cost Averaging orders'
        },
        enabled: {
          type: 'boolean',
          default: riskManager.defaultSettings.enabled,
          description: 'Master toggle for all universal risk management features'
        }
      },
      isNewBot: !await fs.pathExists(riskManager.settingsPath),
      message: 'Universal settings retrieved successfully'
    };

    // If this is a new bot, ensure settings file is created with defaults
    if (response.isNewBot) {
      console.log(`[API] Initializing default settings for new bot: ${instanceId}`);
      await riskManager.saveSettings();
      await riskManager.updateRiskConfig();
      console.log(`[${instanceId}] ✓ Initialized default universal settings for new bot`);
    }

    console.log(`[API] Sending response for: ${instanceId}`);
    res.json(response);

  } catch (error) {
    console.error('Error getting universal settings:', error.message);
    console.error('Error stack:', error.stack);
    res.status(500).json({ success: false, error: 'Failed to get universal settings' });
  }
});

// Get default universal settings schema (for frontend initialization)
app.get('/api/universal-settings-defaults', authenticateToken, async (req, res) => {
  try {
    // Create a temporary risk manager to get default values
    const tempRiskManager = new UniversalRiskManager('temp', '/tmp');

    const defaultsSchema = {
      success: true,
      defaults: tempRiskManager.defaultSettings,
      schema: {
        riskLevel: {
          type: 'number',
          min: 0,
          max: 100,
          default: tempRiskManager.defaultSettings.riskLevel,
          step: 1,
          description: 'Risk level from conservative (0) to aggressive (100)',
          examples: {
            0: 'Ultra Conservative - 5% max drawdown, 1% risk per trade',
            25: 'Conservative - 10% max drawdown, 1.5% risk per trade',
            50: 'Balanced - 15% max drawdown, 2% risk per trade',
            75: 'Aggressive - 20% max drawdown, 2.5% risk per trade',
            100: 'Ultra Aggressive - 25% max drawdown, 3% risk per trade'
          }
        },
        autoRebalance: {
          type: 'boolean',
          default: tempRiskManager.defaultSettings.autoRebalance,
          description: 'Automatically rebalance portfolio to maintain target allocations',
          details: 'Monitors portfolio drift and rebalances when allocation deviates beyond threshold'
        },
        dcaEnabled: {
          type: 'boolean',
          default: tempRiskManager.defaultSettings.dcaEnabled,
          description: 'Enable Dollar Cost Averaging on losing positions',
          details: 'Places additional orders when price drops to average down position cost'
        },
        enabled: {
          type: 'boolean',
          default: tempRiskManager.defaultSettings.enabled,
          description: 'Master toggle for all universal risk management features',
          details: 'When disabled, bot uses only the original strategy without enhancements'
        }
      },
      riskLevelMapping: {
        0: { label: 'Ultra Conservative', maxDrawdown: '5%', riskPerTrade: '1%', dcaOrders: 2 },
        25: { label: 'Conservative', maxDrawdown: '10%', riskPerTrade: '1.5%', dcaOrders: 2 },
        50: { label: 'Balanced', maxDrawdown: '15%', riskPerTrade: '2%', dcaOrders: 3 },
        75: { label: 'Aggressive', maxDrawdown: '20%', riskPerTrade: '2.5%', dcaOrders: 4 },
        100: { label: 'Ultra Aggressive', maxDrawdown: '25%', riskPerTrade: '3%', dcaOrders: 5 }
      },
      message: 'Default universal settings schema retrieved successfully'
    };

    res.json(defaultsSchema);

  } catch (error) {
    console.error('Error getting default settings schema:', error);
    res.status(500).json({ success: false, error: 'Failed to get default settings schema' });
  }
});

// Update universal settings for a bot (Frontend "Save Changes" endpoint)
app.put('/api/universal-settings/:instanceId', authenticateToken, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const { riskLevel, autoRebalance, dcaEnabled, enabled = true } = req.body;

    const instanceDir = await resolveBotInstancePath(instanceId);

    if (!instanceDir) {
      return res.status(404).json({ success: false, error: 'Bot instance not found' });
    }

    // Validate input
    if (riskLevel !== undefined && (riskLevel < 0 || riskLevel > 100)) {
      return res.status(400).json({ success: false, error: 'Risk level must be between 0 and 100' });
    }

    const riskManager = new UniversalRiskManager(instanceId, instanceDir);
    await riskManager.loadSettings();

    // Update settings
    const newSettings = {};
    if (riskLevel !== undefined) newSettings.riskLevel = riskLevel;
    if (autoRebalance !== undefined) newSettings.autoRebalance = autoRebalance;
    if (dcaEnabled !== undefined) newSettings.dcaEnabled = dcaEnabled;
    if (enabled !== undefined) newSettings.enabled = enabled;

    await riskManager.updateSettings(newSettings);

    console.log(`[API] ✓ Universal settings updated for ${instanceId}:`, newSettings);

    res.json({
      success: true,
      settings: riskManager.settings,
      message: 'Universal settings updated successfully'
    });

  } catch (error) {
    console.error('Error updating universal settings:', error);
    res.status(500).json({ success: false, error: 'Failed to update universal settings' });
  }
});

// Get risk metrics with universal risk management data
app.get('/api/risk-metrics/:instanceId', authenticateToken, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const instanceDir = await resolveBotInstancePath(instanceId);

    if (!instanceDir) {
      return res.status(404).json({ success: false, error: 'Bot instance not found' });
    }

    // Get standard risk metrics
    const metrics = await calculateRiskMetrics(instanceId);

    // Add universal risk management status
    const riskManager = new UniversalRiskManager(instanceId, instanceDir);
    await riskManager.loadSettings();

    metrics.universalRiskManagement = {
      enabled: riskManager.settings.enabled,
      riskLevel: riskManager.settings.riskLevel,
      autoRebalance: riskManager.settings.autoRebalance,
      dcaEnabled: riskManager.settings.dcaEnabled,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      metrics: metrics,
      message: 'Risk metrics with universal settings retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting risk metrics:', error);
    res.status(500).json({ success: false, error: 'Failed to get risk metrics' });
  }
});

// Reset universal settings to defaults
app.post('/api/universal-settings/:instanceId/reset', authenticateToken, async (req, res) => {
  try {
    const { instanceId } = req.params;
    const instanceDir = await resolveBotInstancePath(instanceId);

    if (!instanceDir) {
      return res.status(404).json({ success: false, error: 'Bot instance not found' });
    }

    const riskManager = new UniversalRiskManager(instanceId, instanceDir);
    await riskManager.updateSettings(riskManager.defaultSettings);

    console.log(`[API] ✓ Universal settings reset to defaults for ${instanceId}`);

    res.json({
      success: true,
      settings: riskManager.settings,
      message: 'Universal settings reset to defaults successfully'
    });

  } catch (error) {
    console.error('Error resetting universal settings:', error);
    res.status(500).json({ success: false, error: 'Failed to reset universal settings' });
  }
});

// Get all bots with their universal settings (for dashboard overview)
app.get('/api/universal-settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.uid;
    if (!userId) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }

    const userDir = path.join(INSTANCES_DIR);
    const instances = await fs.readdir(userDir);
    const userInstances = instances.filter(inst => inst.startsWith(userId));

    const botsWithSettings = [];

    for (const instanceId of userInstances) {
      const instanceDir = path.join(userDir, instanceId);
      const riskManager = new UniversalRiskManager(instanceId, instanceDir);
      await riskManager.loadSettings();

      botsWithSettings.push({
        instanceId: instanceId,
        settings: riskManager.settings,
        defaults: riskManager.defaultSettings,
        isRunning: await isBotRunning(instanceId),
        isNewBot: !await fs.pathExists(riskManager.settingsPath),
        lastUpdated: (await fs.stat(riskManager.settingsPath).catch(() => null))?.mtime
      });
    }

    // Get default settings for frontend reference
    const tempRiskManager = new UniversalRiskManager('temp', '/tmp');

    res.json({
      success: true,
      bots: botsWithSettings,
      defaults: tempRiskManager.defaultSettings,
      totalBots: botsWithSettings.length,
      runningBots: botsWithSettings.filter(bot => bot.isRunning).length,
      message: 'Universal settings for all bots retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting all universal settings:', error);
    res.status(500).json({ success: false, error: 'Failed to get universal settings' });
  }
});

// =============================================================================
// UNIVERSAL RISK MANAGEMENT BACKGROUND SERVICES
// =============================================================================

// Background service to apply universal risk management to running bots
async function runUniversalRiskManagement() {
  try {
    const instances = await fs.readdir(INSTANCES_DIR);

    for (const instanceId of instances) {
      const instanceDir = path.join(INSTANCES_DIR, instanceId);
      const riskManager = new UniversalRiskManager(instanceId, instanceDir);
      await riskManager.loadSettings();

      if (!riskManager.settings.enabled) continue;

      // Check if bot is running
      const isRunning = await isBotRunning(instanceId);
      if (!isRunning) continue;

      try {
        // Get bot's current trades and status
        const botUrl = await buildBotUrl(instanceId);
        if (!botUrl) continue;

        const tradesResponse = await fetch(`${botUrl}/api/v1/trades`, { timeout: 5000 });
        const openTrades = await tradesResponse.json();

        const statusResponse = await fetch(`${botUrl}/api/v1/status`, { timeout: 5000 });
        const status = await statusResponse.json();

        // Apply DCA management
        if (openTrades && Array.isArray(openTrades)) {
          for (const trade of openTrades.filter(t => t.is_open)) {
            // Get current price for the pair
            const tickerResponse = await fetch(`${botUrl}/api/v1/pair_ticker/${trade.pair}`, { timeout: 5000 });
            const ticker = await tickerResponse.json();

            if (ticker && ticker.last) {
              await riskManager.checkAndPlaceDCAOrders(trade.pair, ticker.last, [trade]);
            }
          }
        }

        // Apply auto-rebalancing
        if (status && status.total_stake) {
          const currentPositions = {};
          if (openTrades && Array.isArray(openTrades)) {
            for (const trade of openTrades.filter(t => t.is_open)) {
              currentPositions[trade.pair] = {
                value: trade.stake_amount,
                amount: trade.amount
              };
            }
          }

          await riskManager.checkAndRebalance(currentPositions, status.total_stake);
        }

      } catch (botError) {
        console.warn(`[${instanceId}] Universal risk management failed:`, botError.message);
      }
    }

  } catch (error) {
    console.error('Universal risk management background service error:', error);
  }
}

// Helper function to check if bot is running
async function isBotRunning(instanceId) {
  try {
    const botUrl = await buildBotUrl(instanceId);
    if (!botUrl) return false;

    const response = await fetch(`${botUrl}/api/v1/ping`, { timeout: 3000 });
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Start background services
console.log('🚀 Starting universal risk management background services...');

// Run universal risk management every 5 minutes
setInterval(runUniversalRiskManagement, 5 * 60 * 1000);

// Initial run after 30 seconds
setTimeout(runUniversalRiskManagement, 30 * 1000);
# Enhanced Bot Manager: Auto-Rebalancing, DCA & Risk Management

## Overview
The bot manager now supports advanced trading features including auto-rebalancing, Dollar Cost Averaging (DCA), and comprehensive risk management with adjustable risk levels.

## 🚀 New Features Implemented

### 1. Advanced Trading Strategies
- **EnhancedRiskManagedStrategy.py**: Comprehensive risk management with dynamic position sizing and volatility-based adjustments
- **DCAStrategy.py**: Dollar Cost Averaging with multi-level entry system (up to 4 DCA levels)
- **PortfolioRebalancingStrategy.py**: Automatic portfolio rebalancing with target allocation maintenance

### 2. Risk Management Templates
- **Conservative**: 10% max drawdown, 1.5% risk per trade, 2 DCA orders max
- **Balanced**: 15% max drawdown, 2% risk per trade, 3 DCA orders max  
- **Aggressive**: 25% max drawdown, 3% risk per trade, 5 DCA orders max
- **DCA Focused**: Optimized for Dollar Cost Averaging strategies
- **Portfolio Rebalancing**: Designed for balanced portfolio management

### 3. Enhanced API Endpoints

#### Get Available Strategies
```
GET /api/strategies
```
Returns list of available trading strategies including new enhanced ones.

#### Get Risk Templates
```
GET /api/risk-templates
```
Returns available risk management templates with their configurations.

#### Enhanced Bot Provisioning
```
POST /api/provision
{
  "userId": "user123",
  "strategy": "EnhancedRiskManagedStrategy",
  "riskTemplate": "balanced",
  "tradingPairs": ["BTC/USD", "ETH/USD", "SOL/USD"],
  "initialBalance": 10000,
  "enhanced": true
}
```

#### Risk Configuration Management
```
GET /api/risk-config/:instanceId
PUT /api/risk-config/:instanceId
POST /api/risk-config/:instanceId/reset
```

### 4. Strategy-Specific Features

#### EnhancedRiskManagedStrategy
- Dynamic position sizing based on volatility
- Custom stop-loss calculations
- Portfolio risk assessment
- DCA integration with position adjustment
- Volatility-based entry/exit timing

#### DCAStrategy  
- 4-level DCA system with smart sizing
- Custom entry price calculation
- Trade persistence for order tracking
- Adjustable DCA parameters via risk config

#### PortfolioRebalancingStrategy
- Target allocation maintenance
- Drift detection and correction
- Automated rebalancing triggers
- Asset categorization system

## 🔧 Configuration Examples

### Enhanced Bot Creation
```javascript
// Create a bot with DCA strategy and balanced risk
const botConfig = {
  userId: "trader001",
  strategy: "DCAStrategy", 
  riskTemplate: "dcaFocused",
  tradingPairs: ["BTC/USD", "ETH/USD", "ADA/USD"],
  initialBalance: 15000,
  enhanced: true
};
```

### Custom Risk Configuration
```javascript
const customRisk = {
  maxDrawdown: 0.12,
  maxTotalRisk: 0.20,
  riskPerTrade: 0.025,
  positionSizing: {
    baseStakePercent: 0.10,
    maxStakePercent: 0.20,
    volatilityAdjustment: true
  },
  stopLoss: {
    enabled: true,
    baseStopLoss: -0.08,
    trailingStop: true,
    dynamicAdjustment: true
  },
  dca: {
    enabled: true,
    maxOrders: 3,
    triggerPercent: -0.06,
    sizeMultiplier: 1.3
  }
};
```

## 📊 Key Benefits

1. **Risk Management**: Comprehensive risk controls with maximum drawdown limits and position sizing rules
2. **Auto-Rebalancing**: Automatic portfolio rebalancing to maintain target allocations
3. **DCA Integration**: Smart Dollar Cost Averaging with multiple entry levels
4. **Flexibility**: Choose from pre-configured risk templates or create custom configurations
5. **Safety**: All bots run in dry-run mode with local SQLite databases for safe testing

## 🛡️ Safety Features

- All bots default to dry-run mode for safe testing
- Local SQLite databases with WAL mode for better concurrent access
- Comprehensive error handling and logging
- Risk limits enforced at strategy and portfolio levels
- Automatic risk configuration validation

## 🎯 Usage Scenarios

1. **Conservative Investor**: Use `EnhancedRiskManagedStrategy` with `conservative` risk template
2. **DCA Enthusiast**: Use `DCAStrategy` with `dcaFocused` risk template  
3. **Portfolio Manager**: Use `PortfolioRebalancingStrategy` with `portfolioRebalancing` template
4. **Aggressive Trader**: Use any strategy with `aggressive` risk template

## 📁 File Structure
```
/root/Crypto-Pilot-Freqtrade/
├── Admin Strategies/
│   ├── EnhancedRiskManagedStrategy.py    # Advanced risk management
│   ├── DCAStrategy.py                    # Dollar Cost Averaging
│   └── PortfolioRebalancingStrategy.py   # Portfolio rebalancing
└── bot-manager/
    └── index.js                          # Enhanced API with risk management
```

The bot manager now provides enterprise-level trading capabilities with comprehensive risk management, auto-rebalancing, and DCA features that can be easily configured through the API.
#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');

// Configuration for database connection optimization
const OPTIMIZED_DB_URL = "sqlite:///user_data/tradesv3.sqlite?check_same_thread=False&timeout=60";

const DB_OPTIMIZATION_CONFIG = {
  internals: {
    process_throttle_secs: 120, // Increased to 2 minutes to reduce DB load even more
    heartbeat_interval: 600,    // 10 minutes heartbeat to reduce DB operations
    sd_notify: false            // Disable systemd notifications that can cause DB locks
  }
};

async function updateBotConfig(configPath) {
  try {
    console.log(`Updating config: ${configPath}`);

    // Read existing config
    const config = JSON.parse(await fs.readFile(configPath, 'utf8'));

    // Update db_url to use optimized SQLite URL with proper connection pooling
    config.db_url = OPTIMIZED_DB_URL;

    // Remove database_settings if it exists (FreqTrade doesn't use it properly)
    if (config.database_settings) {
      delete config.database_settings;
    }

    // Update internals
    config.internals = {
      ...config.internals,
      ...DB_OPTIMIZATION_CONFIG.internals
    };

    // Write updated config
    await fs.writeFile(configPath, JSON.stringify(config, null, 2));
    console.log(`✓ Updated: ${configPath}`);

    return true;
  } catch (error) {
    console.error(`✗ Failed to update ${configPath}:`, error.message);
    return false;
  }
}

async function main() {
  const BOT_BASE_DIR = process.env.BOT_BASE_DIR || path.join(__dirname, '..', 'freqtrade-instances');

  console.log('🔧 Fixing database connection issues for all bots...');
  console.log(`Bot base directory: ${BOT_BASE_DIR}`);

  let updatedCount = 0;
  let failedCount = 0;

  try {
    // Scan for all bot instances
    const users = await fs.readdir(BOT_BASE_DIR);

    for (const userId of users) {
      const userDir = path.join(BOT_BASE_DIR, userId);
      const userStat = await fs.stat(userDir);

      if (userStat.isDirectory()) {
        console.log(`\nProcessing user: ${userId}`);

        const instances = await fs.readdir(userDir);

        for (const instanceId of instances) {
          const instanceDir = path.join(userDir, instanceId);
          const instanceStat = await fs.stat(instanceDir);

          if (instanceStat.isDirectory()) {
            const configPath = path.join(instanceDir, 'config.json');

            if (await fs.pathExists(configPath)) {
              const success = await updateBotConfig(configPath);
              if (success) {
                updatedCount++;
              } else {
                failedCount++;
              }
            } else {
              console.log(`⚠️  No config.json found in ${instanceDir}`);
            }
          }
        }
      }
    }

    console.log('\n📊 Summary:');
    console.log(`✓ Successfully updated: ${updatedCount} configs`);
    console.log(`✗ Failed to update: ${failedCount} configs`);

    if (updatedCount > 0) {
      console.log('\n🔄 Now restart all bot containers to apply the changes:');
      console.log('   docker restart $(docker ps -q --filter "name=freqtrade-")');
    }

  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { updateBotConfig, DB_OPTIMIZATION_CONFIG };

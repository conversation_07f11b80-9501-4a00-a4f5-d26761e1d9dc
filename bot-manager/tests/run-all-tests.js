#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Running Complete Phase 4 Test Suite...\n');

const tests = [
    {
        name: 'Phase 4 Core Features',
        file: 'test-phase4.js',
        description: 'Portfolio Summary, System Health, Chart Data, Bot Status'
    },
    {
        name: 'Container Recreation',
        file: 'test-bot-start.js',
        description: 'Docker container recreation for missing/broken containers'
    },
    {
        name: 'Portfolio WebSocket',
        file: 'test-portfolio-websocket.js',
        description: 'Real-time portfolio updates via WebSocket'
    }
];

let totalPassed = 0;
let totalFailed = 0;

for (const test of tests) {
    console.log(`\n🧪 Running: ${test.name}`);
    console.log(`📝 Description: ${test.description}`);
    console.log('─'.repeat(50));

    try {
        const output = execSync(`node ${path.join(__dirname, test.file)}`, {
            encoding: 'utf8',
            timeout: 30000 // 30 second timeout
        });

        console.log(output);

        // Parse results if available
        const passMatch = output.match(/(\d+) passed/);
        const failMatch = output.match(/(\d+) failed/);

        if (passMatch) totalPassed += parseInt(passMatch[1]);
        if (failMatch) totalFailed += parseInt(failMatch[1]);

        console.log(`✅ ${test.name}: COMPLETED`);

    } catch (error) {
        console.log(`❌ ${test.name}: FAILED`);
        console.log(`Error: ${error.message}`);
        totalFailed++;
    }
}

console.log('\n' + '='.repeat(60));
console.log('📊 PHASE 4 TEST SUITE SUMMARY');
console.log('='.repeat(60));
console.log(`✅ Total Passed: ${totalPassed}`);
console.log(`❌ Total Failed: ${totalFailed}`);
console.log(`📈 Success Rate: ${totalPassed + totalFailed > 0 ? Math.round((totalPassed / (totalPassed + totalFailed)) * 100) : 0}%`);

if (totalFailed === 0) {
    console.log('\n🎉 ALL PHASE 4 FEATURES WORKING CORRECTLY!');
    console.log('🚀 Ready to proceed to Phase 5 implementation.');
} else {
    console.log('\n⚠️  Some tests failed. Please review and fix before Phase 5.');
}

console.log('\n📁 All test files organized in: /root/Crypto-Pilot-Freqtrade/bot-manager/tests/');

const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env') });

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
    console.error('JWT_SECRET not found in environment variables');
    process.exit(1);
}

// Create a test token for Phase 3 testing that won't conflict with Firebase
const testUser = {
    id: 'test-user-123',
    uid: 'test-user-123', // Firebase UID format
    email: '<EMAIL>',
    role: 'user',
    // Add custom claims to distinguish from Firebase tokens
    custom_jwt: true,
    token_type: 'bot_manager_jwt'
};

const token = jwt.sign(
    testUser,
    JWT_SECRET,
    {
        expiresIn: '24h',
        issuer: 'bot-manager',
        audience: 'freqtrade-users'
    }
);

console.log('='.repeat(80));
console.log('Phase 3 Test Token Generated');
console.log('='.repeat(80));
console.log('Token:', token);
console.log('');
console.log('User Info:');
console.log('- ID:', testUser.id);
console.log('- UID:', testUser.uid);
console.log('- Email:', testUser.email);
console.log('- Role:', testUser.role);
console.log('');
console.log('Copy this token into the Phase 3 test client:');
console.log('='.repeat(80));
console.log(token);
console.log('='.repeat(80));

#!/usr/bin/env node

/**
 * Phase 5: Production Monitoring & Metrics System
 * ===============================================
 * 
 * Comprehensive monitoring for production deployment
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

console.log('📊 PHASE 5: MONITORING & METRICS IMPLEMENTATION');
console.log('='.repeat(50));

// Monitoring configuration
const MONITORING_CONFIG = {
    METRICS_COLLECTION_INTERVAL: 30000, // 30 seconds
    HEALTH_CHECK_INTERVAL: 60000, // 1 minute
    LOG_ROTATION_SIZE: 100 * 1024 * 1024, // 100MB
    METRICS_RETENTION_HOURS: 24,
    ALERT_THRESHOLDS: {
        CPU_USAGE_PERCENT: 80,
        MEMORY_USAGE_MB: 500,
        RESPONSE_TIME_MS: 2000,
        ERROR_RATE_PERCENT: 5,
        CONNECTION_COUNT: 100
    }
};

/**
 * Production Monitoring System
 */
const MONITORING_SYSTEM_CODE = `
/**
 * Production Monitoring & Metrics System
 * ======================================
 * 
 * Real-time monitoring with health checks, performance metrics, and alerting
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

class ProductionMonitor {
    constructor(options = {}) {
        this.config = {
            metricsInterval: ${MONITORING_CONFIG.METRICS_COLLECTION_INTERVAL},
            healthCheckInterval: ${MONITORING_CONFIG.HEALTH_CHECK_INTERVAL},
            logRotationSize: ${MONITORING_CONFIG.LOG_ROTATION_SIZE},
            retentionHours: ${MONITORING_CONFIG.METRICS_RETENTION_HOURS},
            thresholds: ${JSON.stringify(MONITORING_CONFIG.ALERT_THRESHOLDS, null, 12)},
            ...options
        };
        
        // Metrics storage
        this.metrics = {
            system: {
                startTime: Date.now(),
                uptime: 0,
                cpu: [],
                memory: [],
                connections: [],
                responseTime: [],
                errors: [],
                requests: []
            },
            business: {
                activeUsers: 0,
                activeBots: 0,
                portfolioUpdates: 0,
                messagesProcessed: 0,
                botActions: 0
            },
            alerts: []
        };
        
        // Performance tracking
        this.requestTimes = new Map();
        this.errorCounts = new Map();
        
        // Start monitoring
        this.startMonitoring();
        
        console.log('[ProductionMonitor] Monitoring system initialized');
    }
    
    /**
     * Start all monitoring routines
     */
    startMonitoring() {
        // System metrics collection
        this.metricsInterval = setInterval(() => {
            this.collectSystemMetrics();
        }, this.config.metricsInterval);
        
        // Health checks
        this.healthInterval = setInterval(() => {
            this.performHealthCheck();
        }, this.config.healthCheckInterval);
        
        // Cleanup old metrics
        this.cleanupInterval = setInterval(() => {
            this.cleanupOldMetrics();
        }, 3600000); // 1 hour
        
        // Initial health check
        setTimeout(() => this.performHealthCheck(), 5000);
    }
    
    /**
     * Collect system performance metrics
     */
    collectSystemMetrics() {
        const now = Date.now();
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        // Memory metrics
        this.metrics.system.memory.push({
            timestamp: now,
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss
        });
        
        // CPU metrics (simplified - in production use better CPU monitoring)
        this.metrics.system.cpu.push({
            timestamp: now,
            user: cpuUsage.user,
            system: cpuUsage.system
        });
        
        // Uptime
        this.metrics.system.uptime = Date.now() - this.metrics.system.startTime;
        
        // Check thresholds and generate alerts
        this.checkAlertThresholds();
        
        console.log(\`[ProductionMonitor] Metrics collected - Memory: \${(memUsage.heapUsed/1024/1024).toFixed(2)}MB, Uptime: \${(this.metrics.system.uptime/3600000).toFixed(2)}h\`);
    }
    
    /**
     * Perform comprehensive health check
     */
    async performHealthCheck() {
        const healthCheck = {
            timestamp: Date.now(),
            status: 'healthy',
            checks: {}
        };
        
        try {
            // Memory health
            const memUsage = process.memoryUsage();
            const memUsageMB = memUsage.heapUsed / 1024 / 1024;
            healthCheck.checks.memory = {
                status: memUsageMB < this.config.thresholds.MEMORY_USAGE_MB ? 'healthy' : 'warning',
                value: memUsageMB,
                unit: 'MB',
                threshold: this.config.thresholds.MEMORY_USAGE_MB
            };
            
            // Response time health
            const avgResponseTime = this.getAverageResponseTime();
            healthCheck.checks.responseTime = {
                status: avgResponseTime < this.config.thresholds.RESPONSE_TIME_MS ? 'healthy' : 'warning',
                value: avgResponseTime,
                unit: 'ms',
                threshold: this.config.thresholds.RESPONSE_TIME_MS
            };
            
            // Error rate health
            const errorRate = this.getErrorRate();
            healthCheck.checks.errorRate = {
                status: errorRate < this.config.thresholds.ERROR_RATE_PERCENT ? 'healthy' : 'critical',
                value: errorRate,
                unit: '%',
                threshold: this.config.thresholds.ERROR_RATE_PERCENT
            };
            
            // Connection health
            healthCheck.checks.connections = {
                status: this.metrics.business.activeUsers < this.config.thresholds.CONNECTION_COUNT ? 'healthy' : 'warning',
                value: this.metrics.business.activeUsers,
                unit: 'connections',
                threshold: this.config.thresholds.CONNECTION_COUNT
            };
            
            // Determine overall status
            const statuses = Object.values(healthCheck.checks).map(check => check.status);
            if (statuses.includes('critical')) {
                healthCheck.status = 'critical';
            } else if (statuses.includes('warning')) {
                healthCheck.status = 'warning';
            }
            
            console.log(\`[ProductionMonitor] Health check completed - Status: \${healthCheck.status}\`);
            
            // Store health check result
            this.lastHealthCheck = healthCheck;
            
            // Generate alerts for critical issues
            if (healthCheck.status === 'critical') {
                this.generateAlert('critical', 'System health check failed', healthCheck);
            }
            
        } catch (error) {
            console.error('[ProductionMonitor] Health check failed:', error);
            healthCheck.status = 'critical';
            healthCheck.error = error.message;
        }
        
        return healthCheck;
    }
    
    /**
     * Track request start
     */
    trackRequestStart(requestId) {
        this.requestTimes.set(requestId, performance.now());
    }
    
    /**
     * Track request completion
     */
    trackRequestEnd(requestId, success = true) {
        const startTime = this.requestTimes.get(requestId);
        if (startTime) {
            const duration = performance.now() - startTime;
            this.requestTimes.delete(requestId);
            
            // Record response time
            this.metrics.system.responseTime.push({
                timestamp: Date.now(),
                duration,
                success
            });
            
            // Record request
            this.metrics.system.requests.push({
                timestamp: Date.now(),
                duration,
                success
            });
            
            if (!success) {
                this.recordError('request_failed');
            }
            
            return duration;
        }
    }
    
    /**
     * Record an error
     */
    recordError(errorType, details = null) {
        const now = Date.now();
        
        this.metrics.system.errors.push({
            timestamp: now,
            type: errorType,
            details
        });
        
        // Track error counts
        const hourKey = Math.floor(now / 3600000); // Hour bucket
        if (!this.errorCounts.has(hourKey)) {
            this.errorCounts.set(hourKey, new Map());
        }
        
        const hourErrors = this.errorCounts.get(hourKey);
        hourErrors.set(errorType, (hourErrors.get(errorType) || 0) + 1);
        
        console.log(\`[ProductionMonitor] Error recorded: \${errorType}\`);
    }
    
    /**
     * Update business metrics
     */
    updateBusinessMetrics(metrics) {
        Object.assign(this.metrics.business, metrics);
        
        // Record connection count
        this.metrics.system.connections.push({
            timestamp: Date.now(),
            count: this.metrics.business.activeUsers
        });
    }
    
    /**
     * Get average response time for recent requests
     */
    getAverageResponseTime() {
        const now = Date.now();
        const recentRequests = this.metrics.system.responseTime
            .filter(req => now - req.timestamp < 300000) // Last 5 minutes
            .map(req => req.duration);
        
        if (recentRequests.length === 0) return 0;
        
        return recentRequests.reduce((sum, time) => sum + time, 0) / recentRequests.length;
    }
    
    /**
     * Get error rate percentage
     */
    getErrorRate() {
        const now = Date.now();
        const recentRequests = this.metrics.system.requests
            .filter(req => now - req.timestamp < 300000); // Last 5 minutes
        
        if (recentRequests.length === 0) return 0;
        
        const errors = recentRequests.filter(req => !req.success).length;
        return (errors / recentRequests.length) * 100;
    }
    
    /**
     * Check alert thresholds
     */
    checkAlertThresholds() {
        const now = Date.now();
        
        // Memory threshold
        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
        if (currentMemory > this.config.thresholds.MEMORY_USAGE_MB) {
            this.generateAlert('warning', \`High memory usage: \${currentMemory.toFixed(2)}MB\`, {
                value: currentMemory,
                threshold: this.config.thresholds.MEMORY_USAGE_MB
            });
        }
        
        // Response time threshold
        const avgResponseTime = this.getAverageResponseTime();
        if (avgResponseTime > this.config.thresholds.RESPONSE_TIME_MS) {
            this.generateAlert('warning', \`High response time: \${avgResponseTime.toFixed(2)}ms\`, {
                value: avgResponseTime,
                threshold: this.config.thresholds.RESPONSE_TIME_MS
            });
        }
        
        // Error rate threshold
        const errorRate = this.getErrorRate();
        if (errorRate > this.config.thresholds.ERROR_RATE_PERCENT) {
            this.generateAlert('critical', \`High error rate: \${errorRate.toFixed(2)}%\`, {
                value: errorRate,
                threshold: this.config.thresholds.ERROR_RATE_PERCENT
            });
        }
    }
    
    /**
     * Generate an alert
     */
    generateAlert(severity, message, data = null) {
        const alert = {
            timestamp: Date.now(),
            severity,
            message,
            data
        };
        
        this.metrics.alerts.push(alert);
        
        console.log(\`[ProductionMonitor] ALERT [\${severity.toUpperCase()}]: \${message}\`);
        
        // In production, send to monitoring service (Slack, email, PagerDuty, etc.)
        this.sendAlertNotification(alert);
    }
    
    /**
     * Send alert notification (implement for your monitoring setup)
     */
    sendAlertNotification(alert) {
        // Placeholder for external monitoring integration
        // e.g., Slack webhook, email, PagerDuty, etc.
        console.log(\`[ProductionMonitor] Alert notification sent: \${alert.message}\`);
    }
    
    /**
     * Clean up old metrics to prevent memory leaks
     */
    cleanupOldMetrics() {
        const now = Date.now();
        const retentionMs = this.config.retentionHours * 3600000;
        
        // Clean system metrics
        ['memory', 'cpu', 'connections', 'responseTime', 'errors', 'requests'].forEach(metric => {
            this.metrics.system[metric] = this.metrics.system[metric]
                .filter(item => now - item.timestamp < retentionMs);
        });
        
        // Clean alerts (keep longer - 7 days)
        this.metrics.alerts = this.metrics.alerts
            .filter(alert => now - alert.timestamp < 7 * 24 * 3600000);
        
        // Clean error counts
        const currentHour = Math.floor(now / 3600000);
        const oldHours = Array.from(this.errorCounts.keys())
            .filter(hour => currentHour - hour > this.config.retentionHours);
        
        oldHours.forEach(hour => this.errorCounts.delete(hour));
        
        console.log(\`[ProductionMonitor] Cleaned old metrics. Retention: \${this.config.retentionHours}h\`);
    }
    
    /**
     * Get comprehensive metrics report
     */
    getMetricsReport() {
        const now = Date.now();
        const recentMemory = this.metrics.system.memory.slice(-1)[0];
        const recentConnections = this.metrics.system.connections.slice(-1)[0];
        
        return {
            timestamp: now,
            uptime: this.metrics.system.uptime,
            system: {
                memory: {
                    current: recentMemory ? (recentMemory.heapUsed / 1024 / 1024).toFixed(2) + 'MB' : 'N/A',
                    total: recentMemory ? (recentMemory.heapTotal / 1024 / 1024).toFixed(2) + 'MB' : 'N/A'
                },
                responseTime: {
                    average: this.getAverageResponseTime().toFixed(2) + 'ms',
                    threshold: this.config.thresholds.RESPONSE_TIME_MS + 'ms'
                },
                errorRate: {
                    current: this.getErrorRate().toFixed(2) + '%',
                    threshold: this.config.thresholds.ERROR_RATE_PERCENT + '%'
                },
                connections: {
                    current: recentConnections ? recentConnections.count : 0,
                    threshold: this.config.thresholds.CONNECTION_COUNT
                }
            },
            business: this.metrics.business,
            health: this.lastHealthCheck,
            alerts: {
                total: this.metrics.alerts.length,
                recent: this.metrics.alerts.filter(alert => now - alert.timestamp < 3600000).length
            }
        };
    }
    
    /**
     * Export metrics for external monitoring systems
     */
    exportMetrics(format = 'json') {
        const report = this.getMetricsReport();
        
        switch (format) {
            case 'prometheus':
                return this.exportPrometheusMetrics();
            case 'json':
            default:
                return JSON.stringify(report, null, 2);
        }
    }
    
    /**
     * Export metrics in Prometheus format
     */
    exportPrometheusMetrics() {
        const report = this.getMetricsReport();
        let output = '';
        
        // System metrics
        output += \`# HELP bot_manager_memory_usage_bytes Memory usage in bytes\\n\`;
        output += \`# TYPE bot_manager_memory_usage_bytes gauge\\n\`;
        output += \`bot_manager_memory_usage_bytes \${process.memoryUsage().heapUsed}\\n\`;
        
        output += \`# HELP bot_manager_uptime_seconds Uptime in seconds\\n\`;
        output += \`# TYPE bot_manager_uptime_seconds counter\\n\`;
        output += \`bot_manager_uptime_seconds \${this.metrics.system.uptime / 1000}\\n\`;
        
        output += \`# HELP bot_manager_active_connections Active WebSocket connections\\n\`;
        output += \`# TYPE bot_manager_active_connections gauge\\n\`;
        output += \`bot_manager_active_connections \${this.metrics.business.activeUsers}\\n\`;
        
        output += \`# HELP bot_manager_response_time_ms Average response time in milliseconds\\n\`;
        output += \`# TYPE bot_manager_response_time_ms gauge\\n\`;
        output += \`bot_manager_response_time_ms \${this.getAverageResponseTime()}\\n\`;
        
        output += \`# HELP bot_manager_error_rate Percentage of errors\\n\`;
        output += \`# TYPE bot_manager_error_rate gauge\\n\`;
        output += \`bot_manager_error_rate \${this.getErrorRate()}\\n\`;
        
        return output;
    }
    
    /**
     * Graceful shutdown
     */
    shutdown() {
        clearInterval(this.metricsInterval);
        clearInterval(this.healthInterval);
        clearInterval(this.cleanupInterval);
        
        console.log('[ProductionMonitor] Monitoring system shutdown');
    }
}

module.exports = ProductionMonitor;
`;

// Write the monitoring system
fs.writeFileSync(
    path.join(__dirname, '../websocket/ProductionMonitor.js'),
    MONITORING_SYSTEM_CODE
);

console.log('✅ ProductionMonitor.js created');

// Health check endpoint code
const HEALTH_ENDPOINT_CODE = `
// Add this to your Express app (index.js)

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        const healthCheck = await webSocketManager.productionMonitor.performHealthCheck();
        
        const statusCode = healthCheck.status === 'healthy' ? 200 :
                          healthCheck.status === 'warning' ? 200 : 503;
        
        res.status(statusCode).json({
            status: healthCheck.status,
            timestamp: healthCheck.timestamp,
            uptime: webSocketManager.productionMonitor.metrics.system.uptime,
            checks: healthCheck.checks,
            version: process.env.npm_package_version || '1.0.0'
        });
    } catch (error) {
        res.status(503).json({
            status: 'error',
            message: error.message,
            timestamp: Date.now()
        });
    }
});

// Metrics endpoint
app.get('/metrics', (req, res) => {
    const format = req.query.format || 'json';
    const metrics = webSocketManager.productionMonitor.exportMetrics(format);
    
    if (format === 'prometheus') {
        res.set('Content-Type', 'text/plain');
        res.send(metrics);
    } else {
        res.json(JSON.parse(metrics));
    }
});

// Ready endpoint for load balancer
app.get('/ready', (req, res) => {
    const isReady = webSocketManager && webSocketManager.productionMonitor;
    res.status(isReady ? 200 : 503).json({
        ready: isReady,
        timestamp: Date.now()
    });
});
`;

console.log('📝 Health check endpoints code generated');
console.log('   Add /health, /metrics, /ready endpoints to Express app');

console.log('\n✅ Production monitoring implementation completed');
console.log('📊 Features implemented:');
console.log('   • Real-time system metrics collection');
console.log('   • Comprehensive health checks');
console.log('   • Performance monitoring with thresholds');
console.log('   • Alert generation and notifications');
console.log('   • Metrics export (JSON/Prometheus)');
console.log('   • Memory leak prevention');
console.log('   • HTTP health endpoints');

console.log('\n🚀 Integration steps:');
console.log('   1. Add ProductionMonitor to WebSocketManager');
console.log('   2. Integrate health endpoints in Express app');
console.log('   3. Configure alert notifications');
console.log('   4. Set up external monitoring (optional)');

module.exports = {
    ProductionMonitor: MONITORING_SYSTEM_CODE,
    HealthEndpoints: HEALTH_ENDPOINT_CODE
};

#!/usr/bin/env python3
"""
Test script to verify the enhanced delete endpoint functionality
"""

import requests
import json
import sys
import os

def test_delete_endpoint():
    """Test the enhanced delete endpoint"""
    
    # Bot manager URL
    BASE_URL = "http://localhost:3001"
    
    # Test instance (we'll use mybot1 which doesn't have a running container)
    INSTANCE_ID = "mybot1"
    
    # You'll need to provide a valid JWT token
    # For testing, you can get this from your browser dev tools or auth system
    TOKEN = input("Enter your JWT token: ").strip()
    
    if not TOKEN:
        print("❌ No token provided")
        return False
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    print(f"🧪 Testing comprehensive delete for instance: {INSTANCE_ID}")
    print(f"📡 Bot Manager URL: {BASE_URL}")
    
    try:
        # Make delete request
        response = requests.delete(f"{BASE_URL}/{INSTANCE_ID}", headers=headers)
        
        print(f"📨 Response Status: {response.status_code}")
        print(f"📝 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Delete successful!")
            print(f"📋 Response: {json.dumps(result, indent=2)}")
            
            # Verify details
            details = result.get('details', {})
            print("\n🔍 Verification Summary:")
            print(f"  Container Removed: {'✅' if details.get('containerRemoved') else '❌'}")
            print(f"  Turso DB Removed: {'✅' if details.get('tursoDbRemoved') else '❌'}")
            print(f"  Directory Removed: {'✅' if details.get('directoryRemoved') else '❌'}")
            print(f"  Metadata Cleaned: {'✅' if details.get('metadataCleaned') else '❌'}")
            print(f"  Turso DB Name: {details.get('tursoDbName', 'N/A')}")
            
            return True
            
        elif response.status_code == 401:
            print("❌ Authentication failed - check your token")
            return False
            
        elif response.status_code == 404:
            print("❌ Instance not found")
            return False
            
        else:
            print(f"❌ Delete failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📋 Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📋 Raw response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the bot manager running on port 3001?")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Enhanced Delete Endpoint Test")
    print("=" * 50)
    
    success = test_delete_endpoint()
    
    if success:
        print("\n🎉 Test completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Test failed!")
        sys.exit(1)

#!/usr/bin/env node

const WebSocket = require('ws');

const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.vqPQ7aiEFRi7_YpZG61yk_Y2z0fD3bquWvnjd-n_TOo';

console.log('🚀 Testing Phase 4 Features...\n');

const ws = new WebSocket(`ws://127.0.0.1:3001/ws?token=${TEST_TOKEN}`);

let testResults = [];
let currentTest = 0;

const tests = [
    {
        name: 'Portfolio Summary',
        message: { type: 'get_portfolio_summary', includeTimeSeries: true }
    },
    {
        name: 'System Health Check',
        message: { type: 'get_system_health' }
    },
    {
        name: 'Advanced Chart Data',
        message: {
            type: 'get_chart_data',
            chartType: 'portfolio_value',
            parameters: { period: '7d', includeMetrics: true }
        }
    },
    {
        name: 'Bot Status Check',
        message: {
            type: 'get_bot_status',
            botId: 'tessa-bot1'
        }
    }
];

ws.on('open', function () {
    console.log('✅ WebSocket connected successfully');
    runNextTest();
});

ws.on('message', function (data) {
    try {
        const response = JSON.parse(data);
        console.log(`📨 Received: ${response.type}`);

        if (response.type === 'connection_established') {
            console.log('🔗 Connection established');
            return;
        }

        // Record test result
        if (currentTest <= tests.length) {
            const testName = tests[currentTest - 1]?.name || 'Unknown';
            const success = response.type !== 'error';

            testResults.push({
                test: testName,
                success: success,
                response: response.type,
                error: response.error || response.data?.message
            });

            if (success) {
                console.log(`✅ ${testName}: PASSED`);
            } else {
                console.log(`❌ ${testName}: FAILED - ${response.error || response.data?.message}`);
            }
        }

        // Run next test after a short delay
        setTimeout(() => {
            runNextTest();
        }, 1000);

    } catch (error) {
        console.error('❌ Error parsing message:', error);
    }
});

ws.on('error', function (error) {
    console.error('❌ WebSocket error:', error);
});

ws.on('close', function () {
    console.log('\n🔗 WebSocket connection closed');

    // Print test summary
    console.log('\n📊 Test Results Summary:');
    console.log('=======================');

    let passed = 0;
    let failed = 0;

    testResults.forEach(result => {
        const status = result.success ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} - ${result.test} (${result.response})`);
        if (result.error) {
            console.log(`     Error: ${result.error}`);
        }

        if (result.success) passed++;
        else failed++;
    });

    console.log(`\n📈 Results: ${passed} passed, ${failed} failed`);

    if (failed === 0) {
        console.log('🎉 All Phase 4 features working correctly!');
    } else {
        console.log('⚠️  Some features need attention');
    }

    process.exit(failed > 0 ? 1 : 0);
});

function runNextTest() {
    if (currentTest >= tests.length) {
        ws.close();
        return;
    }

    const test = tests[currentTest];
    console.log(`\n🧪 Running test ${currentTest + 1}/${tests.length}: ${test.name}`);

    ws.send(JSON.stringify(test.message));
    currentTest++;
}

// Timeout after 30 seconds
setTimeout(() => {
    console.log('\n⏰ Test timeout reached');
    ws.close();
}, 30000);

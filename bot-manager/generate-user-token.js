const jwt = require('jsonwebtoken');
require('dotenv').config();

const userSpecificPayload = {
  id: 'Js1Gaz4sMPPiDNgFbmAgDFLe4je2',
  uid: 'Js1Gaz4sMPPiDNgFbmAgDFLe4je2',
  email: '<EMAIL>',
  role: 'user',
  custom_jwt: true,
  token_type: 'bot_manager_jwt'
};

const token = jwt.sign(userSpecificPayload, process.env.JWT_SECRET, {
  expiresIn: '24h',
  audience: 'freqtrade-users',
  issuer: 'bot-manager'
});

console.log('================================================================================');
console.log('User-Specific JWT Token Generated');
console.log('================================================================================');
console.log('Token:', token);
console.log('');
console.log('User Info:');
console.log('- ID:', userSpecificPayload.id);
console.log('- UID:', userSpecificPayload.uid);
console.log('- Email:', userSpecificPayload.email);
console.log('- Role:', userSpecificPayload.role);
console.log('');
console.log('This token will be valid for portfolio aggregation testing:');
console.log('================================================================================');
console.log(token);
console.log('================================================================================');

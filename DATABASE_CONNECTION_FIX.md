# SQLite Database Connection Pool Timeout Fix

## Problem
FreqTrade bots were experiencing `sqlalchemy.exc.TimeoutError: QueuePool limit of size 5 overflow 10 reached` errors, causing all bots to crash after some time.

## Root Cause
The default SQLAlchemy connection pool settings for SQLite were too restrictive:
- Default pool_size: 5 connections
- Default max_overflow: 10 connections  
- No connection timeout handling
- No proper connection recycling

SQLite databases were getting locked by stale connections, preventing new connections from being established.

## Solution Applied

### 1. Database URL Optimization
Updated all bot configurations to use optimized SQLite connection strings:
```json
"db_url": "sqlite:///user_data/tradesv3.sqlite?timeout=60&isolation_level=None"
```

### 2. Database Connection Settings
Added comprehensive database connection pool settings:
```json
"database_settings": {
  "pool_size": 1,           // Single connection per bot (SQLite best practice)
  "max_overflow": 0,        // No overflow connections (prevents pool exhaustion)
  "pool_timeout": 60,       // 60-second timeout for getting connections
  "pool_recycle": 3600,     // Recycle connections every hour
  "pool_pre_ping": true,    // Test connections before use
  "connect_args": {
    "timeout": 60,          // SQLite-specific timeout
    "check_same_thread": false  // Allow multi-threaded access
  }
}
```

### 3. Process Throttling
Increased processing intervals to reduce database load:
```json
"internals": {
  "process_throttle_secs": 60,  // Increased from 30 to 60 seconds
  "heartbeat_interval": 300,    // 5-minute heartbeat intervals
  "sd_notify": false           // Disable systemd notifications that can cause locks
}
```

## Implementation

### Files Modified
1. `/root/Crypto-Pilot-Freqtrade/bot-manager/index.js` - Updated bot provisioning
2. `/root/Crypto-Pilot-Freqtrade/bot-manager/fix-db-connections.js` - Retroactive fix script

### Commands Run
```bash
# Fix all existing bot configurations
cd /root/Crypto-Pilot-Freqtrade/bot-manager
node fix-db-connections.js

# Restart all bot containers
docker start freqtrade-anshjarvis2003-bot-1 freqtrade-anshjarvis2003-bot-2 freqtrade-anshjarvis2003-bot-3 freqtrade-anshjarvis2003-bot-4
```

## Results
- ✅ All 4 bots successfully restarted
- ✅ No more SQLAlchemy timeout errors
- ✅ Bots processing trades normally
- ✅ Reduced database contention through connection pooling
- ✅ Future bot provisions will automatically include these optimizations

## Prevention
- New bot provisions automatically include optimized database settings
- Connection pool monitoring prevents future timeout issues
- Proper SQLite connection management ensures stability

## Monitoring
Check bot logs for database-related errors:
```bash
docker logs --tail 20 freqtrade-anshjarvis2003-bot-1 | grep -i "timeout\|pool\|database"
```

## Date Fixed
July 19, 2025

# Portfolio API Documentation

## Overview

The Portfolio API provides comprehensive access to real-time portfolio data, historical snapshots, and chart data for different time intervals. It supports both REST API endpoints and real-time SSE (Server-Sent Events) streaming.

**Base URL**: `https://freqtrade.crypto-pilot.dev/`

---

## 🔐 Authentication

All endpoints require JWT authentication via the `Authorization` header:

```http
Authorization: Bearer <your-jwt-token>
```

### Generating Test Tokens (IGNORE THIS)

```bash
# Generate a test token (development only)
cd /root/Crypto-Pilot-Freqtrade/bot-manager
node generate-test-token.js
```

---

## 📊 Chart Data Endpoints

### 1. Get Chart Data for Specific Interval

**Endpoint**: `GET /api/charts/portfolio/:interval`

**Supported Intervals**:
- `1h` - Last 1 hour (5-minute windows)
- `24h` - Last 24 hours (30-minute windows)  
- `7d` - Last 7 days (4-hour windows)
- `30d` - Last 30 days (24-hour windows)

**Request Example**:
```javascript
fetch('https://freqtrade.crypto-pilot.dev/api/charts/portfolio/24h', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

**Response Format**:
```json
{
  "success": true,
  "interval": "24h",
  "data": [
    {
      "timestamp": 1757362500000,
      "portfolioValue": 19994.82,
      "totalPnL": -5.18,
      "activeBots": 2,
      "botCount": 2,
      "snapshotCount": 51
    }
  ],
  "metadata": {
    "totalSnapshots": 68,
    "dataPoints": 2,
    "firstSnapshot": 1757290286554,
    "lastSnapshot": 1757362799840,
    "timeRange": {
      "start": 1757362500000,
      "end": 1757362500000
    }
  }
}
```

### 2. Get Chart Data for All Intervals

**Endpoint**: `GET /api/charts/portfolio`

**Request Example**:
```javascript
const getAllChartData = async (token) => {
  const response = await fetch('https://freqtrade.crypto-pilot.dev/api/charts/portfolio', {
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
  return await response.json();
};
```

**Response Format**:
```json
{
  "success": true,
  "intervals": {
    "1h": {
      "data": [...],
      "dataPoints": 2,
      "timeRange": {
        "start": 1757362500000,
        "end": 1757362800000
      }
    },
    "24h": { /* ... */ },
    "7d": { /* ... */ },
    "30d": { /* ... */ }
  },
  "metadata": {
    "totalSnapshots": 68,
    "firstSnapshot": 1757290286554,
    "lastSnapshot": 1757362799840
  }
}
```

---

## � Positions & Trades Endpoints

### Get All Positions Across All Bots

**Endpoint**: `GET /api/portfolio/positions`

**Query Parameters**:
- `status` (optional): Filter by position status
  - `open` - Only open positions
  - `closed` - Only closed positions  
  - `all` - All positions (default)

**Request Example**:
```javascript
const getAllPositions = async (token, status = 'all') => {
  const response = await fetch(
    `http://localhost:3001/api/portfolio/positions?status=${status}`,
    {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    }
  );
  return await response.json();
};

// Get only open positions
const openPositions = await getAllPositions(token, 'open');

// Get all positions
const allPositions = await getAllPositions(token, 'all');
```

**Response Format**:
```json
{
  "success": true,
  "positions": [
    {
      "botId": "anshjarvis2003-bot-1",
      "tradeId": 13,
      "pair": "SOL/USD",
      "baseCurrency": "SOL",
      "quoteCurrency": "USD",
      "exchange": "kraken",
      "strategy": "EmaRsiStrategy",
      "isOpen": true,
      "isShort": false,
      "amount": 0.46728971,
      "stakeAmount": 99.99999794,
      "leverage": 1,
      "tradingMode": "spot",
      "openDate": "2025-09-08 13:45:12",
      "openTimestamp": 1757339112082,
      "openRate": 214,
      "currentRate": 215.11,
      "profitAbs": 0.01739486,
      "profitPct": 0.02,
      "profitRatio": 0.00017351,
      "totalProfitAbs": 0.01739486,
      "totalProfitRatio": 0.00017351,
      "stopLossAbs": 192.6,
      "stopLossRatio": -0.1,
      "stopLossPct": -10,
      "minRate": 213.05,
      "maxRate": 217,
      "hasOpenOrders": false,
      "orderCount": 1,
      "durationMinutes": 832,
      "profitUsd": 0.01739486,
      "feeOpen": 0.0025,
      "feeOpenCost": 0.24999999485,
      "timeframe": 15,
      "enterTag": ""
    }
  ],
  "summary": {
    "totalPositions": 4,
    "openPositions": 4,
    "closedPositions": 0,
    "totalUnrealizedPnL": -4.98,
    "totalRealizedPnL": 0,
    "totalPnL": -4.98,
    "uniquePairs": 2,
    "uniqueBots": 2,
    "totalStakeAmount": 399.99,
    "averagePositionSize": 99.99,
    "profitablePositions": 1,
    "losingPositions": 3,
    "winRate": 25
  },
  "metadata": {
    "timestamp": 1757362894659,
    "userId": "Js1Gaz4sMPPiDNgFbmAgDFLe4je2",
    "filter": "all",
    "source": "live_data"
  }
}
```

### Get Positions for Specific Bot

**Endpoint**: `GET /api/portfolio/positions/:botId`

**Query Parameters**:
- `status` (optional): Same as above (`open`, `closed`, `all`)

**Request Example**:
```javascript
const getBotPositions = async (token, botId, status = 'all') => {
  const response = await fetch(
    `http://localhost:3001/api/portfolio/positions/${botId}?status=${status}`,
    {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    }
  );
  return await response.json();
};

// Get all positions for a specific bot
const botPositions = await getBotPositions(token, 'anshjarvis2003-bot-1');

// Get only open positions for a specific bot
const openBotPositions = await getBotPositions(token, 'anshjarvis2003-bot-1', 'open');
```

**Response Format**:
```json
{
  "success": true,
  "botId": "anshjarvis2003-bot-1",
  "positions": [
    {
      "tradeId": 13,
      "pair": "SOL/USD",
      "baseCurrency": "SOL",
      "quoteCurrency": "USD",
      "exchange": "kraken",
      "strategy": "EmaRsiStrategy",
      "isOpen": true,
      "isShort": false,
      "amount": 0.46728971,
      "stakeAmount": 99.99999794,
      "openDate": "2025-09-08 13:45:12",
      "openTimestamp": 1757339112082,
      "openRate": 214,
      "currentRate": 215.11,
      "profitAbs": 0.01739486,
      "profitPct": 0.02,
      "totalProfitAbs": 0.01739486,
      "stopLossAbs": 192.6,
      "hasOpenOrders": false,
      "orders": [...]
    }
  ],
  "summary": {
    "totalPositions": 2,
    "openPositions": 2,
    "closedPositions": 0,
    "totalPnL": -0.66
  }
}
```

---

## �📈 Historical Data Endpoint

### Get Raw Portfolio History

**Endpoint**: `GET /api/portfolio/history`

**Query Parameters**:
- `limit` (optional): Number of snapshots to return (default: 1000)
- `offset` (optional): Number of snapshots to skip (default: 0)

**Request Example**:
```javascript
const getPortfolioHistory = async (token, limit = 100, offset = 0) => {
  const response = await fetch(
    `https://freqtrade.crypto-pilot.dev/api/portfolio/history?limit=${limit}&offset=${offset}`,
    {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    }
  );
  return await response.json();
};
```

**Response Format**:
```json
{
  "success": true,
  "snapshots": [
    {
      "timestamp": 1757362879886,
      "portfolioValue": 19994.821857685907,
      "totalBalance": 19994.821857685907,
      "totalPnL": -5.178142314091019,
      "activeBots": 2,
      "botCount": 2,
      "bots": [...]
    }
  ],
  "pagination": {
    "total": 70,
    "limit": 100,
    "offset": 0,
    "returned": 70
  },
  "metadata": {
    "firstSnapshot": 1757290286554,
    "lastSnapshot": 1757362879886,
    "totalSnapshots": 70
  }
}
```

---

## 🔴 Real-Time SSE Streaming

### Connect to Portfolio Stream

**Endpoint**: `GET /api/stream?token=<jwt-token>`

**Note**: EventSource cannot set Authorization headers, so the token must be in the query parameter.

**JavaScript Example**:
```javascript
class PortfolioStreamer {
  constructor(token) {
    this.token = token;
    this.eventSource = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const url = `https://freqtrade.crypto-pilot.dev/api/stream?token=${encodeURIComponent(this.token)}`;
    this.eventSource = new EventSource(url);

    this.eventSource.onopen = () => {
      console.log('✅ Portfolio stream connected');
      this.reconnectAttempts = 0;
    };

    this.eventSource.onerror = (error) => {
      console.error('❌ Portfolio stream error:', error);
      this.handleReconnect();
    };

    // Listen for connection confirmation
    this.eventSource.addEventListener('connected', (event) => {
      const data = JSON.parse(event.data);
      console.log('Stream connected at:', new Date(data.timestamp));
    });

    // Listen for portfolio updates
    this.eventSource.addEventListener('portfolio', (event) => {
      const portfolioData = JSON.parse(event.data);
      this.onPortfolioUpdate(portfolioData);
    });
  }

  onPortfolioUpdate(data) {
    // Handle portfolio update
    console.log('Portfolio Update:', {
      value: data.portfolioValue,
      pnl: data.totalPnL,
      bots: `${data.activeBots}/${data.botCount}`,
      timestamp: new Date(data.timestamp)
    });

    // Update your UI here
    this.updateUI(data);
  }

  updateUI(data) {
    // Example UI updates
    document.getElementById('portfolio-value').textContent = 
      `$${data.portfolioValue.toFixed(2)}`;
    document.getElementById('total-pnl').textContent = 
      `$${data.totalPnL.toFixed(2)}`;
    document.getElementById('active-bots').textContent = 
      `${data.activeBots}/${data.botCount}`;
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
      
      setTimeout(() => {
        console.log(`Reconnecting... Attempt ${this.reconnectAttempts}`);
        this.connect();
      }, delay);
    }
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}

// Usage
const streamer = new PortfolioStreamer('your-jwt-token');
streamer.connect();
```

**SSE Event Types**:
- `connected` - Connection established
- `portfolio` - Portfolio data update (every 5 seconds)

---

## 📊 Frontend Integration Examples

### 1. Chart.js Integration

```javascript
class PortfolioChart {
  constructor(canvasId, token) {
    this.ctx = document.getElementById(canvasId).getContext('2d');
    this.token = token;
    this.chart = null;
    this.currentInterval = '24h';
  }

  async init() {
    await this.loadChartData();
    this.createChart();
  }

  async loadChartData(interval = '24h') {
    this.currentInterval = interval;
    
    const response = await fetch(
      `https://freqtrade.crypto-pilot.dev/api/charts/portfolio/${interval}`,
      {
        headers: { 'Authorization': 'Bearer ' + this.token }
      }
    );
    
    const result = await response.json();
    if (result.success) {
      this.updateChart(result.data);
    }
  }

  createChart() {
    this.chart = new Chart(this.ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: 'Portfolio Value',
            data: [],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            yAxisID: 'y'
          },
          {
            label: 'P&L',
            data: [],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.1,
            yAxisID: 'y1'
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          x: {
            type: 'time',
            time: {
              unit: this.getTimeUnit()
            }
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: 'Portfolio Value ($)'
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: 'P&L ($)'
            }
          }
        }
      }
    });
  }

  updateChart(data) {
    const labels = data.map(point => new Date(point.timestamp));
    const portfolioValues = data.map(point => point.portfolioValue);
    const pnlValues = data.map(point => point.totalPnL);

    this.chart.data.labels = labels;
    this.chart.data.datasets[0].data = portfolioValues;
    this.chart.data.datasets[1].data = pnlValues;
    this.chart.update();
  }

  getTimeUnit() {
    switch (this.currentInterval) {
      case '1h': return 'minute';
      case '24h': return 'hour';
      case '7d': return 'day';
      case '30d': return 'day';
      default: return 'hour';
    }
  }

  // Change time interval
  async changeInterval(interval) {
    await this.loadChartData(interval);
  }
}

// Usage
const chart = new PortfolioChart('portfolio-chart', 'your-jwt-token');
chart.init();

// Add interval buttons
document.getElementById('btn-1h').onclick = () => chart.changeInterval('1h');
document.getElementById('btn-24h').onclick = () => chart.changeInterval('24h');
document.getElementById('btn-7d').onclick = () => chart.changeInterval('7d');
document.getElementById('btn-30d').onclick = () => chart.changeInterval('30d');
```

### 2. React Hook Example

```javascript
import { useState, useEffect, useRef } from 'react';

const usePortfolioData = (token) => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [chartData, setChartData] = useState({});
  const [isConnected, setIsConnected] = useState(false);
  const eventSourceRef = useRef(null);

  // Real-time streaming
  useEffect(() => {
    if (!token) return;

    const connectStream = () => {
      const url = `https://freqtrade.crypto-pilot.dev/api/stream?token=${encodeURIComponent(token)}`;
      eventSourceRef.current = new EventSource(url);

      eventSourceRef.current.onopen = () => {
        setIsConnected(true);
      };

      eventSourceRef.current.onerror = () => {
        setIsConnected(false);
      };

      eventSourceRef.current.addEventListener('portfolio', (event) => {
        const data = JSON.parse(event.data);
        setPortfolioData(data);
      });
    };

    connectStream();

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [token]);

  // Load chart data
  const loadChartData = async (interval = 'all') => {
    try {
      const endpoint = interval === 'all' 
        ? '/api/charts/portfolio'
        : `/api/charts/portfolio/${interval}`;
      
      const response = await fetch(`https://freqtrade.crypto-pilot.dev${endpoint}`, {
        headers: { 'Authorization': 'Bearer ' + token }
      });
      
      const result = await response.json();
      if (result.success) {
        setChartData(interval === 'all' ? result.intervals : { [interval]: result });
      }
    } catch (error) {
      console.error('Failed to load chart data:', error);
    }
  };

  return {
    portfolioData,
    chartData,
    isConnected,
    loadChartData
  };
};

// Component usage
const PortfolioDashboard = ({ token }) => {
  const { portfolioData, chartData, isConnected, loadChartData } = usePortfolioData(token);

  useEffect(() => {
    loadChartData('all'); // Load all intervals on mount
  }, []);

  return (
    <div>
      <div className={`status ${isConnected ? 'connected' : 'disconnected'}`}>
        {isConnected ? '🟢 Live' : '🔴 Disconnected'}
      </div>
      
      {portfolioData && (
        <div className="portfolio-summary">
          <div>Value: ${portfolioData.portfolioValue.toFixed(2)}</div>
          <div>P&L: ${portfolioData.totalPnL.toFixed(2)}</div>
          <div>Bots: {portfolioData.activeBots}/{portfolioData.botCount}</div>
        </div>
      )}
      
      <div className="chart-container">
        {/* Your chart component here */}
      </div>
    </div>
  );
};
```

### 3. Vue.js Composition API Example

```javascript
import { ref, onMounted, onUnmounted } from 'vue';

export function usePortfolioStream(token) {
  const portfolioData = ref(null);
  const isConnected = ref(false);
  let eventSource = null;

  const connect = () => {
    if (!token.value) return;

    const url = `https://freqtrade.crypto-pilot.dev/api/stream?token=${encodeURIComponent(token.value)}`;
    eventSource = new EventSource(url);

    eventSource.onopen = () => {
      isConnected.value = true;
    };

    eventSource.onerror = () => {
      isConnected.value = false;
    };

    eventSource.addEventListener('portfolio', (event) => {
      portfolioData.value = JSON.parse(event.data);
    });
  };

  const disconnect = () => {
    if (eventSource) {
      eventSource.close();
      eventSource = null;
      isConnected.value = false;
    }
  };

  onMounted(connect);
  onUnmounted(disconnect);

  return {
    portfolioData,
    isConnected,
    connect,
    disconnect
  };
}
```

---

## 🏥 Health & Status Endpoints

### API Health Check

**Endpoint**: `GET /api/health`

```javascript
fetch('https://freqtrade.crypto-pilot.dev/api/health')
  .then(response => response.json())
  .then(data => console.log(data));
```

**Response**:
```json
{
  "status": "ok",
  "timestamp": 1757362894659,
  "uptime": 12345
}
```

---

## 🚨 Error Handling

### Common Error Responses

```json
{
  "success": false,
  "message": "Authentication failed",
  "error": "Invalid token"
}
```

### Error Codes

- `401` - Authentication failed (invalid/expired token)
- `400` - Bad request (invalid interval, missing parameters)
- `404` - Resource not found
- `500` - Internal server error

### Frontend Error Handling

```javascript
const handleApiError = (response) => {
  if (!response.ok) {
    if (response.status === 401) {
      // Redirect to login or refresh token
      window.location.href = '/login';
    } else if (response.status === 500) {
      // Show error message to user
      showErrorMessage('Server error. Please try again later.');
    }
  }
  return response.json();
};

// Usage
fetch('/api/charts/portfolio/24h', { headers })
  .then(handleApiError)
  .then(data => {
    if (data.success) {
      // Handle success
    } else {
      console.error(data.message);
    }
  });
```

---

## 📱 Complete Dashboard Example

```html
<!DOCTYPE html>
<html>
<head>
    <title>Portfolio Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
</head>
<body>
    <div id="dashboard">
        <div class="status" id="connection-status">🔴 Disconnected</div>
        
        <div class="metrics">
            <div>Portfolio: $<span id="portfolio-value">0.00</span></div>
            <div>P&L: $<span id="total-pnl">0.00</span></div>
            <div>Bots: <span id="active-bots">0/0</span></div>
        </div>
        
        <div class="chart-controls">
            <button onclick="loadInterval('1h')">1H</button>
            <button onclick="loadInterval('24h')">24H</button>
            <button onclick="loadInterval('7d')">7D</button>
            <button onclick="loadInterval('30d')">30D</button>
        </div>
        
        <canvas id="portfolio-chart"></canvas>
    </div>

    <script>
        const TOKEN = 'your-jwt-token-here';
        let chart = null;
        let eventSource = null;

        // Initialize dashboard
        async function initDashboard() {
            await loadChart('24h');
            connectStream();
        }

        // Load chart data
        async function loadChart(interval) {
            try {
                const response = await fetch(`https://freqtrade.crypto-pilot.dev/api/charts/portfolio/${interval}`, {
                    headers: { 'Authorization': 'Bearer ' + TOKEN }
                });
                
                const result = await response.json();
                if (result.success) {
                    updateChart(result.data, interval);
                }
            } catch (error) {
                console.error('Failed to load chart:', error);
            }
        }

        // Create/update chart
        function updateChart(data, interval) {
            const ctx = document.getElementById('portfolio-chart').getContext('2d');
            
            if (chart) {
                chart.destroy();
            }

            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(point => new Date(point.timestamp)),
                    datasets: [{
                        label: 'Portfolio Value',
                        data: data.map(point => point.portfolioValue),
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: interval === '1h' ? 'minute' : 'hour'
                            }
                        }
                    }
                }
            });
        }

        // Connect to real-time stream
        function connectStream() {
            const url = `https://freqtrade.crypto-pilot.dev/api/stream?token=${encodeURIComponent(TOKEN)}`;
            eventSource = new EventSource(url);

            eventSource.onopen = () => {
                document.getElementById('connection-status').innerHTML = '🟢 Connected';
            };

            eventSource.onerror = () => {
                document.getElementById('connection-status').innerHTML = '🔴 Disconnected';
            };

            eventSource.addEventListener('portfolio', (event) => {
                const data = JSON.parse(event.data);
                updateMetrics(data);
            });
        }

        // Update live metrics
        function updateMetrics(data) {
            document.getElementById('portfolio-value').textContent = data.portfolioValue.toFixed(2);
            document.getElementById('total-pnl').textContent = data.totalPnL.toFixed(2);
            document.getElementById('active-bots').textContent = `${data.activeBots}/${data.botCount}`;
        }

        // Interval button handler
        function loadInterval(interval) {
            loadChart(interval);
        }

        // Initialize on page load
        window.onload = initDashboard;
    </script>
</body>
</html>
```

---

## 🔧 Development & Testing

### Test the API

```bash
# Test chart endpoint
curl -H "Authorization: Bearer <token>" \
  "https://freqtrade.crypto-pilot.dev/api/charts/portfolio/24h" | jq

# Test streaming (will output continuous data)
curl -N -H "Authorization: Bearer <token>" \
  "https://freqtrade.crypto-pilot.dev/api/stream"

# Test history
curl -H "Authorization: Bearer <token>" \
  "https://freqtrade.crypto-pilot.dev/api/portfolio/history?limit=5" | jq
```

### Generate Test Token

```bash
cd /root/Crypto-Pilot-Freqtrade/bot-manager
node generate-test-token.js
```

---

## 📋 Data Schema Reference

### Portfolio Data Point
```typescript
interface PortfolioSnapshot {
  timestamp: number;           // Unix timestamp in milliseconds
  portfolioValue: number;      // Total portfolio value in USD
  totalBalance: number;        // Same as portfolioValue
  totalPnL: number;           // Profit/Loss vs starting capital
  activeBots: number;         // Number of active bots
  botCount: number;           // Total number of bots
  bots: BotInfo[];           // Detailed bot information
}
```

### Chart Data Point
```typescript
interface ChartDataPoint {
  timestamp: number;          // Window start time
  portfolioValue: number;     // Average portfolio value in window
  totalPnL: number;          // Average P&L in window
  activeBots: number;        // Active bots (from latest snapshot)
  botCount: number;          // Total bots (from latest snapshot)
  snapshotCount: number;     // Number of snapshots in this window
}
```

This documentation provides everything needed to integrate the Portfolio API into your frontend application. The system automatically collects data every 5 seconds and provides it through both REST endpoints and real-time streaming.

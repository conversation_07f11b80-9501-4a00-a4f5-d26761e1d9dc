#!/usr/bin/env node

/**
 * Debug FreqTrade Balance API
 * Check what balance data FreqTrade actually returns
 */

const fetch = require('node-fetch');

async function checkBotBalance(port, botName) {
    try {
        console.log(`\n🔍 Checking ${botName} (port ${port}):`);

        // First, try to ping the bot
        const pingResponse = await fetch(`http://127.0.0.1:${port}/api/v1/ping`, {
            timeout: 3000
        });

        if (!pingResponse.ok) {
            console.log(`❌ Bot not responding: ${pingResponse.status}`);
            return;
        }

        console.log(`✅ <PERSON><PERSON> is responding`);

        // Get balance
        const balanceResponse = await fetch(`http://127.0.0.1:${port}/api/v1/balance`, {
            timeout: 3000
        });

        if (!balanceResponse.ok) {
            console.log(`❌ Balance API error: ${balanceResponse.status}`);
            const errorText = await balanceResponse.text();
            console.log(`Error details: ${errorText}`);
            return;
        }

        const balance = await balanceResponse.json();
        console.log(`📊 Balance data:`, JSON.stringify(balance, null, 2));

        // Show key balance fields
        if (balance.currencies) {
            console.log(`💰 Currency breakdown:`);
            for (const [currency, data] of Object.entries(balance.currencies)) {
                console.log(`   ${currency}: ${data.free || 0} free, ${data.used || 0} used, ${data.total || 0} total`);
            }
        }

        if (balance.total !== undefined) {
            console.log(`💯 Total balance: ${balance.total}`);
        }

        if (balance.symbol !== undefined) {
            console.log(`🔣 Symbol: ${balance.symbol}`);
        }

    } catch (error) {
        console.error(`❌ Error checking ${botName}:`, error.message);
    }
}

async function main() {
    console.log('🧪 FreqTrade Balance API Debug Tool\n');

    const bots = [
        { port: 8100, name: 'anshjarvis2003-bot-1' },
        { port: 8101, name: 'anshjarvis2003-bot-2' },
        { port: 8102, name: 'anshjarvis2003-bot-3' },
        { port: 8103, name: 'anshjarvis2003-bot-4' }
    ];

    for (const bot of bots) {
        await checkBotBalance(bot.port, bot.name);
    }

    console.log('\n🎯 Summary:');
    console.log('The balance API should show the breakdown of currencies.');
    console.log('In dry run mode, bots start with USD, BTC, and ETH.');
    console.log('The "total" field likely includes all currencies converted to USD.');
}

main().catch(console.error);

#                                               -*- Autoconf -*-
# Process this file with autoconf to produce a configure script.

AC_PREREQ(2.59)
AC_COPYRIGHT([(c) TicTacTec 2005, 2006])
AC_INIT([ta-lib], [0.4.0], [http://sourceforge.net/tracker/?group_id=8903&atid=108903])
AC_CONFIG_SRCDIR([src/ta_func/ta_AD.c])
AC_CONFIG_HEADER([include/ta_config.h])
AM_INIT_AUTOMAKE([ta-lib], [0.4.0])

# Checks for programs.
AC_PROG_CC
AC_PROG_LIBTOOL

# Checks for libraries.
AC_CHECK_LIB([dl], [dlopen])
AC_CHECK_LIB([pthread], [pthread_create])

# Checks for header files.
AC_HEADER_STDC
AC_CHECK_HEADERS([float.h inttypes.h limits.h locale.h stddef.h stdint.h stdlib.h string.h unistd.h wchar.h wctype.h])

# Checks for typedefs, structures, and compiler characteristics.
AC_C_CONST
AC_TYPE_SIZE_T
AC_STRUCT_TM
AC_C_VOLATILE
AC_CHECK_TYPES([ptrdiff_t])

# Checks for library functions.
AC_TYPE_SIGNAL
AC_FUNC_STRCOLL
AC_FUNC_STRFTIME
AC_FUNC_STRTOD
AC_FUNC_VPRINTF
AC_CHECK_FUNCS([floor isascii localeconv mblen memmove memset modf pow sqrt strcasecmp strchr strerror strncasecmp strrchr strstr strtol strtoul])

# Versioning:
# Only change this if library is no longer
# ABI compatible with previous version
# (e.g. function declaration changed)
TALIB_LIBRARY_VERSION=0:0:0

AC_SUBST(TALIB_LIBRARY_VERSION)

AC_CONFIG_FILES([Makefile src/Makefile src/ta_abstract/Makefile src/ta_common/Makefile src/ta_func/Makefile src/tools/Makefile src/tools/gen_code/Makefile src/tools/ta_regtest/Makefile ta-lib-config ta-lib.spec ta-lib.dpkg])
AC_OUTPUT

#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');

async function updateDockerCompose(composePath, port) {
  try {
    console.log(`Updating docker-compose: ${composePath}`);

    const composeContent = `version: '3.8'
services:
  freqtrade:
    image: freqtradeorg/freqtrade:stable
    container_name: ${path.basename(path.dirname(composePath))}
    restart: unless-stopped
    volumes:
      # 1. Mount instance's user_data (logs, db, COPIED strategies, etc.) - Read/Write
      - ./user_data:/freqtrade/user_data
      # 2. Mount instance's config read-only
      - ./config.json:/freqtrade/config.json:ro
      # 3. Mount the specific SHARED host exchange data dir read-only to a separate location
      #    FreqTrade will use the data from user_data/data but we can symlink or copy from shared data
      - /root/freqtrade_shared_data/kraken:/freqtrade/shared_data/kraken:ro
    ports:
      - "0.0.0.0:${port}:${port}"
    environment:
      - FREQTRADE_USER_DATA_DIR=/freqtrade/user_data
      - SQLALCHEMY_POOL_SIZE=1
      - SQLALCHEMY_MAX_OVERFLOW=0
      - SQLALCHEMY_POOL_TIMEOUT=60
      - SQLALCHEMY_POOL_RECYCLE=3600
    command: trade --config /freqtrade/config.json --logfile /freqtrade/user_data/logs/freqtrade.log
`;

    await fs.writeFile(composePath, composeContent);
    console.log(`✓ Updated: ${composePath}`);

    return true;
  } catch (error) {
    console.error(`✗ Failed to update ${composePath}:`, error.message);
    return false;
  }
}

async function main() {
  const BOT_BASE_DIR = process.env.BOT_BASE_DIR || path.join(__dirname, '..', 'freqtrade-instances');

  console.log('🔧 Updating docker-compose files to fix database connection pooling...');
  console.log(`Bot base directory: ${BOT_BASE_DIR}`);

  let updatedCount = 0;
  let failedCount = 0;

  try {
    // Scan for all bot instances
    const users = await fs.readdir(BOT_BASE_DIR);

    for (const userId of users) {
      const userDir = path.join(BOT_BASE_DIR, userId);
      const userStat = await fs.stat(userDir);

      if (userStat.isDirectory()) {
        console.log(`\nProcessing user: ${userId}`);

        const instances = await fs.readdir(userDir);

        for (const instanceId of instances) {
          const instanceDir = path.join(userDir, instanceId);
          const instanceStat = await fs.stat(instanceDir);

          if (instanceStat.isDirectory()) {
            const composePath = path.join(instanceDir, 'docker-compose.yml');
            const configPath = path.join(instanceDir, 'config.json');

            if (await fs.pathExists(composePath) && await fs.pathExists(configPath)) {
              // Read config to get port
              const config = JSON.parse(await fs.readFile(configPath, 'utf8'));
              const port = config.api_server?.listen_port || 8080;

              const success = await updateDockerCompose(composePath, port);
              if (success) {
                updatedCount++;
              } else {
                failedCount++;
              }
            } else {
              console.log(`⚠️  No docker-compose.yml or config.json found in ${instanceDir}`);
            }
          }
        }
      }
    }

    console.log('\n📊 Summary:');
    console.log(`✓ Successfully updated: ${updatedCount} docker-compose files`);
    console.log(`✗ Failed to update: ${failedCount} docker-compose files`);

    if (updatedCount > 0) {
      console.log('\n🔄 Now restart all bot containers to apply the changes:');
      console.log('   docker-compose up -d (in each bot directory)');
    }

  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { updateDockerCompose };

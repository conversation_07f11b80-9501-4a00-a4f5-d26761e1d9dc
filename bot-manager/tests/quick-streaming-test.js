#!/usr/bin/env node

/**
 * Quick WebSocket Streaming Test
 * Tests the live streaming functionality with real-time updates
 */

const WebSocket = require('ws');

// Test token (valid for testing)
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.9hz7pLZBUnzcrFHeJ4eGiYMthifPGfRGTTcRoZ0qYjc';

console.log('🚀 Starting WebSocket Streaming Test...\n');

const ws = new WebSocket(`ws://127.0.0.1:3001/ws?token=${TEST_TOKEN}`);

let messageCount = 0;
const startTime = Date.now();

ws.on('open', () => {
  console.log('✅ WebSocket connection established');
  console.log('📡 Subscribing to all streaming channels...\n');
  
  // Subscribe to all available channels
  ws.send(JSON.stringify({
    type: 'subscribe_updates',
    data: {
      channels: ['portfolio', 'bot_metrics', 'timeseries', 'trade_alerts', 'bot_status', 'system_health']
    }
  }));
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    messageCount++;
    
    const timestamp = new Date().toLocaleTimeString();
    
    switch (message.type) {
      case 'subscription_confirmed':
        console.log('🎯 Subscription Confirmed:');
        console.log(`   📊 Subscribed to: ${message.data.subscribedChannels.join(', ')}`);
        console.log('   ⏱️  Streaming intervals:');
        Object.entries(message.data.streamingIntervals).forEach(([channel, interval]) => {
          console.log(`      • ${channel}: ${interval}`);
        });
        console.log('\n🔴 Live streaming started... (Press Ctrl+C to stop)\n');
        break;
        
      case 'portfolio_update':
        console.log(`[${timestamp}] 💰 Portfolio Update: Value=$${message.data.totalValue?.toFixed(2) || '0.00'}, P&L=$${message.data.totalPnl?.toFixed(2) || '0.00'}`);
        break;
        
      case 'bot_metrics_update':
        console.log(`[${timestamp}] 🤖 Bot Metrics: ${message.data.botId} - Status: ${message.data.status}`);
        break;
        
      case 'timeseries_update':
        console.log(`[${timestamp}] 📈 Time-Series: New data point added (${message.data.timeframe})`);
        break;
        
      case 'trade_alert':
        console.log(`[${timestamp}] 🔔 Trade Alert: ${message.data.action?.toUpperCase()} ${message.data.pair} - $${message.data.price}`);
        break;
        
      case 'bot_status_update':
        console.log(`[${timestamp}] 🔄 Bot Status: ${message.data.botId} changed to ${message.data.newStatus}`);
        break;
        
      case 'system_health_update':
        console.log(`[${timestamp}] ❤️  System Health: ${message.data.status} - Memory: ${message.data.memoryUsage?.percentage?.toFixed(1)}%`);
        break;
        
      case 'error':
        console.log(`[${timestamp}] ❌ Error: ${message.data.message}`);
        break;
        
      default:
        console.log(`[${timestamp}] 📨 Received: ${message.type}`);
    }
  } catch (error) {
    console.log(`❌ Error parsing message: ${error.message}`);
  }
});

ws.on('error', (error) => {
  console.log(`❌ WebSocket error: ${error.message}`);
});

ws.on('close', (code, reason) => {
  const duration = ((Date.now() - startTime) / 1000).toFixed(1);
  console.log(`\n🔌 Connection closed (code: ${code})`);
  console.log(`📊 Test Results:`);
  console.log(`   • Duration: ${duration} seconds`);
  console.log(`   • Messages received: ${messageCount}`);
  console.log(`   • Average rate: ${(messageCount / (duration / 60)).toFixed(1)} messages/minute`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping test...');
  ws.close();
  process.exit(0);
});

// Auto-stop after 2 minutes for demo
setTimeout(() => {
  console.log('\n⏰ 2-minute test completed!');
  ws.close();
}, 120000);

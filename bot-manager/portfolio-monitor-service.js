#!/usr/bin/env node

'use strict';

/**
 * DEPRECATED: The standalone Portfolio Monitor Service has been retired.
 * Live data is provided by the API Gateway via SSE at GET /api/stream.
 */

class PortfolioMonitorService {
  constructor() {
    if (!PortfolioMonitorService._warned) {
      console.warn('[DEPRECATED] portfolio-monitor-service.js has been removed. Use SSE /api/stream.');
      PortfolioMonitorService._warned = true;
    }
  }
  async start() { }
  async stop() { }
  trackBotCreation() { }
  getStats() { return { isRunning: false }; }
}

if (require.main === module) {
  console.warn('[DEPRECATED] This service is no longer runnable. Use SSE /api/stream.');
  process.exit(0);
}

module.exports = { PortfolioMonitorService };

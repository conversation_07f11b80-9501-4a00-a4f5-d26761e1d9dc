{"portfolioMonitor": {"enabled": true, "snapshotInterval": 120000, "botDiscoveryInterval": 300000, "maxRetries": 2, "retryDelay": 10000, "newBotGracePeriod": 300000, "botHealthCheckTimeout": 3000, "maxConcurrentBotCalls": 2, "compression": {"enabled": true, "maxSnapshots": 10000, "compressionThreshold": 1000}, "backup": {"enabled": true, "interval": 86400000, "retention": {"daily": 30, "weekly": 12, "monthly": 12}}, "monitoring": {"statusUpdateInterval": 300000, "healthCheckInterval": 60000, "alertThresholds": {"errorRate": 0.1, "responseTime": 5000}}}, "paths": {"botBaseDir": "/root/Crypto-Pilot-Freqtrade/freqtrade-instances", "logDir": "/var/log/portfolio-monitor", "backupDir": "portfolio_backups"}, "api": {"timeout": 3000, "defaultPort": 8080, "retryInterval": 30000, "connectionPoolLimit": 5, "botStartupDelay": 60000}, "logging": {"level": "info", "maxSize": "10MB", "maxFiles": 5, "datePattern": "YYYY-MM-DD"}}
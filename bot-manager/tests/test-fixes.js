const WebSocket = require('ws');

console.log('Testing Phase 4 fixes...');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.YNVYNZ8x5_r0svHvvBX2K52I63T9EroHfUASfgeT3m4';

const ws = new WebSocket(`ws://127.0.0.1:3001/ws?token=${token}`);

ws.on('open', function open() {
    console.log('✓ WebSocket connection opened');

    // Test system health check (was failing with toUpperCase error)
    console.log('Testing system health check...');
    ws.send(JSON.stringify({
        type: 'get_system_health'
    }));

    setTimeout(() => {
        // Test bot management (was failing with service.stopBot not a function)
        console.log('Testing bot management...');
        ws.send(JSON.stringify({
            type: 'start_bot',
            botId: 'tessa-bot1' // Use real bot
        }));
    }, 2000);

    setTimeout(() => {
        console.log('Testing complete. Closing connection...');
        ws.close();
    }, 5000);
});

ws.on('message', function message(data) {
    try {
        const parsed = JSON.parse(data);
        console.log('✓ Received message:', {
            type: parsed.type,
            status: parsed.status || 'N/A',
            hasData: !!parsed.data,
            timestamp: new Date().toISOString()
        });

        if (parsed.type === 'system_health_response') {
            console.log('✓ System health response received');
            console.log('System health data:', JSON.stringify(parsed.data, null, 2));
            if (parsed.data && parsed.data.overall_status) {
                console.log('✓ overall_status field present:', parsed.data.overall_status);
            } else {
                console.log('⚠ overall_status field missing');
            }
        } else if (parsed.type === 'system_health') {
            console.log('✓ System health broadcast received');
            console.log('System health data:', JSON.stringify(parsed.data, null, 2));
            if (parsed.data && parsed.data.overall_status) {
                console.log('✓ overall_status field present:', parsed.data.overall_status);
            } else {
                console.log('⚠ overall_status field missing');
            }
        }

        if (parsed.type === 'bot_action_response' || parsed.type === 'bot_started') {
            console.log('✓ Bot action response received');
            console.log('Bot response data:', JSON.stringify(parsed.data, null, 2));
        }
    } catch (e) {
        console.log('Raw message:', data.toString());
    }
});

ws.on('error', function error(err) {
    console.error('✗ WebSocket error:', err.message);
});

ws.on('close', function close() {
    console.log('✓ WebSocket connection closed');
});

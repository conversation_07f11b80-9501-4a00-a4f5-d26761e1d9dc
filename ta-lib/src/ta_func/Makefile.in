# Makefile.in generated by automake 1.10 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006  Free Software Foundation, Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@


VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = src/ta_func
DIST_COMMON = $(libta_func_HEADERS) $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.in
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/ta_config.h
CONFIG_CLEAN_FILES =
LTLIBRARIES = $(noinst_LTLIBRARIES)
libta_func_la_LIBADD =
am_libta_func_la_OBJECTS = ta_utility.lo ta_ACOS.lo ta_AD.lo ta_ADD.lo \
	ta_ADOSC.lo ta_ADX.lo ta_ADXR.lo ta_APO.lo ta_AROON.lo \
	ta_AROONOSC.lo ta_ASIN.lo ta_ATAN.lo ta_ATR.lo ta_AVGPRICE.lo \
	ta_BBANDS.lo ta_BETA.lo ta_BOP.lo ta_CCI.lo ta_CDL2CROWS.lo \
	ta_CDL3BLACKCROWS.lo ta_CDL3INSIDE.lo ta_CDL3LINESTRIKE.lo \
	ta_CDL3OUTSIDE.lo ta_CDL3STARSINSOUTH.lo \
	ta_CDL3WHITESOLDIERS.lo ta_CDLABANDONEDBABY.lo \
	ta_CDLADVANCEBLOCK.lo ta_CDLBELTHOLD.lo ta_CDLBREAKAWAY.lo \
	ta_CDLCLOSINGMARUBOZU.lo ta_CDLCONCEALBABYSWALL.lo \
	ta_CDLCOUNTERATTACK.lo ta_CDLDARKCLOUDCOVER.lo ta_CDLDOJI.lo \
	ta_CDLDOJISTAR.lo ta_CDLDRAGONFLYDOJI.lo ta_CDLENGULFING.lo \
	ta_CDLEVENINGDOJISTAR.lo ta_CDLEVENINGSTAR.lo \
	ta_CDLGAPSIDESIDEWHITE.lo ta_CDLGRAVESTONEDOJI.lo \
	ta_CDLHAMMER.lo ta_CDLHANGINGMAN.lo ta_CDLHARAMI.lo \
	ta_CDLHARAMICROSS.lo ta_CDLHIGHWAVE.lo ta_CDLHIKKAKE.lo \
	ta_CDLHIKKAKEMOD.lo ta_CDLHOMINGPIGEON.lo \
	ta_CDLIDENTICAL3CROWS.lo ta_CDLINNECK.lo \
	ta_CDLINVERTEDHAMMER.lo ta_CDLKICKING.lo \
	ta_CDLKICKINGBYLENGTH.lo ta_CDLLADDERBOTTOM.lo \
	ta_CDLLONGLEGGEDDOJI.lo ta_CDLLONGLINE.lo ta_CDLMARUBOZU.lo \
	ta_CDLMATCHINGLOW.lo ta_CDLMATHOLD.lo ta_CDLMORNINGDOJISTAR.lo \
	ta_CDLMORNINGSTAR.lo ta_CDLONNECK.lo ta_CDLPIERCING.lo \
	ta_CDLRICKSHAWMAN.lo ta_CDLRISEFALL3METHODS.lo \
	ta_CDLSEPARATINGLINES.lo ta_CDLSHOOTINGSTAR.lo \
	ta_CDLSHORTLINE.lo ta_CDLSPINNINGTOP.lo \
	ta_CDLSTALLEDPATTERN.lo ta_CDLSTICKSANDWICH.lo ta_CDLTAKURI.lo \
	ta_CDLTASUKIGAP.lo ta_CDLTHRUSTING.lo ta_CDLTRISTAR.lo \
	ta_CDLUNIQUE3RIVER.lo ta_CDLUPSIDEGAP2CROWS.lo \
	ta_CDLXSIDEGAP3METHODS.lo ta_CEIL.lo ta_CMO.lo ta_CORREL.lo \
	ta_COS.lo ta_COSH.lo ta_DEMA.lo ta_DIV.lo ta_DX.lo ta_EMA.lo \
	ta_EXP.lo ta_FLOOR.lo ta_HT_DCPERIOD.lo ta_HT_DCPHASE.lo \
	ta_HT_PHASOR.lo ta_HT_SINE.lo ta_HT_TRENDLINE.lo \
	ta_HT_TRENDMODE.lo ta_KAMA.lo ta_LINEARREG.lo \
	ta_LINEARREG_ANGLE.lo ta_LINEARREG_INTERCEPT.lo \
	ta_LINEARREG_SLOPE.lo ta_LN.lo ta_LOG10.lo ta_MA.lo ta_MACD.lo \
	ta_MACDEXT.lo ta_MACDFIX.lo ta_MAMA.lo ta_MAVP.lo ta_MAX.lo \
	ta_MAXINDEX.lo ta_MEDPRICE.lo ta_MFI.lo ta_MIDPOINT.lo \
	ta_MIDPRICE.lo ta_MIN.lo ta_MININDEX.lo ta_MINMAX.lo \
	ta_MINMAXINDEX.lo ta_MINUS_DI.lo ta_MINUS_DM.lo ta_MOM.lo \
	ta_MULT.lo ta_NATR.lo ta_OBV.lo ta_PLUS_DI.lo ta_PLUS_DM.lo \
	ta_PPO.lo ta_ROC.lo ta_ROCP.lo ta_ROCR.lo ta_ROCR100.lo \
	ta_RSI.lo ta_SAR.lo ta_SAREXT.lo ta_SIN.lo ta_SINH.lo \
	ta_SMA.lo ta_SQRT.lo ta_STDDEV.lo ta_STOCH.lo ta_STOCHF.lo \
	ta_STOCHRSI.lo ta_SUB.lo ta_SUM.lo ta_T3.lo ta_TAN.lo \
	ta_TANH.lo ta_TEMA.lo ta_TRANGE.lo ta_TRIMA.lo ta_TRIX.lo \
	ta_TSF.lo ta_TYPPRICE.lo ta_ULTOSC.lo ta_VAR.lo ta_WCLPRICE.lo \
	ta_WILLR.lo ta_WMA.lo
libta_func_la_OBJECTS = $(am_libta_func_la_OBJECTS)
libta_func_la_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libta_func_la_LDFLAGS) $(LDFLAGS) -o $@
DEFAULT_INCLUDES = -I. -I$(top_builddir)/include@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__depfiles_maybe = depfiles
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
CCLD = $(CC)
LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
SOURCES = $(libta_func_la_SOURCES)
DIST_SOURCES = $(libta_func_la_SOURCES)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = `echo $$p | sed -e 's|^.*/||'`;
am__installdirs = "$(DESTDIR)$(libta_funcdir)"
libta_funcHEADERS_INSTALL = $(INSTALL_HEADER)
HEADERS = $(libta_func_HEADERS)
ETAGS = etags
CTAGS = ctags
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO = @ECHO@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F77 = @F77@
FFLAGS = @FFLAGS@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
POW_LIB = @POW_LIB@
RANLIB = @RANLIB@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
TALIB_LIBRARY_VERSION = @TALIB_LIBRARY_VERSION@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_F77 = @ac_ct_F77@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
noinst_LTLIBRARIES = libta_func.la
AM_CPPFLAGS = -I../ta_common/
libta_func_la_SOURCES = ta_utility.c \
	ta_ACOS.c \
	ta_AD.c \
	ta_ADD.c \
	ta_ADOSC.c \
	ta_ADX.c \
	ta_ADXR.c \
	ta_APO.c \
	ta_AROON.c \
	ta_AROONOSC.c \
	ta_ASIN.c \
	ta_ATAN.c \
	ta_ATR.c \
	ta_AVGPRICE.c \
	ta_BBANDS.c \
	ta_BETA.c \
	ta_BOP.c \
	ta_CCI.c \
	ta_CDL2CROWS.c \
	ta_CDL3BLACKCROWS.c \
	ta_CDL3INSIDE.c \
	ta_CDL3LINESTRIKE.c \
	ta_CDL3OUTSIDE.c \
	ta_CDL3STARSINSOUTH.c \
	ta_CDL3WHITESOLDIERS.c \
	ta_CDLABANDONEDBABY.c \
	ta_CDLADVANCEBLOCK.c \
	ta_CDLBELTHOLD.c \
	ta_CDLBREAKAWAY.c \
	ta_CDLCLOSINGMARUBOZU.c \
	ta_CDLCONCEALBABYSWALL.c \
	ta_CDLCOUNTERATTACK.c \
	ta_CDLDARKCLOUDCOVER.c \
	ta_CDLDOJI.c \
	ta_CDLDOJISTAR.c \
	ta_CDLDRAGONFLYDOJI.c \
	ta_CDLENGULFING.c \
	ta_CDLEVENINGDOJISTAR.c \
	ta_CDLEVENINGSTAR.c \
	ta_CDLGAPSIDESIDEWHITE.c \
	ta_CDLGRAVESTONEDOJI.c \
	ta_CDLHAMMER.c \
	ta_CDLHANGINGMAN.c \
	ta_CDLHARAMI.c \
	ta_CDLHARAMICROSS.c \
	ta_CDLHIGHWAVE.c \
	ta_CDLHIKKAKE.c \
	ta_CDLHIKKAKEMOD.c \
	ta_CDLHOMINGPIGEON.c \
	ta_CDLIDENTICAL3CROWS.c \
	ta_CDLINNECK.c \
	ta_CDLINVERTEDHAMMER.c \
	ta_CDLKICKING.c \
	ta_CDLKICKINGBYLENGTH.c \
	ta_CDLLADDERBOTTOM.c \
	ta_CDLLONGLEGGEDDOJI.c \
	ta_CDLLONGLINE.c \
	ta_CDLMARUBOZU.c \
	ta_CDLMATCHINGLOW.c \
	ta_CDLMATHOLD.c \
	ta_CDLMORNINGDOJISTAR.c \
	ta_CDLMORNINGSTAR.c \
	ta_CDLONNECK.c \
	ta_CDLPIERCING.c \
	ta_CDLRICKSHAWMAN.c \
	ta_CDLRISEFALL3METHODS.c \
	ta_CDLSEPARATINGLINES.c \
	ta_CDLSHOOTINGSTAR.c \
	ta_CDLSHORTLINE.c \
	ta_CDLSPINNINGTOP.c \
	ta_CDLSTALLEDPATTERN.c \
	ta_CDLSTICKSANDWICH.c \
	ta_CDLTAKURI.c \
	ta_CDLTASUKIGAP.c \
	ta_CDLTHRUSTING.c \
	ta_CDLTRISTAR.c \
	ta_CDLUNIQUE3RIVER.c \
	ta_CDLUPSIDEGAP2CROWS.c \
	ta_CDLXSIDEGAP3METHODS.c \
	ta_CEIL.c \
	ta_CMO.c \
	ta_CORREL.c \
	ta_COS.c \
	ta_COSH.c \
	ta_DEMA.c \
	ta_DIV.c \
	ta_DX.c \
	ta_EMA.c \
	ta_EXP.c \
	ta_FLOOR.c \
	ta_HT_DCPERIOD.c \
	ta_HT_DCPHASE.c \
	ta_HT_PHASOR.c \
	ta_HT_SINE.c \
	ta_HT_TRENDLINE.c \
	ta_HT_TRENDMODE.c \
	ta_KAMA.c \
	ta_LINEARREG.c \
	ta_LINEARREG_ANGLE.c \
	ta_LINEARREG_INTERCEPT.c \
	ta_LINEARREG_SLOPE.c \
	ta_LN.c \
	ta_LOG10.c \
	ta_MA.c \
	ta_MACD.c \
	ta_MACDEXT.c \
	ta_MACDFIX.c \
	ta_MAMA.c \
	ta_MAVP.c \
	ta_MAX.c \
	ta_MAXINDEX.c \
	ta_MEDPRICE.c \
	ta_MFI.c \
	ta_MIDPOINT.c \
	ta_MIDPRICE.c \
	ta_MIN.c \
	ta_MININDEX.c \
	ta_MINMAX.c \
	ta_MINMAXINDEX.c \
	ta_MINUS_DI.c \
	ta_MINUS_DM.c \
	ta_MOM.c \
	ta_MULT.c \
	ta_NATR.c \
	ta_OBV.c \
	ta_PLUS_DI.c \
	ta_PLUS_DM.c \
	ta_PPO.c \
	ta_ROC.c \
	ta_ROCP.c \
	ta_ROCR.c \
	ta_ROCR100.c \
	ta_RSI.c \
	ta_SAR.c \
	ta_SAREXT.c \
	ta_SIN.c \
	ta_SINH.c \
	ta_SMA.c \
	ta_SQRT.c \
	ta_STDDEV.c \
	ta_STOCH.c \
	ta_STOCHF.c \
	ta_STOCHRSI.c \
	ta_SUB.c \
	ta_SUM.c \
	ta_T3.c \
	ta_TAN.c \
	ta_TANH.c \
	ta_TEMA.c \
	ta_TRANGE.c \
	ta_TRIMA.c \
	ta_TRIX.c \
	ta_TSF.c \
	ta_TYPPRICE.c \
	ta_ULTOSC.c \
	ta_VAR.c \
	ta_WCLPRICE.c \
	ta_WILLR.c \
	ta_WMA.c

libta_func_la_LDFLAGS = -version-info $(TALIB_LIBRARY_VERSION)
libta_funcdir = $(includedir)/ta-lib/
libta_func_HEADERS = ../../include/ta_defs.h \
	../../include/ta_libc.h \
	../../include/ta_func.h

all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu  src/ta_func/Makefile'; \
	cd $(top_srcdir) && \
	  $(AUTOMAKE) --gnu  src/ta_func/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; for p in $$list; do \
	  dir="`echo $$p | sed -e 's|/[^/]*$$||'`"; \
	  test "$$dir" != "$$p" || dir=.; \
	  echo "rm -f \"$${dir}/so_locations\""; \
	  rm -f "$${dir}/so_locations"; \
	done
libta_func.la: $(libta_func_la_OBJECTS) $(libta_func_la_DEPENDENCIES) 
	$(libta_func_la_LINK)  $(libta_func_la_OBJECTS) $(libta_func_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ACOS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AD.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADD.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADOSC.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADXR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_APO.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AROON.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AROONOSC.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ASIN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ATAN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ATR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AVGPRICE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_BBANDS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_BETA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_BOP.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CCI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL2CROWS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3BLACKCROWS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3INSIDE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3LINESTRIKE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3OUTSIDE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3STARSINSOUTH.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3WHITESOLDIERS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLABANDONEDBABY.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLADVANCEBLOCK.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLBELTHOLD.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLBREAKAWAY.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLCLOSINGMARUBOZU.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLCONCEALBABYSWALL.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLCOUNTERATTACK.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDARKCLOUDCOVER.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDOJI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDOJISTAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDRAGONFLYDOJI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLENGULFING.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLEVENINGDOJISTAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLEVENINGSTAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLGAPSIDESIDEWHITE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLGRAVESTONEDOJI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHAMMER.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHANGINGMAN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHARAMI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHARAMICROSS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHIGHWAVE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHIKKAKE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHIKKAKEMOD.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHOMINGPIGEON.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLIDENTICAL3CROWS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLINNECK.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLINVERTEDHAMMER.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLKICKING.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLKICKINGBYLENGTH.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLLADDERBOTTOM.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLLONGLEGGEDDOJI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLLONGLINE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMARUBOZU.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMATCHINGLOW.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMATHOLD.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMORNINGDOJISTAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMORNINGSTAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLONNECK.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLPIERCING.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLRICKSHAWMAN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLRISEFALL3METHODS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSEPARATINGLINES.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSHOOTINGSTAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSHORTLINE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSPINNINGTOP.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSTALLEDPATTERN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSTICKSANDWICH.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTAKURI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTASUKIGAP.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTHRUSTING.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTRISTAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLUNIQUE3RIVER.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLUPSIDEGAP2CROWS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLXSIDEGAP3METHODS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CEIL.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CMO.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CORREL.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_COS.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_COSH.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_DEMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_DIV.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_DX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_EMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_EXP.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_FLOOR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_DCPERIOD.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_DCPHASE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_PHASOR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_SINE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_TRENDLINE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_TRENDMODE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_KAMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG_ANGLE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG_INTERCEPT.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG_SLOPE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LOG10.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MACD.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MACDEXT.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MACDFIX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAVP.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAXINDEX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MEDPRICE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MFI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MIDPOINT.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MIDPRICE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MIN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MININDEX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINMAX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINMAXINDEX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINUS_DI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINUS_DM.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MOM.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MULT.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_NATR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_OBV.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_PLUS_DI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_PLUS_DM.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_PPO.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROC.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROCP.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROCR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROCR100.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_RSI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SAREXT.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SIN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SINH.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SQRT.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STDDEV.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STOCH.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STOCHF.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STOCHRSI.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SUB.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SUM.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_T3.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TAN.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TANH.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TEMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TRANGE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TRIMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TRIX.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TSF.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TYPPRICE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ULTOSC.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_VAR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_WCLPRICE.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_WILLR.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_WMA.Plo@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_utility.Plo@am__quote@

.c.o:
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c $<

.c.obj:
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-libta_funcHEADERS: $(libta_func_HEADERS)
	@$(NORMAL_INSTALL)
	test -z "$(libta_funcdir)" || $(MKDIR_P) "$(DESTDIR)$(libta_funcdir)"
	@list='$(libta_func_HEADERS)'; for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  f=$(am__strip_dir) \
	  echo " $(libta_funcHEADERS_INSTALL) '$$d$$p' '$(DESTDIR)$(libta_funcdir)/$$f'"; \
	  $(libta_funcHEADERS_INSTALL) "$$d$$p" "$(DESTDIR)$(libta_funcdir)/$$f"; \
	done

uninstall-libta_funcHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(libta_func_HEADERS)'; for p in $$list; do \
	  f=$(am__strip_dir) \
	  echo " rm -f '$(DESTDIR)$(libta_funcdir)/$$f'"; \
	  rm -f "$(DESTDIR)$(libta_funcdir)/$$f"; \
	done

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	if test -z "$(ETAGS_ARGS)$$tags$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	    $$tags $$unique; \
	fi
ctags: CTAGS
CTAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	test -z "$(CTAGS_ARGS)$$tags$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$tags $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && cd $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) $$here

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -pR $(srcdir)/$$file $(distdir)$$dir || exit 1; \
	    fi; \
	    cp -pR $$d/$$file $(distdir)$$dir || exit 1; \
	  else \
	    test -f $(distdir)/$$file \
	    || cp -p $$d/$$file $(distdir)/$$file \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libta_funcdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	mostlyclean-am

distclean: distclean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

info: info-am

info-am:

install-data-am: install-libta_funcHEADERS

install-dvi: install-dvi-am

install-exec-am:

install-html: install-html-am

install-info: install-info-am

install-man:

install-pdf: install-pdf-am

install-ps: install-ps-am

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libta_funcHEADERS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS all all-am check check-am clean clean-generic \
	clean-libtool clean-noinstLTLIBRARIES ctags distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libta_funcHEADERS install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags uninstall uninstall-am uninstall-libta_funcHEADERS

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

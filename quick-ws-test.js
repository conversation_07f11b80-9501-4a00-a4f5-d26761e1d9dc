#!/usr/bin/env node

const WebSocket = require('ws');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************.aMz4o-Upq1aQ7wki_bKHS8hOeRvzAtvT6B6k44GtwfQ';

console.log('🔌 Testing WebSocket connection...');

const ws = new WebSocket(`ws://127.0.0.1:3001/ws?token=${token}`);

ws.on('open', () => {
    console.log('✅ Connected to WebSocket!');
    
    // Subscribe to all channels
    const subscribeMessage = {
        type: 'subscribe_updates',
        data: {
            channels: ['portfolio', 'bot_metrics', 'timeseries', 'trade_alerts', 'bot_status', 'system_health']
        }
    };
    
    console.log('📤 Subscribing to channels:', subscribeMessage.data.channels);
    ws.send(JSON.stringify(subscribeMessage));
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data);
        console.log(`📨 Received ${message.type}:`);
        console.log('   Full message:', JSON.stringify(message, null, 2));
        
        if (message.type === 'subscription_confirmed') {
            console.log('✅ Subscription confirmed for channels:', message.data.subscribedChannels);
            console.log('⏰ Streaming intervals:', message.data.streamingIntervals);
        } else if (message.type === 'error') {
            console.log('❌ Error details:', message.data);
        }
    } catch (error) {
        console.log('📨 Raw message:', data.toString());
    }
});

ws.on('error', (error) => {
    console.log('❌ WebSocket error:', error.message);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 Connection closed: ${code} - ${reason}`);
});

// Keep the script running
setTimeout(() => {
    console.log('🕐 Test completed after 30 seconds');
    ws.close();
    process.exit(0);
}, 30000);

ta_regtest-test_sar.o: ta_test_func/test_sar.c /usr/include/stdc-predef.h \
 /usr/include/stdio.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/x86_64-linux-gnu/bits/stdio.h /usr/include/string.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/strings.h ta_test_priv.h ../../../include/ta_libc.h \
 ../../../include/ta_common.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/syslimits.h \
 /usr/include/limits.h /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min.h \
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/float.h \
 ../../../include/ta_defs.h ../../../include/ta_func.h \
 ../../../include/ta_abstract.h ta_error_number.h ta_test_func.h \
 ../../ta_func/ta_utility.h ../../ta_common/ta_global.h
/usr/include/stdc-predef.h:
/usr/include/stdio.h:
/usr/include/x86_64-linux-gnu/bits/libc-header-start.h:
/usr/include/features.h:
/usr/include/features-time64.h:
/usr/include/x86_64-linux-gnu/bits/wordsize.h:
/usr/include/x86_64-linux-gnu/bits/timesize.h:
/usr/include/x86_64-linux-gnu/sys/cdefs.h:
/usr/include/x86_64-linux-gnu/bits/long-double.h:
/usr/include/x86_64-linux-gnu/gnu/stubs.h:
/usr/include/x86_64-linux-gnu/gnu/stubs-64.h:
/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h:
/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h:
/usr/include/x86_64-linux-gnu/bits/types.h:
/usr/include/x86_64-linux-gnu/bits/typesizes.h:
/usr/include/x86_64-linux-gnu/bits/time64.h:
/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__FILE.h:
/usr/include/x86_64-linux-gnu/bits/types/FILE.h:
/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h:
/usr/include/x86_64-linux-gnu/bits/stdio_lim.h:
/usr/include/x86_64-linux-gnu/bits/floatn.h:
/usr/include/x86_64-linux-gnu/bits/floatn-common.h:
/usr/include/x86_64-linux-gnu/bits/stdio.h:
/usr/include/string.h:
/usr/include/x86_64-linux-gnu/bits/types/locale_t.h:
/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h:
/usr/include/strings.h:
ta_test_priv.h:
../../../include/ta_libc.h:
../../../include/ta_common.h:
/usr/lib/gcc/x86_64-linux-gnu/12/include/limits.h:
/usr/lib/gcc/x86_64-linux-gnu/12/include/syslimits.h:
/usr/include/limits.h:
/usr/include/x86_64-linux-gnu/bits/posix1_lim.h:
/usr/include/x86_64-linux-gnu/bits/local_lim.h:
/usr/include/linux/limits.h:
/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h:
/usr/include/x86_64-linux-gnu/bits/pthread_stack_min.h:
/usr/include/x86_64-linux-gnu/bits/posix2_lim.h:
/usr/lib/gcc/x86_64-linux-gnu/12/include/float.h:
../../../include/ta_defs.h:
../../../include/ta_func.h:
../../../include/ta_abstract.h:
ta_error_number.h:
ta_test_func.h:
../../ta_func/ta_utility.h:
../../ta_common/ta_global.h:

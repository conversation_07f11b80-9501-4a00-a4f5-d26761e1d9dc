const { authenticateToken } = require('./auth');


// Test the authentication with our test token
const testToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.XwBKKfCu__BoMiGMHS6JJRW7fCOJAp18JxRYc1JIKJA";

// Mock request object
const mockReq = {
    path: '/test',
    header: (name) => {
        if (name === 'Authorization') return `Bearer ${testToken}`;
        return null;
    }
};

const mockRes = {
    status: (code) => ({
        json: (data) => {
            console.log('Response:', code, data);
            return mockRes;
        }
    })
};

const mockNext = (error) => {
    if (error) {
        console.log('Next called with error:', error);
    } else {
        console.log('Next called successfully');
        console.log('User object:', mockReq.user);
    }
};

console.log('Testing authentication with JWT token...');
authenticateToken(mockReq, mockRes, mockNext);

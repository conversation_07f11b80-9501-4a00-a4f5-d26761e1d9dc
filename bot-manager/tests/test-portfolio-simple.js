#!/usr/bin/env node

/**
 * Simple Portfolio Snapshot Testing Script
 * 
 * This script provides easy ways to test the portfolio snapshot system:
 * 1. Quick verification of snapshot file creation
 * 2. Simple WebSocket connection test
 * 3. File system monitoring
 */

const fs = require('fs-extra');
const path = require('path');
const WebSocket = require('ws');

class SimplePortfolioTester {
    constructor() {
        this.userIds = ['Js1Gaz4sMPPiDNgFbmAgDFLe4je2']; // Add your user IDs here
        this.baseDir = '/root/freqtrade-instances';
    }

    async runQuickTests() {
        console.log('🔍 Portfolio Snapshot System - Quick Tests\n');

        // Test 1: Check existing snapshot files
        await this.checkExistingSnapshots();

        // Test 2: File structure validation
        await this.validateFileStructure();

        // Test 3: Monitor file changes
        console.log('\n📂 File Monitoring Setup:');
        console.log('Run this command to watch for snapshot changes:');
        console.log(`watch -n 2 'find ${this.baseDir} -name "portfolio_snapshots.json" -exec ls -la {} \\;'`);

        // Test 4: WebSocket connection test (if server is running)
        console.log('\n🌐 WebSocket Test:');
        console.log('To test WebSocket connectivity, run:');
        console.log('npm start  # Start the server first');
        console.log('Then use this script with --websocket flag');
    }

    async checkExistingSnapshots() {
        console.log('📊 Checking existing portfolio snapshots...\n');

        for (const userId of this.userIds) {
            const userDir = path.join(this.baseDir, userId);
            const snapshotFile = path.join(userDir, 'portfolio_snapshots.json');

            console.log(`User: ${userId}`);
            console.log(`Folder: ${userDir}`);

            try {
                if (await fs.pathExists(snapshotFile)) {
                    const data = await fs.readJson(snapshotFile);
                    console.log(`✅ Snapshot file exists`);
                    console.log(`   📈 Total snapshots: ${data.snapshots?.length || 0}`);
                    console.log(`   🕐 First: ${data.metadata?.firstSnapshot ? new Date(data.metadata.firstSnapshot).toLocaleString() : 'None'}`);
                    console.log(`   🕐 Last: ${data.metadata?.lastSnapshot ? new Date(data.metadata.lastSnapshot).toLocaleString() : 'None'}`);
                    console.log(`   📊 Account age: ${data.metadata?.firstSnapshot ? Math.round((Date.now() - data.metadata.firstSnapshot) / (1000 * 60 * 60 * 24)) : 0} days`);

                    // Show latest snapshot details
                    if (data.snapshots && data.snapshots.length > 0) {
                        const latest = data.snapshots[data.snapshots.length - 1];
                        console.log(`   💰 Latest portfolio value: $${latest.portfolioValue?.toFixed(2) || '0.00'}`);
                        console.log(`   🤖 Bot count: ${latest.botCount || 0}`);
                    }
                } else {
                    console.log(`❌ No snapshot file found`);
                    console.log(`   💡 File will be created when user first connects via WebSocket`);
                }

                // Check for other portfolio files
                if (await fs.pathExists(userDir)) {
                    const files = await fs.readdir(userDir);
                    const portfolioFiles = files.filter(f => f.includes('portfolio_'));
                    if (portfolioFiles.length > 1) {
                        console.log(`   📁 Other portfolio files: ${portfolioFiles.join(', ')}`);
                    }
                }
            } catch (error) {
                console.log(`❌ Error reading snapshot file: ${error.message}`);
            }

            console.log('');
        }
    }

    async validateFileStructure() {
        console.log('🔍 Validating file structure...\n');

        for (const userId of this.userIds) {
            const snapshotFile = path.join(this.baseDir, userId, 'portfolio_snapshots.json');

            if (await fs.pathExists(snapshotFile)) {
                try {
                    const data = await fs.readJson(snapshotFile);

                    // Validate structure
                    const checks = [
                        { name: 'Has metadata', test: !!data.metadata },
                        { name: 'Has snapshots array', test: Array.isArray(data.snapshots) },
                        { name: 'Has version', test: !!data.version },
                        {
                            name: 'Metadata has required fields', test:
                                data.metadata &&
                                typeof data.metadata.totalSnapshots === 'number' &&
                                typeof data.metadata.accountCreated === 'number'
                        },
                        {
                            name: 'Snapshots have required fields', test:
                                data.snapshots.length === 0 ||
                                (data.snapshots[0].timestamp &&
                                    typeof data.snapshots[0].portfolioValue === 'number')
                        }
                    ];

                    console.log(`User ${userId} validation:`);
                    checks.forEach(check => {
                        console.log(`   ${check.test ? '✅' : '❌'} ${check.name}`);
                    });

                    // File size check
                    const stats = await fs.stat(snapshotFile);
                    console.log(`   📏 File size: ${(stats.size / 1024).toFixed(2)} KB`);

                } catch (error) {
                    console.log(`❌ File validation failed: ${error.message}`);
                }
            }
        }
    }

    async testWebSocketConnection() {
        console.log('\n🌐 Testing WebSocket connection...\n');

        // Note: This requires proper authentication
        console.log('⚠️  WebSocket testing requires:');
        console.log('   1. Running bot manager server (npm start)');
        console.log('   2. Valid JWT authentication token');
        console.log('   3. Proper CORS configuration');

        console.log('\n📝 Manual WebSocket test commands:');
        console.log('# Test connection:');
        console.log('wscat -c ws://localhost:3000/ws?token=YOUR_TOKEN');
        console.log('');
        console.log('# Send portfolio summary request:');
        console.log('{"type":"get_portfolio_summary","data":{"includeSnapshots":true}}');
        console.log('');
        console.log('# Send portfolio history request:');
        console.log('{"type":"get_portfolio_history","data":{"limit":10}}');
        console.log('');
        console.log('# Send growth analysis request:');
        console.log('{"type":"get_portfolio_growth"}');
    }

    showTroubleshootingTips() {
        console.log('\n🔧 Troubleshooting Tips:\n');

        console.log('1. 📁 Check user folders exist:');
        console.log(`   ls -la ${this.baseDir}/`);
        console.log('');

        console.log('2. 📊 Monitor real-time snapshot creation:');
        console.log(`   tail -f ${this.baseDir}/*/portfolio_snapshots.json`);
        console.log('');

        console.log('3. 🔍 Check server logs for snapshot activity:');
        console.log('   grep -i "portfolio snapshot" /var/log/syslog');
        console.log('   # Or check your application logs');
        console.log('');

        console.log('4. 🧪 Test snapshot creation manually:');
        console.log('   # Connect via WebSocket and trigger portfolio update');
        console.log('   # Check if new snapshot appears in file');
        console.log('');

        console.log('5. 🚀 Start fresh (if needed):');
        console.log(`   rm ${this.baseDir}/*/portfolio_snapshots.json`);
        console.log('   # Restart server and reconnect - should create initial snapshot');
        console.log('');

        console.log('6. 📈 Verify file permissions:');
        console.log(`   ls -la ${this.baseDir}/*/portfolio_*`);
        console.log('   # Ensure files are writable by the application');
    }

    showQuickCommands() {
        console.log('\n⚡ Quick Test Commands:\n');

        console.log('# Check if snapshot files exist:');
        console.log(`find ${this.baseDir} -name "portfolio_snapshots.json" -exec ls -la {} \\;`);
        console.log('');

        console.log('# View snapshot file contents:');
        console.log(`find ${this.baseDir} -name "portfolio_snapshots.json" -exec cat {} \\; | jq .`);
        console.log('');

        console.log('# Count snapshots per user:');
        console.log(`find ${this.baseDir} -name "portfolio_snapshots.json" -exec jq '.snapshots | length' {} \\;`);
        console.log('');

        console.log('# Watch for file changes:');
        console.log(`inotifywait -m ${this.baseDir}/ -e create,modify --include="portfolio_.*"`);
        console.log('');

        console.log('# Simple WebSocket test (requires wscat):');
        console.log('wscat -c ws://localhost:3000/ws?token=test');
    }
}

// Demo functionality
async function createDemoSnapshot() {
    console.log('🎭 Creating demo snapshot for testing...\n');

    const demoUser = 'demo-test-user';
    const demoDir = path.join('/tmp', 'portfolio-demo', demoUser);
    await fs.ensureDir(demoDir);

    const demoSnapshot = {
        metadata: {
            firstSnapshot: Date.now() - (7 * 24 * 60 * 60 * 1000), // 7 days ago
            lastSnapshot: Date.now(),
            totalSnapshots: 3,
            accountCreated: Date.now() - (7 * 24 * 60 * 60 * 1000),
            compressionHistory: []
        },
        snapshots: [
            {
                timestamp: Date.now() - (7 * 24 * 60 * 60 * 1000),
                portfolioValue: 10000,
                totalBalance: 8000,
                totalPnL: 2000,
                botCount: 2,
                activeBots: 2,
                dailyPnL: 0,
                weeklyPnL: 0,
                monthlyPnL: 0,
                bots: [
                    { instanceId: 'demo-bot-1', balance: 4000, pnl: 1000, openTrades: 2 },
                    { instanceId: 'demo-bot-2', balance: 4000, pnl: 1000, openTrades: 1 }
                ],
                riskMetrics: { portfolioRisk: 100, maxDrawdown: 500, sharpeRatio: 1.5 },
                sessionInfo: { wsConnected: true, userAgent: 'Demo-Client', ipAddress: '127.0.0.1' },
                metadata: { isInitialSnapshot: true, snapshotReason: 'first_websocket_connection' }
            },
            {
                timestamp: Date.now() - (24 * 60 * 60 * 1000), // Yesterday
                portfolioValue: 10500,
                totalBalance: 8200,
                totalPnL: 2300,
                botCount: 2,
                activeBots: 2,
                dailyPnL: 500,
                weeklyPnL: 500,
                monthlyPnL: 500,
                bots: [
                    { instanceId: 'demo-bot-1', balance: 4100, pnl: 1150, openTrades: 1 },
                    { instanceId: 'demo-bot-2', balance: 4100, pnl: 1150, openTrades: 2 }
                ],
                riskMetrics: { portfolioRisk: 80, maxDrawdown: 300, sharpeRatio: 1.8 },
                sessionInfo: { wsConnected: true, userAgent: 'Demo-Client', ipAddress: '127.0.0.1' }
            },
            {
                timestamp: Date.now(),
                portfolioValue: 11000,
                totalBalance: 8500,
                totalPnL: 2500,
                botCount: 2,
                activeBots: 2,
                dailyPnL: 500,
                weeklyPnL: 1000,
                monthlyPnL: 1000,
                bots: [
                    { instanceId: 'demo-bot-1', balance: 4250, pnl: 1250, openTrades: 2 },
                    { instanceId: 'demo-bot-2', balance: 4250, pnl: 1250, openTrades: 1 }
                ],
                riskMetrics: { portfolioRisk: 75, maxDrawdown: 200, sharpeRatio: 2.0 },
                sessionInfo: { wsConnected: true, userAgent: 'Demo-Client', ipAddress: '127.0.0.1' }
            }
        ],
        lastUpdated: Date.now(),
        version: '1.0'
    };

    const snapshotFile = path.join(demoDir, 'portfolio_snapshots.json');
    await fs.writeJson(snapshotFile, demoSnapshot, { spaces: 2 });

    console.log(`✅ Demo snapshot created at: ${snapshotFile}`);
    console.log(`📊 Contains ${demoSnapshot.snapshots.length} snapshots showing portfolio growth from $10,000 to $11,000`);
    console.log(`\nView with: cat ${snapshotFile} | jq .`);
    console.log(`Remove with: rm -rf ${path.dirname(demoDir)}`);
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    const tester = new SimplePortfolioTester();

    if (args.includes('--help')) {
        console.log('Portfolio Snapshot Testing Tool\n');
        console.log('Usage: node test-portfolio-simple.js [options]\n');
        console.log('Options:');
        console.log('  --quick       Run quick verification tests');
        console.log('  --websocket   Show WebSocket testing instructions');
        console.log('  --demo        Create demo snapshot file for testing');
        console.log('  --commands    Show useful commands');
        console.log('  --troubleshoot Show troubleshooting tips');
        console.log('  --help        Show this help');
        return;
    }

    if (args.includes('--demo')) {
        await createDemoSnapshot();
        return;
    }

    if (args.includes('--websocket')) {
        await tester.testWebSocketConnection();
        return;
    }

    if (args.includes('--commands')) {
        tester.showQuickCommands();
        return;
    }

    if (args.includes('--troubleshoot')) {
        tester.showTroubleshootingTips();
        return;
    }

    // Default: run quick tests
    await tester.runQuickTests();
    tester.showTroubleshootingTips();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SimplePortfolioTester;

# Strategy Management API Documentation

## Overview

The Bot Manager API now includes comprehensive strategy management endpoints that allow users to:
- List all available trading strategies
- View current strategy for any bot
- Change bot strategies dynamically with automatic restart

## Available Endpoints

### 1. GET /api/strategies
**Purpose**: List all available trading strategies

**Authentication**: Required (Bearer token)

**Response Format**:
```json
{
  "success": true,
  "strategies": [
    {
      "name": "EmaRsiStrategy",
      "className": "EmaRsiStrategy", 
      "description": "Basic EMA Crossover Strategy with RSI Filter...",
      "fileName": "EmaRsiStrategy.py"
    },
    {
      "name": "HighFrequencyStrategy",
      "className": "HighFrequencyStrategy",
      "description": "HighFrequencyStrategy trading strategy",
      "fileName": "HighFrequencyStrategy.py"
    }
  ]
}
```

**Usage Example**:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3001/api/strategies
```

### 2. GET /api/bots/:instanceId/strategy
**Purpose**: Get current strategy for a specific bot

**Authentication**: Required (Bear<PERSON> token + bot ownership)

**Parameters**:
- `instanceId` (path): The bot instance ID

**Response Format**:
```json
{
  "success": true,
  "strategy": {
    "current": "EmaRsiStrategy",
    "instanceId": "bot-12345"
  }
}
```

**Usage Example**:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3001/api/bots/bot-12345/strategy
```

### 3. PUT /api/bots/:instanceId/strategy
**Purpose**: Update strategy for a bot and restart it

**Authentication**: Required (Bearer token + bot ownership)

**Parameters**:
- `instanceId` (path): The bot instance ID
- `strategy` (body): The new strategy name

**Request Body**:
```json
{
  "strategy": "HighFrequencyStrategy"
}
```

**Response Format**:
```json
{
  "success": true,
  "message": "Strategy updated to HighFrequencyStrategy and bot restarted",
  "strategy": {
    "previous": "EmaRsiStrategy",
    "current": "HighFrequencyStrategy", 
    "restarted": true
  }
}
```

**Usage Example**:
```bash
curl -X PUT \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"strategy": "HighFrequencyStrategy"}' \
     http://localhost:3001/api/bots/bot-12345/strategy
```

## Available Strategies

Based on the `/root/Crypto-Pilot-Freqtrade/Admin Strategies/` directory:

1. **AggressiveSophisticated1m** - Advanced 1-minute timeframe strategy with multi-indicator confluence
2. **EmaRsiStrategy** - Basic EMA crossover with RSI filter (default)
3. **ExtremeStrategy** - High-risk, high-reward aggressive strategy 
4. **HighFrequencyScalp1m** - High-frequency 1-minute scalping strategy
5. **HighFrequencyStrategy** - High-frequency trading strategy
6. **balancedStrat** - Balanced risk/reward strategy

## Frontend Integration Guide

### 1. Strategy Selector Component
```javascript
// Fetch available strategies
const fetchStrategies = async () => {
  const response = await fetch('/api/strategies', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const data = await response.json();
  return data.strategies;
};

// Get current bot strategy
const getCurrentStrategy = async (botId) => {
  const response = await fetch(`/api/bots/${botId}/strategy`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const data = await response.json();
  return data.strategy.current;
};

// Update bot strategy
const updateStrategy = async (botId, newStrategy) => {
  const response = await fetch(`/api/bots/${botId}/strategy`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ strategy: newStrategy })
  });
  return await response.json();
};
```

### 2. Example React Component
```jsx
const StrategySelector = ({ bot }) => {
  const [strategies, setStrategies] = useState([]);
  const [currentStrategy, setCurrentStrategy] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Load strategies and current strategy
    Promise.all([
      fetchStrategies(),
      getCurrentStrategy(bot.instanceId)
    ]).then(([strategiesData, current]) => {
      setStrategies(strategiesData);
      setCurrentStrategy(current);
    });
  }, [bot.instanceId]);

  const handleStrategyChange = async (newStrategy) => {
    setLoading(true);
    try {
      const result = await updateStrategy(bot.instanceId, newStrategy);
      if (result.success) {
        setCurrentStrategy(newStrategy);
        alert('Strategy updated successfully!');
      }
    } catch (error) {
      alert('Failed to update strategy: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="strategy-selector">
      <label>Trading Strategy:</label>
      <select 
        value={currentStrategy} 
        onChange={(e) => handleStrategyChange(e.target.value)}
        disabled={loading}
      >
        {strategies.map(strategy => (
          <option key={strategy.name} value={strategy.name}>
            {strategy.name}
          </option>
        ))}
      </select>
      {loading && <span>Updating strategy...</span>}
    </div>
  );
};
```

## Implementation Details

### Security Features
- All endpoints require valid JWT authentication
- Bot strategy endpoints include ownership verification
- Strategy files are validated before application
- Rate limiting exemption for strategy endpoints to prevent UI lag

### Automatic Behavior
- Strategy updates automatically copy the new strategy file to the bot's directory
- Bot configuration is updated atomically to prevent corruption  
- If the bot is running, it will be automatically restarted to apply the new strategy
- If the bot is stopped, the strategy is updated but no restart occurs

### Error Handling
- Invalid strategy names are rejected with 400 Bad Request
- Non-existent bots return 404 Not Found
- Missing authentication returns 401 Unauthorized
- Bot ownership violations return 403 Forbidden

### File Operations
- Strategy files are copied from `/root/Crypto-Pilot-Freqtrade/Admin Strategies/` 
- Target location: `{botDirectory}/user_data/strategies/{strategyName}.py`
- Configuration updates use atomic writes to prevent corruption
- Original strategy files are preserved as backups

## Testing

Use the provided test script `/root/test-strategy-api.sh` to verify all endpoints work correctly.

## Rate Limiting

Strategy management endpoints are exempt from rate limiting to ensure smooth UI experience when users are browsing and changing strategies frequently.

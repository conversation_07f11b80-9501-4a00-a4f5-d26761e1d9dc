<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Pilot - Bot Manager Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #007bff;
            text-align: center;
        }
        .test-link {
            display: block;
            padding: 15px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
            font-size: 16px;
        }
        .test-link:hover {
            background-color: #0056b3;
        }
        .description {
            color: #6c757d;
            margin: 10px 0;
            font-size: 14px;
        }
        .api-endpoint {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 Crypto Pilot Bot Manager Test Suite</h1>
    
    <div class="container">
        <h2>Test Clients</h2>
        
        <a href="/test-streaming-client.html" class="test-link">
            🌊 Live Streaming SSE Test Client
        </a>
        <div class="description">
            Comprehensive real-time streaming client with portfolio metrics, chart visualization, 
            live positions tracking, and security data streams.
        </div>
        
        <h3>Features:</h3>
        <ul>
            <li>📊 Real-time portfolio metrics and P&L tracking</li>
            <li>📈 Interactive portfolio value chart</li>
            <li>💼 Live trading positions with profit/loss indicators</li>
            <li>🔒 Security price data streams</li>
            <li>🔌 Connection management and error handling</li>
        </ul>
    </div>
    
    <div class="container">
        <h2>API Endpoints</h2>
        
        <h3>Streaming</h3>
        <div class="api-endpoint">GET /api/stream?token={jwt_token}</div>
        <div class="description">Server-Sent Events (SSE) stream for real-time data</div>
        
        <h3>Bot Management</h3>
        <div class="api-endpoint">GET /api/bots</div>
        <div class="description">List all bots for authenticated user</div>
        
        <div class="api-endpoint">GET /api/bots/{instanceId}</div>
        <div class="description">Get individual bot details</div>
        
        <div class="api-endpoint">POST /api/bots/{instanceId}/stop</div>
        <div class="description">Stop a running bot</div>
        
        <div class="api-endpoint">POST /api/bots/{instanceId}/start</div>
        <div class="description">Start a stopped bot</div>
        
        <div class="api-endpoint">DELETE /api/bots/{instanceId}</div>
        <div class="description">Delete a bot completely</div>
        
        <h3>Bot Provisioning</h3>
        <div class="api-endpoint">POST /api/provision</div>
        <div class="description">Create a new bot instance</div>
        
        <h3>Health Check</h3>
        <div class="api-endpoint">GET /api/health</div>
        <div class="description">Service health status</div>
    </div>
    
    <div class="container">
        <h2>Authentication</h2>
        <p>All endpoints require authentication via:</p>
        <ul>
            <li><strong>Authorization Header:</strong> <code>Bearer {jwt_token}</code></li>
            <li><strong>Query Parameter:</strong> <code>?token={jwt_token}</code> (for EventSource)</li>
        </ul>
        <p>Use the "Load Test Token" button in the streaming client for quick testing.</p>
    </div>
</body>
</html>

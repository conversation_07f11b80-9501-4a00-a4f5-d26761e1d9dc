#! /bin/bash

# ta_regtest - temporary wrapper script for .libs/ta_regtest
# Generated by ltmain.sh - GNU libtool 1.5.22 Debian 1.5.22-4 (1.1220.2.365 2005/12/18 22:14:06)
#
# The ta_regtest program cannot be directly executed until all the libtool
# libraries that it depends on are installed.
#
# This wrapper script should never be moved out of the build directory.
# If it is, it will not operate correctly.

# Sed substitution that helps us do robust quoting.  It backslashifies
# metacharacters that are still active within double-quoted strings.
Xsed='/usr/bin/sed -e 1s/^X//'
sed_quote_subst='s/\([\\`\\"$\\\\]\)/\\\1/g'

# The HP-UX ksh and POSIX shell print the target directory to stdout
# if CDPATH is set.
(unset CDPATH) >/dev/null 2>&1 && unset CDPATH

relink_command="(cd /root/ta-lib/src/tools/ta_regtest; { test -z \"\${LIBRARY_PATH+set}\" || unset LIBRARY_PATH || { LIBRARY_PATH=; export LIBRARY_PATH; }; }; { test -z \"\${COMPILER_PATH+set}\" || unset COMPILER_PATH || { COMPILER_PATH=; export COMPILER_PATH; }; }; { test -z \"\${GCC_EXEC_PREFIX+set}\" || unset GCC_EXEC_PREFIX || { GCC_EXEC_PREFIX=; export GCC_EXEC_PREFIX; }; }; { test -z \"\${LD_RUN_PATH+set}\" || unset LD_RUN_PATH || { LD_RUN_PATH=; export LD_RUN_PATH; }; }; { test -z \"\${LD_LIBRARY_PATH+set}\" || unset LD_LIBRARY_PATH || { LD_LIBRARY_PATH=; export LD_LIBRARY_PATH; }; }; PATH=\"/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\"; export PATH; gcc -g -O2 -o \$progdir/\$file ta_regtest-ta_regtest.o ta_regtest-test_data.o ta_regtest-test_util.o ta_regtest-test_abstract.o ta_regtest-test_adx.o ta_regtest-test_mom.o ta_regtest-test_sar.o ta_regtest-test_rsi.o ta_regtest-test_candlestick.o ta_regtest-test_per_ema.o ta_regtest-test_per_hlc.o ta_regtest-test_stoch.o ta_regtest-test_macd.o ta_regtest-test_minmax.o ta_regtest-test_per_hlcv.o ta_regtest-test_1in_1out.o ta_regtest-test_1in_2out.o ta_regtest-test_per_ohlc.o ta_regtest-test_stddev.o ta_regtest-test_bbands.o ta_regtest-test_ma.o ta_regtest-test_po.o ta_regtest-test_per_hl.o ta_regtest-test_trange.o ta_regtest-test_internals.o  -L/root/ta-lib/src /root/ta-lib/src/.libs/libta_lib.so -lm -lpthread -ldl -Wl,--rpath -Wl,/root/ta-lib/src/.libs)"

# This environment variable determines our operation mode.
if test "$libtool_install_magic" = "%%%MAGIC variable%%%"; then
  # install mode needs the following variable:
  notinst_deplibs=' /root/ta-lib/src/libta_lib.la'
else
  # When we are sourced in execute mode, $file and $echo are already set.
  if test "$libtool_execute_magic" != "%%%MAGIC variable%%%"; then
    echo="echo"
    file="$0"
    # Make sure echo works.
    if test "X$1" = X--no-reexec; then
      # Discard the --no-reexec flag, and continue.
      shift
    elif test "X`($echo '\t') 2>/dev/null`" = 'X\t'; then
      # Yippee, $echo works!
      :
    else
      # Restart under the correct shell, and then maybe $echo will work.
      exec /bin/bash "$0" --no-reexec ${1+"$@"}
    fi
  fi

  # Find the directory that this script lives in.
  thisdir=`$echo "X$file" | $Xsed -e 's%/[^/]*$%%'`
  test "x$thisdir" = "x$file" && thisdir=.

  # Follow symbolic links until we get to the real thisdir.
  file=`ls -ld "$file" | /usr/bin/sed -n 's/.*-> //p'`
  while test -n "$file"; do
    destdir=`$echo "X$file" | $Xsed -e 's%/[^/]*$%%'`

    # If there was a directory component, then change thisdir.
    if test "x$destdir" != "x$file"; then
      case "$destdir" in
      [\\/]* | [A-Za-z]:[\\/]*) thisdir="$destdir" ;;
      *) thisdir="$thisdir/$destdir" ;;
      esac
    fi

    file=`$echo "X$file" | $Xsed -e 's%^.*/%%'`
    file=`ls -ld "$thisdir/$file" | /usr/bin/sed -n 's/.*-> //p'`
  done

  # Try to get the absolute directory name.
  absdir=`cd "$thisdir" && pwd`
  test -n "$absdir" && thisdir="$absdir"

  program=lt-'ta_regtest'
  progdir="$thisdir/.libs"

  if test ! -f "$progdir/$program" || \
     { file=`ls -1dt "$progdir/$program" "$progdir/../$program" 2>/dev/null | /usr/bin/sed 1q`; \
       test "X$file" != "X$progdir/$program"; }; then

    file="$$-$program"

    if test ! -d "$progdir"; then
      mkdir "$progdir"
    else
      rm -f "$progdir/$file"
    fi

    # relink executable if necessary
    if test -n "$relink_command"; then
      if relink_command_output=`eval $relink_command 2>&1`; then :
      else
	echo "$relink_command_output" >&2
	rm -f "$progdir/$file"
	exit 1
      fi
    fi

    mv -f "$progdir/$file" "$progdir/$program" 2>/dev/null ||
    { rm -f "$progdir/$program";
      mv -f "$progdir/$file" "$progdir/$program"; }
    rm -f "$progdir/$file"
  fi

  if test -f "$progdir/$program"; then
    if test "$libtool_execute_magic" != "%%%MAGIC variable%%%"; then
      # Run the actual program with our arguments.

      exec "$progdir/$program" ${1+"$@"}

      $echo "$0: cannot exec $program ${1+"$@"}"
      exit 1
    fi
  else
    # The program doesn't exist.
    $echo "$0: error: \`$progdir/$program' does not exist" 1>&2
    $echo "This script is just a wrapper for $program." 1>&2
    echo "See the libtool documentation for more information." 1>&2
    exit 1
  fi
fi
